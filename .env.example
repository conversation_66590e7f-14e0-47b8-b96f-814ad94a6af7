APP_NAME=Yosr
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=https://app.yosr.io

APP_LOCALE=en
APP_FALLBACK_LOCALE=en

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

# Log
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=

# Config
BROADCAST_CONNECTION=ably
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis
CACHE_STORE=redis
CACHE_PREFIX=

# Redis
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mailersend
MAILERSEND_API_KEY=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Yosr
MAIL_MAILER=log

# ABLY
ABLY_KEY=

# Sentry
SENTRY_DSN=

# Slack
SLACK_WEBHOOK_URL=

# Here
HERE_API_KEY=

# Newrelic
NEWRELIC_API_KEY=

# Mixpanel
MIXPANEL_TOKEN=

# Mapbox
MAPBOX_ACCESS_TOKEN=
MAPBOX_PUBLIC_ACCESS_TOKEN=

# OneSignal
ONESIGNAL_API_KEY=
ONESIGNAL_APP_ID=


# --FRONTEND--
VITE_APP_ENV="${APP_ENV}"
VITE_APP_NAME="${APP_NAME}"
VITE_MAPBOX_TOKEN="${MAPBOX_PUBLIC_ACCESS_TOKEN}"
VITE_MIXPANEL_TOKEN="${MIXPANEL_TOKEN}"
VITE_SENTRY_DSN="${SENTRY_DSN}"