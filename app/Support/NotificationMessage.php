<?php

namespace App\Support;

use App\Enums\General\Locale;
use App\Services\General\ImagePreviewService;

class NotificationMessage
{
    private array $parameters;

    public function __construct(
        public string $notificationKey,
        array $parameters = [],
    ) {
        if (isset($parameters['names'])) {
            $parameters['names'] = $this->getFormatedNames($parameters['names'], $parameters['locale']);
        }
        if (isset($parameters['ride_mode'])) {
            $parameters['ride_mode'] = __(
                'general.enums.'.$parameters['ride_mode'], [], $this->getLocale()
            );
        }
        $this->parameters = $parameters;
    }

    public function toArray(): array
    {
        return [
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
        ];
    }

    public function getTitle(?string $locale = null): string
    {
        return ucfirst(
            strtolower(
                __(
                    'notification.'.$this->notificationKey.'.'.$this->getParam('user_type').'.title',
                    $this->parameters,
                    locale: $locale ?? $this->getLocale()
                )
            )
        );
    }

    public function getBody(?string $locale = null): string
    {
        return ucfirst(
            strtolower(
                __(
                    'notification.'.$this->notificationKey.'.'.$this->getParam('user_type').'.body',
                    $this->parameters,
                    locale: $locale ?? $this->getLocale()
                )
            )
        );
    }

    public function getParam(string $key): string
    {
        return isset($this->parameters[$key]) ? strtolower($this->parameters[$key]) : '';
    }

    public function getLocale(): string
    {
        return $this->parameters['locale'] ?? Locale::English;
    }

    public function getFormatedNames(array $names, string $locale = Locale::English): string
    {
        $count = count($names);

        if ($count === 0) {
            return '';
        }

        if ($count === 1) {
            return $names[0];
        }

        $conjunctions = [
            Locale::English => 'and',
            Locale::Arabic => 'و',
            Locale::French => 'et',
        ];

        $allButLast = array_slice(
            array_map(fn ($name) => ucfirst($name), $names),
            0,
            -1
        );
        $last = $names[$count - 1];

        return implode(', ', $allButLast).' '.$conjunctions[$locale].' '.$last;
    }

    public function toSlackMessage(array $to): string
    {
        return 'Notification Sent:'
            ."\n *Title:* ".$this->getTitle()
            ."\n *Body:* ".$this->getBody()
            ."\n *To:* ".implode(' - ', $to);
    }

    public function getImage(): ?string
    {
        return app(ImagePreviewService::class)->getImageForNotification($this->notificationKey, $this->parameters);
    }
}
