<?php

namespace App\Notifications\Channels;

use App\Facades\Notification\Slack;
use App\Support\NotificationMessage;
use Illuminate\Notifications\Notification;

class SlackNotificationChannel
{
    public function send(object $notifiable, Notification $notification): void
    {
        /** @var NotificationMessage $notificationMessage */
        $notificationMessage = $notification->toMessage($notifiable);

        Slack::send($notificationMessage->toSlackMessage([
            $notifiable->getOneSignalKey(),
        ]));
    }
}
