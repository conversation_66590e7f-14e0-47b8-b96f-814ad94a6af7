<?php

namespace App\Notifications\Transport;

use App\Support\NotificationMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class ResponsibleNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public readonly string $type,
        public readonly array $attributes,
    ) {}

    public function via(object $notifiable): array
    {
        return $notifiable->getNotificationChannels();
    }

    public function toMessage(object $notifiable): NotificationMessage
    {
        return new NotificationMessage($this->type, $this->attributes);
    }
}
