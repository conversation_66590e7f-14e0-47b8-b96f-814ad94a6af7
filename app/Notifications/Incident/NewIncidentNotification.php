<?php

namespace App\Notifications\Incident;

use App\Enums\Notification\NotificationType;
use App\Models\Incident\Incident;
use App\Support\NotificationMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Notification;

class NewIncidentNotification extends Notification implements ShouldBroadcast
{
    use Queueable;

    public function __construct(public readonly int $incidentId) {}

    public function via(object $notifiable): array
    {
        return $notifiable->getNotificationChannels();
    }

    public function toMessage(object $notifiable): NotificationMessage
    {
        $incident = Incident::find($this->incidentId);

        return new NotificationMessage(
            NotificationType::NewIncident.'.'.$incident->type,
            ['locale' => $notifiable?->locale]
        );
    }
}
