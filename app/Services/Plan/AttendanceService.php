<?php

namespace App\Services\Plan;

use App\Enums\General\Models;
use App\Enums\Plan\AttendanceStatus;
use App\Models\Plan\Attendance;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Models\Transport\Stop;
use App\Repositories\Plan\AttendanceRepository;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

readonly class AttendanceService
{
    public function __construct(
        private AttendanceRepository $attendanceRepository,
        private AbsenceService $absenceService,
    ) {}

    public function list(int $companyId, array $filters): LengthAwarePaginator
    {
        return Attendance::with([
            'passenger:id,first_name,last_name,status',
            'creator:id,first_name,last_name,status,company_id',
        ])
            ->company($companyId)
            ->filter($filters)
            ->latest()
            ->paginate()
            ->withQueryString();
    }

    public function store(array $data): Attendance
    {
        return $this->attendanceRepository->create($data);
    }

    public function updateOrCreate(array $fields, array $data): Attendance
    {
        return $this->attendanceRepository->updateOrCreate($fields, $data);
    }

    public function getAbsencesForDate(Schedule $schedule, Carbon $date): Collection
    {
        return $schedule
            ->attendances()
            ->whereDate('date', $date)
            ->whereIn('status', [AttendanceStatus::Absent, AttendanceStatus::Canceled])
            ->get();
    }

    public function unsetAbsentPassengers(Schedule &$schedule, Carbon $date): void
    {
        $attendances = $this->getAbsencesForDate($schedule, $date);

        if ($attendances->isEmpty()) {
            return;
        }

        $schedule->route->stations = $schedule->route->stations->filter(function ($station) use ($attendances) {
            $absentPassengersCount = 0;
            $totalPassengers = $station->passengers->count();

            $station->passengers = $station->passengers->filter(function ($passenger) use ($attendances, &$absentPassengersCount) {
                $attendance = $attendances->where('passenger_id', $passenger->id)->first();
                if ($attendance) {
                    $absentPassengersCount++;

                    return false;
                }

                return true;
            });

            // Remove the entire station if all passengers are absent
            return $absentPassengersCount < $totalPassengers;
        });
    }

    public function getItems(Passenger $passenger, array $filters = []): Collection
    {
        $attendances = $passenger
            ->attendances()
            ->whereHas('ride')
            ->with([
                'ride.vehicle',
                'ride.route.destination',
                'ride.driver',
            ])
            ->get();

        return $attendances->map(function (Attendance $attendance) {
            return [
                'id' => $attendance->id,
                'status' => $attendance->status,
                'date' => $attendance->date->format('d M Y'),
                'pickup_time' => $attendance->created_at->format('H:i'),
                'arrival_time' => $attendance->ride->arrived_at->format('H:i'),
                'destination' => $attendance->ride->route->destination->name,
                'driver' => [
                    'name' => $attendance->ride->driver->name,
                    'image' => $attendance->ride->driver->image,
                ],
                'vehicle' => [
                    'name' => $attendance->ride->vehicle->name,
                    'plate' => $attendance->ride->vehicle->plate,
                ],
            ];
        });
    }

    public function saveStopAttendances(Stop $stop, array $passengerIds, Carbon $date, array $attendantIds = []): void
    {
        foreach ($passengerIds as $passengerId) {
            $scheduleId = $stop->ride->schedule_id;

            if ($absentAttendance = $this->existingAttendance($scheduleId, $passengerId, $date)) {
                $this->attendanceRepository->update($absentAttendance, [
                    'ride_id' => $stop->ride_id,
                ]);

                return;
            }

            if ($this->absenceService->isAbsent($passengerId, $date)) {
                $status = AttendanceStatus::Absent;
            } else {
                $status = in_array($passengerId, $attendantIds)
                    ? AttendanceStatus::Present
                    : AttendanceStatus::NoShow;
            }

            $this->attendanceRepository->updateOrCreate([
                'passenger_id' => $passengerId,
                'schedule_id' => $scheduleId,
                'date' => $date,
                'company_id' => $stop->company_id,
            ], [
                'ride_id' => $stop->ride_id,
                'status' => $status,
                'creator_id' => $stop->ride->driver_id,
                'creator_type' => Models::Driver,
            ]);
        }
    }

    public function existingAttendance(int $scheduleId, int $passengerId, Carbon $date): ?Attendance
    {
        return Attendance::where('schedule_id', $scheduleId)
            ->where('passenger_id', $passengerId)
            ->whereDate('date', $date)
            ->first();
    }

    public function delete(Attendance $attendance): ?bool
    {
        return $this->attendanceRepository->delete($attendance);
    }

    public function absentAttendancesForStop(Stop $stop): Collection
    {
        return $stop
            ->ride
            ->schedule
            ->attendances()
            ->whereDate('date', $stop->scheduled_at)
            ->whereIn('status', [AttendanceStatus::Absent, AttendanceStatus::Canceled])
            ->get();
    }

    public function absenceForRide(Ride $ride): Collection
    {
        return $ride
            ->schedule
            ->attendances()
            ->whereDate('date', $ride->started_at)
            ->whereIn('status', [AttendanceStatus::Absent, AttendanceStatus::Canceled])
            ->get();
    }

    public function presentForRide(Ride $ride): Collection
    {
        return $ride
            ->schedule
            ->attendances()
            ->whereDate('date', $ride->started_at)
            ->where('status', AttendanceStatus::Present)
            ->get();
    }

    public function getNoShowPassengers(Collection $passengers, array $attendantIds = []): Collection
    {
        return $passengers->filter(function ($passenger) use ($attendantIds) {
            return ! in_array($passenger->id, $attendantIds);
        });
    }

    public function getPresentPassengers(Collection $passengers, array $attendantIds = []): Collection
    {
        return $passengers->filter(function ($passenger) use ($attendantIds) {
            return in_array($passenger->id, $attendantIds);
        });
    }

    public function markIgnoredPassengersByStations(Ride $ride, Collection $expectedPassengers, array $stationIds, array $finishedIds): Collection
    {
        $skippedStations = $ride
            ->route
            ->stations()
            ->with('passengers')
            ->whereNotIn('id', array_merge($stationIds, $finishedIds))
            ->get();

        if ($skippedStations->isEmpty()) {
            return collect();
        }

        $expectedPassengerIds = $expectedPassengers->pluck('id')->toArray();
        $skippedPassengers = collect();

        $skippedStations->each(function ($station) use ($expectedPassengerIds, $ride, $skippedPassengers) {
            $station->passengers->each(function ($passenger) use ($expectedPassengerIds, $ride, $skippedPassengers) {
                if (in_array($passenger->id, $expectedPassengerIds)) {

                    $skippedPassengers->push($passenger);

                    $this->attendanceRepository->updateOrCreate([
                        'passenger_id' => $passenger->id,
                        'schedule_id' => $ride->schedule_id,
                        'company_id' => $passenger->company_id,
                        'date' => today(),
                    ], [
                        'ride_id' => $ride->id,
                        'status' => AttendanceStatus::Ignored,
                        'creator_type' => Models::Driver,
                        'creator_id' => $ride->driver_id,
                    ]);
                }
            });
        });

        return $skippedPassengers;
    }

    public function isPassengerAbsentForRide(Ride $ride, Passenger $passenger, Carbon $date): bool
    {
        return Attendance::whereIn('status', [
            AttendanceStatus::Absent,
            AttendanceStatus::Canceled,
            AttendanceStatus::Skipped,
            AttendanceStatus::Ignored,
        ])->whereDate('date', $date)
            ->where('schedule_id', $ride->schedule_id)
            ->where('passenger_id', $passenger->id)
            ->exists();
    }

    public function getExpectedPassengersForStop(Stop $stop): Collection
    {
        $absentPassengerIds = $this->getAllAbsentPassengersForStop($stop);

        return $stop->station
            ->passengers()
            ->with('responsibles')
            ->whereNotIn('id', $absentPassengerIds)
            ->get();
    }

    public function getAllAbsentPassengersForStop(Stop $stop): Collection
    {
        $absentPassengerIds = $this->absenceService->getAbsentPassengersForStop($stop);

        $absentAttendancesPassengerIds = $this->getAbsentPassengersForStop($stop);

        return $absentPassengerIds->concat($absentAttendancesPassengerIds->toArray())->unique();
    }

    public function getAbsentPassengersForStop(Stop $stop): Collection
    {
        return $stop
            ->ride
            ->schedule
            ->attendances()
            ->whereDate('date', $stop->scheduled_at)
            ->whereIn('status', [AttendanceStatus::Absent, AttendanceStatus::Canceled])
            ->pluck('passenger_id');
    }

    public function getDefaultExpectedAttendanceForPassenger(Passenger $passenger, Schedule $schedule, Carbon $day): Attendance
    {
        $attendance = new Attendance;
        $attendance->status = AttendanceStatus::Expected;
        $attendance->date = $day;
        $attendance->passenger_id = $passenger->id;
        $attendance->id = 0;
        $attendance->schedule_id = $schedule->id;

        return $attendance;
    }
}
