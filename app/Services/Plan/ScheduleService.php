<?php

namespace App\Services\Plan;

use App\Enums\General\Models;
use App\Enums\Plan\PlanStatus;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Repositories\Plan\ScheduleRepository;
use App\Services\Transport\RideService;
use App\Services\Transport\RouteService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

readonly class ScheduleService
{
    public function __construct(
        private ScheduleRepository $scheduleRepository,
        private RouteService $routeService,
        private RideService $rideService,
        private AttendanceService $attendanceService,
    ) {}

    public function create(array $attributes): Schedule
    {
        $route = $this->routeService->find($attributes['route_id']);
        $routeDuration = $this->routeService->getDuration($route);

        if (isset($attributes['arrival_time'])) {
            $attributes['start_time'] = Carbon::parse($attributes['arrival_time'])->subSeconds($routeDuration)->format('H:i:s');
        } elseif (isset($attributes['start_time'])) {
            $attributes['arrival_time'] = Carbon::parse($attributes['start_time'])->addSeconds($routeDuration)->format('H:i:s');
        }

        return $this->scheduleRepository->create($attributes);
    }

    public function list(Driver|Passenger|Assistant $user, Carbon $day): Collection
    {
        if ($user->isDriver()) {
            $field = 'plan_id';
            $values = $user->plans()->pluck('id');
        }

        if ($user->isPassenger()) {
            $field = 'route_id';
            $values = $user->routes()->pluck('id');
        }

        if ($user->isAssistant()) {
            $field = 'assistant_id';
            $values = [$user->id];
        }

        $fields = [
            'route',
            'route.origin:id,name,street_line_1',
            'route.destination:id,name,street_line_1',
            'vehicle:id,plate,type,name',
            'driver',
            'rides' => function ($query) use ($day) {
                return $query->whereDate('started_at', $day);
            },
        ];

        return Schedule::whereHas('plan', function ($query) {
            return $query->where('status', PlanStatus::Active);
        })->with($fields)
            ->where('start_date', '<=', $day)
            ->where('end_date', '>=', $day)
            ->whereIn($field, $values)
            ->forDate($day)
            ->get();
    }

    public function get(Schedule $schedule, Driver|Passenger|Assistant $user, Carbon $day): Schedule
    {
        $fields = [
            'vehicle',
            'driver',
            'assistant',
            'route.origin',
            'route.destination',
            'rides' => function ($query) use ($day) {
                return $query->whereDate('started_at', $day)->latest();
            },
        ];

        switch ($user->type()) {
            case Models::Passenger:
                $fields['attendances'] = function ($query) use ($user, $day) {
                    return $query->where('passenger_id', $user->id)->whereDate('date', $day);
                };
                break;
            case Models::Driver:
                if ($ongoingRide = $this->rideService->getOngoingRideForSchedule($schedule, today())) {
                    $finishedStationIds = $this->rideService->getFinishedStationIds($ongoingRide);

                    $fields = [
                        ...$fields,
                        'route.stations' => function ($query) use ($finishedStationIds) {
                            return $query->whereNotIn('id', $finishedStationIds)->with([
                                'location',
                                'passengers',
                            ]);
                        },
                        'route.stations.location',
                        'route.stations.passengers',
                    ];
                    break;
                }
            default:
                $fields = [
                    ...$fields,
                    'route.stations.location',
                    'route.stations.passengers',
                ];
        }

        $schedule->load($fields);

        return $schedule;
    }

    public function prepareForPassenger(?Schedule $schedule, Passenger $passenger, Carbon $day): void
    {
        $schedule->passenger_attendance = $schedule->attendances[0]
            ?? $this->attendanceService->getDefaultExpectedAttendanceForPassenger($passenger, $schedule, $day);

        $schedule->start_time = $this->getStartTimeForPassenger($passenger, $schedule, $day);
    }

    public function getStartTimeForPassenger(Passenger $passenger, Schedule $schedule, Carbon $day): Carbon
    {
        $stationPassenger = $schedule
            ->route
            ->stationPassengers()
            ->where('passenger_id', $passenger->id)
            ->with('station')
            ->first();

        if (! $stationPassenger) {
            return $schedule->start_time;
        }

        return $schedule->start_time->addSeconds($stationPassenger->station->duration);
    }
}
