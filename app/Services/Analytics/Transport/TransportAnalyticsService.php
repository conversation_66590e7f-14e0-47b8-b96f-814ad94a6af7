<?php

namespace App\Services\Analytics\Transport;

use App\Enums\Transport\RideStatus;
use App\Models\Company\Company;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class TransportAnalyticsService
{
    private function applyDefaultFilters(?array $filters): array
    {
        return [
            'from' => $filters['from'] ?? Carbon::now()->subDays(30),
            'to' => $filters['to'] ?? Carbon::now(),
            'group_by' => $filters['group_by'] ?? 'monthly',
        ];
    }

    private function getGroupByFormat(string $groupBy): string
    {
        return match ($groupBy) {
            'daily' => 'Y-m-d',
            'weekly' => 'o-W',
            default => 'Y-M',
        };
    }

    private function extractDisplayPeriod(string $period, string $groupBy): string
    {
        return match ($groupBy) {
            'daily' => Carbon::createFromFormat('Y-m-d', $period)->format('D j, M'),
            'weekly' => (function () use ($period) {
                [$year, $week] = explode('-', $period);

                return (new \DateTime)->setISODate($year, $week)->format('M j');
            })(),
            default => Carbon::createFromFormat('Y-M', $period)->format('M Y'),
        };
    }

    private function getPreviousPeriodDates(array $filters): array
    {
        $from = Carbon::parse($filters['from'])->startOfDay();
        $to = Carbon::parse($filters['to'])->startOfDay();
        $diff = $from->diffInDays($to);

        return [
            $from->copy()->subDays($diff)->startOfDay(),
            $to->copy()->subDays($diff)->startOfDay(),
        ];
    }

    private function calculatePercentageChange($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    private function metersToKilometers($meters): float
    {
        return round($meters / 1000, 2);
    }

    private function getBaseRidesQuery(Company $company, array $filters)
    {
        return Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']]);
    }

    public function getTotalRides(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        $currentCount = $this->getBaseRidesQuery($company, $filters)->count();

        [$previousFrom, $previousTo] = $this->getPreviousPeriodDates($filters);
        $previousCount = Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$previousFrom, $previousTo])
            ->count();

        return [
            'value' => $currentCount,
            'percentage_change' => $this->calculatePercentageChange($currentCount, $previousCount),
        ];
    }

    public function getRideDurationMetrics(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);
        $groupByFormat = $this->getGroupByFormat($filters['group_by']);

        $rides = $this->getBaseRidesQuery($company, $filters)
            ->with('route:id,name')
            ->whereNotNull('arrived_at')
            ->get(['id', 'route_id', 'started_at', 'arrived_at']);

        $allRoutes = $rides->pluck('route')->filter()->unique('id')->keyBy('id');

        return $rides->groupBy(fn ($ride) => $ride->started_at->format($groupByFormat))
            ->map(function ($periodRides, $period) use ($filters, $allRoutes) {
                $result = ['name' => $this->extractDisplayPeriod($period, $filters['group_by'])];
                $routeGroups = $periodRides->groupBy('route_id');

                $serieIndex = 0;
                foreach ($allRoutes as $routeId => $route) {
                    $routeRides = $routeGroups->get($routeId, collect());

                    $avgDuration = 0;
                    if ($routeRides->isNotEmpty()) {
                        $totalDuration = $routeRides->sum(
                            fn ($ride) => $ride->started_at->diffInMinutes($ride->arrived_at)
                        );
                        $avgDuration = $totalDuration / $routeRides->count();
                    }

                    $result["serie{$serieIndex}"] = [
                        'label' => $route->name ?? 'Unknown Route',
                        'value' => round($avgDuration, 2),
                    ];
                    $serieIndex++;
                }

                return $result;
            })
            ->values()
            ->all();
    }

    public function getRideDistanceMetrics(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);
        $groupByFormat = $this->getGroupByFormat($filters['group_by']);

        return $this->getBaseRidesQuery($company, $filters)
            ->get(['distance', 'started_at'])
            ->groupBy(fn ($ride) => $ride->started_at->format($groupByFormat))
            ->map(function ($rides, $period) use ($filters) {
                return [
                    'name' => $this->extractDisplayPeriod($period, $filters['group_by']),
                    'value' => $this->metersToKilometers($rides->sum('distance')),
                ];
            })
            ->values()
            ->all();
    }

    public function getRideStatusCounts(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        return $this->getBaseRidesQuery($company, $filters)
            ->get(['status'])
            ->countBy('status')
            ->map(fn ($count, $status) => [
                'label' => ucfirst(__('general.enums.'.$status)),
                'value' => $count,
            ])
            ->values()
            ->all();
    }

    public function getRideStatusBreakdownCounts(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        $rides = $this->getBaseRidesQuery($company, $filters)
            ->with('route:id,name')
            ->get(['id', 'route_id', 'status']);

        $groupedRides = $rides->groupBy('route_id');
        $maxCount = $groupedRides->map->count()->max() ?: 1;
        $allStatuses = RideStatus::getValues();

        return $groupedRides->map(function ($rides, $routeId) use ($allStatuses, $maxCount) {
            $routeName = $rides->first()->route?->name ?? 'Unknown Route';
            $statusCounts = $rides->countBy('status');

            return [
                'name' => $routeName,
                'indicators' => collect($allStatuses)->map(fn ($status) => [
                    'name' => __('general.enums.'.strtolower($status)),
                    'max' => $maxCount,
                ])->all(),
                'values' => collect($allStatuses)->map(fn ($status) => $statusCounts->get($status, 0))->all(),
            ];
        })->values()->all();
    }

    public function getRideStartEndTimes(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        $rides = $this->getBaseRidesQuery($company, $filters)
            ->get(['started_at', 'arrived_at']);

        $hours = array_fill(0, 24, ['start' => 0, 'end' => 0]);

        $rides->each(function ($ride) use (&$hours) {
            if ($ride->started_at) {
                $hours[Carbon::parse($ride->started_at)->hour]['start']++;
            }
            if ($ride->arrived_at) {
                $hours[Carbon::parse($ride->arrived_at)->hour]['end']++;
            }
        });

        return collect($hours)->map(function ($counts, $hour) {
            return [
                'name' => sprintf('%02d:00', $hour),
                'serie' => [
                    'label' => ucfirst(__('general.starts')),
                    'value' => $counts['start'],
                ],
                'serie_1' => [
                    'label' => ucfirst(__('general.ends')),
                    'value' => $counts['end'],
                ],
            ];
        })->all();
    }

    public function getTotalNumberOfRoutes(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        return [
            'value' => Route::where('company_id', $company->id)
                ->where('created_at', '<=', $filters['to'])
                ->count(),
        ];
    }

    public function getRidershipPerRoute(Company $company, ?array $filters): Collection
    {
        $filters = $this->applyDefaultFilters($filters);

        return Route::where('company_id', $company->id)
            ->whereBetween('created_at', [$filters['from'], $filters['to']])
            ->withCount('passengers')
            ->get(['id', 'name'])
            ->map(fn ($route) => [
                'name' => $route->name,
                'value' => $route->passengers_count,
            ]);
    }

    public function getOnTimePerformance(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);
        $groupByFormat = $this->getGroupByFormat($filters['group_by']);
        $acceptableDelayMinutes = 5;

        $rides = $this->getBaseRidesQuery($company, $filters)
            ->with('schedule:id,start_time,arrival_time')
            ->whereHas('schedule')
            ->whereNotNull('arrived_at')
            ->get(['id', 'started_at', 'arrived_at']);

        return $rides->map(function ($ride) use ($acceptableDelayMinutes, $groupByFormat) {
            $scheduledStart = Carbon::parse($ride->schedule->start_time);
            $scheduledEnd = Carbon::parse($ride->schedule->arrival_time);
            $actualStart = $ride->started_at;
            $actualEnd = $ride->arrived_at;

            return [
                'period' => $actualStart->format($groupByFormat),
                'is_start_on_time' => abs($actualStart->diffInMinutes($scheduledStart, false)) <= $acceptableDelayMinutes ? 1 : 0,
                'is_end_on_time' => abs($actualEnd->diffInMinutes($scheduledEnd, false)) <= $acceptableDelayMinutes ? 1 : 0,
            ];
        })
            ->groupBy('period')
            ->map(function ($group) use ($filters) {
                $total = $group->count();
                $startOnTime = $group->sum('is_start_on_time');
                $endOnTime = $group->sum('is_end_on_time');

                return [
                    'name' => $this->extractDisplayPeriod($group->first()['period'], $filters['group_by']),
                    'serie' => [
                        'label' => ucfirst(__('analytics.start_on_time')),
                        'value' => $total ? ($startOnTime / $total) * 100 : 0,
                    ],
                    'serie_1' => [
                        'label' => ucfirst(__('analytics.end_on_time')),
                        'value' => $total ? ($endOnTime / $total) * 100 : 0,
                    ],
                ];
            })
            ->values()
            ->all();
    }

    public function getTotalNumberOfStations(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        return [
            'value' => Station::where('company_id', $company->id)
                ->where('created_at', '<=', $filters['to'])
                ->count(),
        ];
    }

    public function getStationLocations(Company $company, ?array $filters): Collection
    {
        $filters = $this->applyDefaultFilters($filters);

        return Station::where('company_id', $company->id)
            ->with('location:id,name,lat,lng')
            ->get(['id', 'name'])
            ->map(fn ($station) => [
                'name' => $station->name ?? $station->location->name,
                'coordinates' => [
                    'lat' => $station->location->lat,
                    'lng' => $station->location->lng,
                ],
            ]);
    }

    public function getTotalNumberOfPassengers(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);
        [$previousFrom, $previousTo] = $this->getPreviousPeriodDates($filters);

        $currentCount = Passenger::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->count();

        $previousCount = Passenger::where('company_id', $company->id)
            ->where('created_at', '<=', $previousTo)
            ->count();

        return [
            'value' => $currentCount,
            'percentage_change' => $this->calculatePercentageChange($currentCount, $previousCount),
        ];
    }
}
