<?php

namespace App\Services\Analytics\Fleet;

use App\Models\Company\Company;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\Transport\Ride;
use Carbon\Carbon;

class FleetAnalyticsService
{
    private function applyDefaultFilters(?array $filters): array
    {
        $from = $filters['from'] ?? Carbon::now()->subDays(30);
        $to = $filters['to'] ?? Carbon::now();
        $groupBy = $filters['group_by'] ?? 'monthly';

        return [
            'from' => $from,
            'to' => $to,
            'group_by' => $groupBy,
        ];
    }

    private function getGroupByFormat(string $groupBy): string
    {
        switch ($groupBy) {
            case 'daily':
                return 'Y-m-d';
            case 'weekly':
                return 'o-W';
            case 'monthly':
            default:
                return 'Y-M';
        }
    }

    private function extractDisplayPeriod(string $period, string $groupBy): string
    {
        switch ($groupBy) {
            case 'daily':
                $date = Carbon::createFromFormat('Y-m-d', $period);

                return $date->format('D j, M');
            case 'weekly':
                $year = substr($period, 0, 4);
                $week = substr($period, 5, 2);
                $date = new \DateTime;
                $date->setISODate($year, $week);

                return $date->format('M j');
            case 'monthly':
            default:
                $date = Carbon::createFromFormat('Y-M', $period);

                return $date->format('M Y');
        }
    }

    private function getPreviousPeriodDates(array $filters): array
    {
        $from = Carbon::parse($filters['from'])->startOfDay();
        $to = Carbon::parse($filters['to'])->startOfDay();
        $diff = $from->diffInDays($to);

        $previousFrom = $from->copy()->subDays($diff)->startOfDay();
        $previousTo = $to->copy()->subDays($diff)->startOfDay();

        return [$previousFrom, $previousTo];
    }

    private function calculatePercentageChange($currentValue, $previousValue): float
    {
        if ($previousValue == 0) {
            return $currentValue > 0 ? 100 : 0;
        }

        return (($currentValue - $previousValue) / $previousValue) * 100;
    }

    private function metersToKilometers($meters): float
    {
        return round($meters / 1000, 2);
    }

    public function getTotalNumberOfDrivers(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        $previousTo = $this->getPreviousPeriodDates($filters)[1];

        $currentTotal = Driver::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->count();

        $previousTotal = Driver::where('company_id', $company->id)
            ->where('created_at', '<=', $previousTo)
            ->count();

        $percentageChange = $this->calculatePercentageChange($currentTotal, $previousTotal);

        return [
            'value' => $currentTotal,
            'percentage_change' => round($percentageChange, 2),
        ];
    }

    public function getTotalNumberOfAssistants(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        $previousTo = $this->getPreviousPeriodDates($filters)[1];

        $currentTotal = Assistant::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->count();

        $previousTotal = Assistant::where('company_id', $company->id)
            ->where('created_at', '<=', $previousTo)
            ->count();

        $percentageChange = $this->calculatePercentageChange($currentTotal, $previousTotal);

        return [
            'value' => $currentTotal,
            'percentage_change' => round($percentageChange, 2),
        ];
    }

    public function getTotalNumberOfVehicles(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        $previousTo = $this->getPreviousPeriodDates($filters)[1];

        $currentTotal = Vehicle::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->count();

        $previousTotal = Vehicle::where('company_id', $company->id)
            ->where('created_at', '<=', $previousTo)
            ->count();

        $percentageChange = $this->calculatePercentageChange($currentTotal, $previousTotal);

        return [
            'value' => $currentTotal,
            'percentage_change' => round($percentageChange, 2),
        ];
    }

    public function getTotalDistanceTraveled(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        [$previousFrom, $previousTo] = $this->getPreviousPeriodDates($filters);

        $currentDistance = Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->sum('distance');

        $previousDistance = Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$previousFrom, $previousTo])
            ->sum('distance');

        $currentDistance = $this->metersToKilometers($currentDistance);
        $previousDistance = $this->metersToKilometers($previousDistance);

        $percentageChange = $this->calculatePercentageChange($currentDistance, $previousDistance);

        return [
            'value' => $currentDistance,
            'percentage_change' => round($percentageChange, 2),
        ];
    }

    public function getAverageSpeedByDriver(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        $groupByFormat = $this->getGroupByFormat($filters['group_by']);

        $allDrivers = Driver::where('company_id', $company->id)
            ->select('id', 'first_name', 'last_name')
            ->get()
            ->keyBy('id');

        $ridesData = Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->whereNotNull('driver_id')
            ->whereNotNull('arrived_at')
            ->whereNotNull('distance')
            ->get(['started_at', 'driver_id', 'distance', 'started_at', 'arrived_at'])
            ->groupBy(function ($ride) use ($groupByFormat) {
                return $ride->started_at->format($groupByFormat);
            });

        $result = [];
        foreach ($ridesData as $period => $periodRides) {
            $displayPeriod = $this->extractDisplayPeriod($period, $filters['group_by']);
            $driverSpeeds = $periodRides->groupBy('driver_id')
                ->map(function ($driverRides) {
                    return $driverRides->avg('average_speed');
                });

            $periodData = ['name' => $displayPeriod];
            $serieIndex = 0;

            foreach ($allDrivers as $driverId => $driver) {
                $periodData["serie{$serieIndex}"] = [
                    'label' => $driver?->name,
                    'value' => round($driverSpeeds->get($driverId, 0), 2),
                ];
                $serieIndex++;
            }

            $result[] = $periodData;
        }

        return $result;
    }

    public function getAverageVehicleUsageOverTime(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);
        $groupByFormat = $this->getGroupByFormat($filters['group_by']);

        $ridesData = Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->get(['started_at'])
            ->groupBy(function ($ride) use ($groupByFormat) {
                return $ride->started_at->format($groupByFormat);
            })
            ->map(function ($rides) {
                return $rides->count();
            });

        $result = [];
        foreach ($ridesData as $period => $count) {
            $result[] = [
                'name' => $this->extractDisplayPeriod($period, $filters['group_by']),
                'value' => $count,
            ];
        }

        usort($result, function ($a, $b) {
            return strtotime($a['name']) - strtotime($b['name']);
        });

        return $result;
    }

    public function getRideCountsByVehicle(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->with('vehicle:id,name')
            ->get(['vehicle_id'])
            ->groupBy('vehicle_id')
            ->map(function ($rides, $vehicleId) {
                $vehicle = $rides->first()->vehicle;

                return [
                    'name' => $vehicle ? $vehicle->name : '-',
                    'value' => $rides->count(),
                ];
            })
            ->sortByDesc('value')
            ->values()
            ->toArray();
    }

    public function getRideCountsByDriver(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->with('driver:id,first_name,last_name')
            ->get(['driver_id'])
            ->groupBy('driver_id')
            ->map(function ($rides, $driverId) {
                $driver = $rides->first()->driver;

                return [
                    'name' => $driver?->name ?? '-',
                    'value' => $rides->count(),
                ];
            })
            ->sortByDesc('value')
            ->values()
            ->toArray();
    }

    public function getDriverPerformanceMetrics(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Ride::where('company_id', $company->id)
            ->whereBetween('started_at', [$filters['from'], $filters['to']])
            ->with('driver:id,first_name,last_name')
            ->get()
            ->groupBy('driver_id')
            ->map(function ($driverRides) {
                $driver = $driverRides->first()->driver;

                return [
                    'driver_id' => $driverRides->first()->driver_id,
                    'driver_name' => $driver?->name ?? '-',
                    'total_trips' => $driverRides->count(),
                    'avg_ride_duration' => round($driverRides->avg('duration') ?? 0, 2),
                    'customer_feedback' => round($driverRides->avg('feedback_score') ?? 0, 2),
                    'safety_incidents' => $driverRides->sum('incidents_count') ?? 0,
                    'avg_speed' => round($driverRides->avg('average_speed') ?? 0, 2),
                ];
            })
            ->values()
            ->toArray();
    }

    public function getDriverStatusCounts(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Driver::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->get(['status'])
            ->groupBy('status')
            ->map(function ($drivers, $status) {
                return [
                    'label' => ucfirst(__('general.enums.'.$status)),
                    'value' => $drivers->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    public function getVehicleStatusCounts(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Vehicle::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->get(['status'])
            ->groupBy('status')
            ->map(function ($vehicles, $status) {
                return [
                    'label' => ucfirst(__('general.enums.'.$status)),
                    'value' => $vehicles->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    public function getVehicleTypeCounts(Company $company, ?array $filters)
    {
        $filters = $this->applyDefaultFilters($filters);

        return Vehicle::where('company_id', $company->id)
            ->where('created_at', '<=', $filters['to'])
            ->get(['type'])
            ->groupBy('type')
            ->map(function ($vehicles, $type) {
                return [
                    'label' => ucfirst(__('fleet.vehicle.types.'.$type)),
                    'value' => $vehicles->count(),
                ];
            })
            ->values()
            ->toArray();
    }
}
