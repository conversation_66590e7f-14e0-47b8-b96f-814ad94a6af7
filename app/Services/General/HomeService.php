<?php

namespace App\Services\General;

use App\Enums\Transport\RideStatus;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\Incident\Incident;
use App\Models\Plan\Absence;
use App\Models\Review\Review;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Services\Fleet\AssistantService;
use App\Services\Fleet\DriverService;
use App\Services\Fleet\VehicleService;
use App\Services\Transport\RideService;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;

class HomeService
{
    public function __construct(
        private readonly RideService $rideService,
        private readonly DriverService $driverService,
        private readonly VehicleService $vehicleService,
        private readonly AssistantService $assistantService
    ) {}

    public function getDashboardStats(Authenticatable $user): array
    {
        $today = Carbon::now();

        return [
            'passengers_count' => Passenger::where('company_id', $user->company_id)->count(),
            'drivers_count' => Driver::where('company_id', $user->company_id)->count(),
            'vehicles_count' => Vehicle::where('company_id', $user->company_id)->count(),
            'assistants_count' => Assistant::where('company_id', $user->company_id)->count(),
            'incidents_today_count' => Incident::where('company_id', $user->company_id)
                ->whereDate('created_at', $today)->count(),
            'absences_today_count' => Absence::where('company_id', $user->company_id)
                ->whereDate('from', '<=', $today)
                ->whereDate('to', '>=', $today)->count(),
        ];
    }

    public function getRidesData(Authenticatable $user): array
    {
        $today = Carbon::now();
        $nextFiveDays = $today->copy()->addDays(5);

        $startedRides = Ride::where('company_id', $user->company_id)
            ->where('status', RideStatus::Ongoing)
            ->with(['driver', 'vehicle'])
            ->get();

        $scheduledRides = $this->rideService->getScheduledRides($user, $today, $nextFiveDays)
            ->load(['driver', 'vehicle', 'schedule']);

        return [
            'ongoing_rides' => $startedRides,
            'next_rides' => $scheduledRides,
        ];
    }

    public function getTodayIncidents(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        $today = Carbon::now();

        return Incident::where('company_id', $user->company_id)
            ->whereDate('created_at', $today)
            ->with(['reporter'])
            ->latest()
            ->take(5)
            ->get();
    }

    public function getTodayAbsences(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        $today = Carbon::now();

        return Absence::where('company_id', $user->company_id)
            ->whereDate('from', '<=', $today)
            ->whereDate('to', '>=', $today)
            ->with(['passenger'])
            ->latest()
            ->get();
    }

    public function getTopDrivers(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        return Driver::where('company_id', $user->company_id)
            ->take(5)
            ->get();
    }

    public function getTopVehicles(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        return Vehicle::where('company_id', $user->company_id)
            ->take(5)
            ->get();
    }

    public function getTopAssistants(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        return Assistant::where('company_id', $user->company_id)
            ->take(5)
            ->get();
    }

    public function getRecentReviews(Authenticatable $user): \Illuminate\Database\Eloquent\Collection
    {
        return Review::whereHas('ride', function ($query) use ($user) {
            $query->where('company_id', $user->company_id);
        })
            ->with(['reviewer'])
            ->latest()
            ->take(10)
            ->get();
    }
}
