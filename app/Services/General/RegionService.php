<?php

namespace App\Services\General;

use App\Models\Company\Company;
use App\Models\General\Region;
use App\Repositories\General\RegionRepository;

readonly class RegionService
{
    public function __construct(
        private RegionRepository $regionRepository
    ) {}

    public function find(int $regionId): ?Region
    {
        return $this->regionRepository->find($regionId);
    }

    public function save(Company $company, array $data): Region
    {
        $data['company_id'] = $company->id;

        return $this->regionRepository->create($data);
    }

    public function update(Company $company, Region $region, array $data): void
    {
        $data['company_id'] = $company->id;

        $this->regionRepository->update($region, $data);
    }

    public function delete(Region $region): void
    {
        $this->regionRepository->delete($region);
    }
}
