<?php

namespace App\Services\Fleet;

use App\Enums\Fleet\DriverStatus;
use App\Models\Company\Company;
use App\Models\Fleet\Driver;
use App\Repositories\Fleet\DriverRepository;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Collection;

readonly class DriverService
{
    public function __construct(
        private DriverRepository $driverRepository,
    ) {}

    public function get(Authenticatable $user, array $filterData = [], bool $paginate = false)
    {
        $query = Driver::query()->company($user->company_id)
            ->with('defaultVehicle');

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function find(int $driverId): ?Driver
    {
        return $this->driverRepository->find($driverId);
    }

    public function save(array $data, Authenticatable $user): Driver
    {
        $data['company_id'] = $user->company_id;

        return $this->driverRepository->create($data);
    }

    public function update(Driver $driver, array $data, Authenticatable $user): void
    {
        $data['company_id'] = $user->company_id;
        $this->driverRepository->update($driver, $data);

        if (isset($data['image'])) {
            $this->handleUploadedMedia($driver, 'image');
        }
    }

    public function handleUploadedMedia(Driver $driver, string $collectionName): void
    {

        $driver->clearImage();
        $driver->addMediaFromRequest($collectionName)->toMediaCollection($collectionName);
    }

    public function delete(Driver $driver): void
    {
        $this->driverRepository->delete($driver);
    }

    public function list(Company $company): Collection
    {
        return Driver::query()->company($company->id)->active()->get();
    }

    public function listForMap(Company $company): Collection
    {
        return Driver::where('company_id', $company->id)
            ->whereHas('ride')
            ->where('status', DriverStatus::Active)
            ->with([
                'ride' => function ($query) {
                    $query->select('id', 'driver_id', 'status', 'vehicle_id', 'started_at', 'arrived_at', 'route_id')
                        ->withCount('stops', 'passengers')
                        ->with([
                            'vehicle:id,type,capacity',
                            'route:id,origin_id,destination_id,name',
                            'route.origin:id,name',
                            'route.destination:id,name',
                        ])
                        ->latest();
                },
            ])
            ->get()
            ->map(function ($driver) {
                $driver->append('coordinates');

                return $driver;
            });
    }

    public function getForMap(Driver $driver): Driver
    {
        $driver->load([
            'ride' => function ($query) {
                $query->select('id', 'driver_id', 'status', 'vehicle_id', 'started_at', 'arrived_at', 'route_id')
                    ->withCount('stops', 'passengers')
                    ->with([
                        'vehicle:id,type,capacity',
                        'route:id,origin_id,destination_id,name',
                        'route.origin:id,name',
                        'route.destination:id,name',
                    ])
                    ->latest();
            },
        ]);

        $driver->append('coordinates');

        return $driver;
    }

    public function listForMobileMap(Company $company): Collection
    {
        return Driver::where('company_id', $company->id)
            ->whereHas('ride')
            ->where('status', DriverStatus::Active)
            ->with([
                'ride' => function ($query) {
                    $query->select('id', 'driver_id', 'status', 'vehicle_id')
                        ->withCount('stops', 'passengers')
                        ->with([
                            'vehicle:id,type',
                        ])
                        ->latest();
                },
            ])
            ->get();
    }

    public function findMany(array $ids): ?Collection
    {
        return Driver::findMany($ids);
    }
}
