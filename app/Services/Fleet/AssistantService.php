<?php

namespace App\Services\Fleet;

use App\Models\Fleet\Assistant;
use App\Repositories\Fleet\AssistantRepository;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

readonly class AssistantService
{
    public function __construct(
        private AssistantRepository $assistantRepository
    ) {}

    public function get(Authenticatable $user, array $filterData = [], bool $paginate = false): LengthAwarePaginator|Collection|null
    {
        $query = Assistant::query()->company($user->company_id);

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function find(int $assistantId): ?Assistant
    {
        return $this->assistantRepository->find($assistantId);
    }

    public function save(array $data, Authenticatable $user): Assistant
    {
        $data['company_id'] = $user->company_id;

        return $this->assistantRepository->create($data);
    }

    public function update(Assistant $assistant, array $data): void
    {
        $this->assistantRepository->update($assistant, $data);

        if (isset($data['image']) && $data['image']) {
            $this->handleUploadedMedia($assistant, 'image');
        }
    }

    public function handleUploadedMedia(Assistant $assistant, string $collectionName): void
    {
        $assistant->clearImage();
        $assistant->addMediaFromRequest($collectionName)->toMediaCollection($collectionName);
    }

    public function delete(Assistant $assistant): void
    {
        $this->assistantRepository->delete($assistant);
    }
}
