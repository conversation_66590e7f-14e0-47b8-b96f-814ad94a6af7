<?php

namespace App\Services\User;

use App\Models\Auth\User;
use App\Repositories\Auth\UserRepository;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Collection;

class UserService
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {}

    public function firstUser(int $companyId): ?User
    {
        return User::query()->company($companyId)->first();
    }

    public function get(int $companyId, array $filterData = [], bool $paginate = false): Collection
    {
        $query = User::query()->company($companyId);

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function save(array $data, Authenticatable $user): User
    {
        $data['company_id'] = $user->company_id;

        return $this->userRepository->create($data);
    }

    public function update(User $user, array $data): void
    {
        $this->userRepository->update($user, $data);

        if (isset($data['image']) && $data['image']) {
            $this->handleUploadedMedia($user, 'image');
        }
    }

    public function handleUploadedMedia(User $user, string $collectionName): void
    {
        $user->clearImage();
        $user->addMediaFromRequest($collectionName)->toMediaCollection($collectionName);
    }

    public function find(int $id): ?User
    {
        return $this->userRepository->find($id);
    }

    public function delete(User $user): ?bool
    {
        return $this->userRepository->delete($user);
    }
}
