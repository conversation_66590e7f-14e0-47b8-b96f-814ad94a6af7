<?php

namespace App\Services\User;

use App\Enums\Auth\UserInvitationStatus;
use App\Mail\UserInvitationMail;
use App\Models\Auth\User;
use App\Models\Auth\UserInvitation;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class UserInvitationService
{
    public function invite(array $invitationData, User $inviter): void
    {
        $invitationData = array_merge($invitationData, [
            'token' => Str::random(24), 'inviter_id' => $inviter->id,
            'status' => UserInvitationStatus::Invited,
        ]);
        $userInvitation = UserInvitation::create($invitationData);
        Mail::to($invitationData['email'])->send(new UserInvitationMail($userInvitation));
    }
}
