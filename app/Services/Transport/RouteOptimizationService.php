<?php

namespace App\Services\Transport;

use App\Models\Geo\Location;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Repositories\Geo\LocationRepository;
use App\Services\Api\Geo\HereSequencingApiService;
use Exception;
use Illuminate\Support\Collection;

readonly class RouteOptimizationService
{
    public function __construct(
        private LocationRepository $locationRepository,
        private HereSequencingApiService $hereSequencingApiService,
    ) {}

    public function needsOptimisation(Route $route): bool
    {
        if (app()->environment('local', 'testing')) {
            return false;
        }

        if ($route->isStatic()) {
            return false;
        }

        if (! $route->optimized_at) {
            return true;
        }

        return
            $route->optimized_at->isBefore(now()->subWeek())
            or
            $route->updated_at->isAfter($route->optimized_at)
            or
            $route->stations() // checking stations
                ->where('stations.updated_at', '>', $route->optimized_at)
                ->orWhere('stations.created_at', '>', $route->optimized_at)
                ->exists()
            or
            $route->stationLocations() // checking locations
                ->where('locations.updated_at', '>', $route->optimized_at)
                ->orWhere('locations.created_at', '>', $route->optimized_at)
                ->exists()
            or
            $route->origin->updated_at->isAfter($route->optimized_at)
            or
            $route->destination->updated_at->isAfter($route->optimized_at);
    }

    public function optimizeStationsSequence(Route $route, bool $force = false): Route
    {
        if ((! $this->needsOptimisation($route)) and (! $force)) {
            return $route;
        }

        $stationLocationIds = $route->stations()->pluck('location_id')->toArray();

        $stationLocations = $this->locationRepository->findMany($stationLocationIds);

        try {
            $optimalSequence = $this->hereSequencingApiService->getOptimalSequence(
                $route->origin,
                $route->destination,
                $stationLocations,
            );
        } catch (Exception $exception) {
            // implement sentry
            return $route;
        }

        $optimalSequence->where('id', 'like', 'des%')->each(function ($order) {
            Station::where('id', $order['id'])->update([
                'order' => $order['order'],
                'duration' => $order['duration'],
                'distance' => $order['distance'],
            ]);
        });

        $route->updateQuietly([
            'distance' => intval($optimalSequence->sum('distance')),
            'duration' => intval($optimalSequence->sum('duration')),
            'optimized_at' => now(),
        ]);

        $route->refresh();

        return $route;
    }

    public function getOptimalRouteForLocation(Location $location, Collection $routes): Route
    {
        $optimalRoute = null;
        $optimalRouteDistance = 0;

        foreach ($routes as $route) {
            $route->load('stations.location');

            foreach ($route->stations as $station) {
                $distance = $location->coordinates->distanceTo($station->location->coordinates);
                if ($optimalRouteDistance === null or $distance < $optimalRouteDistance) {
                    $optimalRouteDistance = $distance;
                    $optimalRoute = $route;
                }
            }
        }

        return $optimalRoute;
    }
}
