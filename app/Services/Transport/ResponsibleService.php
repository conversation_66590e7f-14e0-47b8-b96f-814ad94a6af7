<?php

namespace App\Services\Transport;

use App\Models\Transport\Passenger;
use App\Models\Transport\PassengerResponsible;
use App\Models\Transport\Responsible;
use App\Repositories\Transport\ResponsibleRepository;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ResponsibleService
{
    public function __construct(
        private readonly ResponsibleRepository $responsibleRepository,
    ) {}

    public function get(Authenticatable $user, array $filterData = [], bool $paginate = false): LengthAwarePaginator|Collection|null
    {
        $query = Responsible::query()->company($user->company_id);

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function find(int $responsibleId): ?Responsible
    {
        return $this->responsibleRepository->find($responsibleId);
    }

    public function save(array $data, Authenticatable $user): Responsible
    {
        $data['company_id'] = $user->company_id;

        return $this->responsibleRepository->create($data);
    }

    public function update(Responsible $responsible, array $data): void
    {
        $this->responsibleRepository->update($responsible, $data);
        if ($data['image']) {
            $this->handleUploadedMedia($responsible, 'image');
        }
    }

    public function handleUploadedMedia(Responsible $responsible, string $collectionName): void
    {
        $responsible->clearImage();
        $responsible->addMediaFromRequest($collectionName)->toMediaCollection($collectionName);
    }

    public function delete(Responsible $responsible): void
    {
        $this->responsibleRepository->delete($responsible);
    }

    public function getForPassengerIds(array $passengerIds): Collection
    {
        return Responsible::whereHas('passengers', function ($query) use ($passengerIds) {
            $query->whereIn('id', $passengerIds);
        })->with([
            'passengers' => function ($query) use ($passengerIds) {
                $query->whereIn('id', $passengerIds)->select([
                    'id', 'first_name',
                ]);
            },
        ])->get();
    }

    public function getForPassengers(Collection $passengers): Collection
    {
        return Responsible::whereHas('passengers', function ($query) use ($passengers) {
            $query->whereIn('id', $passengers->pluck('id'));
        })->get();
    }

    public function isResponsible(Responsible $responsible, Passenger $passenger)
    {
        return PassengerResponsible::where('passenger_id', $passenger->id)
            ->where('responsible_id', $responsible->id)
            ->exists();
    }

    public function addPassengersFromIds(Responsible $responsible, array $passengerIds): void
    {
        $passengers = Passenger::whereIn('id', $passengerIds)->get();

        foreach ($passengers as $passenger) {
            if (! $responsible->hasPassenger($passenger)) {
                $responsible->passengers()->attach($passenger->id, [
                    'created_at' => now(),
                    'company_id' => $passenger->company_id,
                ]);
            }
        }
    }

    public function removePassenger(Responsible $responsible, Passenger $passenger): void
    {
        if ($responsible->passengers()->where('id', $passenger->id)->exists()) {
            $responsible->passengers()->detach($passenger->id);
        }
    }
}
