<?php

namespace App\Services\Transport;

use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Models\Transport\Route;
use App\Repositories\Transport\PassengerRepository;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

readonly class PassengerService
{
    public function __construct(
        private PassengerRepository $passengerRepository
    ) {}

    public function get(
        Authenticatable $user,
        array $filterData = [],
        bool $paginate = false,
        int $perPage = 10,
        array $relations = [],
        bool $filterLocation = false
    ): Collection|LengthAwarePaginator|array {
        $query = Passenger::query()->company($user->company_id)
            ->with($relations);

        if ($filterLocation) {
            $query->whereHas('location');
        }

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->orderBy('id', 'asc')
                ->paginate($perPage)
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function find(int $passengerId): ?Passenger
    {
        return $this->passengerRepository->find($passengerId);
    }

    public function save(array $data, Authenticatable $user): Passenger
    {
        $data['company_id'] = $user->company_id;

        return $this->passengerRepository->create($data);
    }

    public function update(Passenger $passenger, array $data): void
    {
        $this->passengerRepository->update($passenger, $data);
        if ($data['image']) {
            $this->handleUploadedMedia($passenger, 'image');
        }
    }

    public function handleUploadedMedia(Passenger $passenger, string $collectionName): void
    {
        $passenger->clearImage();
        $passenger->addMediaFromRequest($collectionName)->toMediaCollection($collectionName);
    }

    public function delete(Passenger $passenger): void
    {
        $this->passengerRepository->delete($passenger);
    }

    public function belongToRoute(Passenger $passenger, Route $route): bool
    {
        return $passenger->routes()->where('id', $route->id)->exists();
    }

    public function responsibleHasPassengerThatBelongsToRoute(Responsible $responsible, Route $route): bool
    {
        foreach ($responsible->passengers as $passenger) {
            $check = $this->belongToRoute($passenger, $route);
            if ($check) {
                return true;
            }
        }

        return false;
    }

    public function listByResponsible(Responsible $responsible): Collection
    {
        return $responsible->passengers()->with('location')->get();
    }
}
