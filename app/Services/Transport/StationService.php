<?php

namespace App\Services\Transport;

use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Repositories\Transport\StationRepository;
use Carbon\Carbon;

class StationService
{
    public function __construct(
        private readonly StationRepository $stationRepository,
        private readonly StationPassengerService $stationPassengerService,
    ) {}

    public function getOrderedStationsData(array $stationIds): array
    {
        $lastOrder = Station::max('order');

        return collect($stationIds)->map(function ($stationId, $index) use ($lastOrder) {
            return [
                'station_id' => $stationId,
                'order' => $lastOrder + ++$index,
            ];
        })->toArray();
    }

    public function updateOrder(Route $route, $stationsIds): void
    {
        $index = 1;
        foreach ($stationsIds as $stationId) {
            $station = $route->stations()->findOrFail($stationId);
            $station->update([
                'order' => $index++,
            ]);
        }
    }

    // public function save(Route $route, array $data): void
    // {
    //     foreach ($data as $station) {
    //         $this->stationRepository->create([
    //             'route_id' => $route->id,
    //             'station_id' => $station['station_id'],
    //             'order' => $station['order'],
    //         ]);
    //     }
    // }

    public function save(Route $route, $data): Station
    {
        // Get the max station order for the specific route
        $stationLastOrder = Station::where('route_id', $route->id)->max('order') + 1;

        $data['route_id'] = $route->id;
        $data['order'] = $stationLastOrder;

        $station = $this->stationRepository->create($data);

        foreach ($data['passengers'] as $passenger) {
            $this->stationPassengerService->save([
                'company_id' => $data['company_id'],
                'passenger_id' => $passenger['id'],
                'station_id' => $station->id,
                'route_id' => $route->id,
                'seat' => $passenger['seat'],
            ]);
        }

        return $station;
    }

    public function update(Route $route, Station $station, $data): void
    {
        $this->stationRepository->update($station, [
            'name' => $data['name'],
            'location_id' => $data['location_id'],
        ]);

        $syncData = [];

        foreach ($data['passengers'] as $passenger) {
            $syncData[$passenger['id']] = [
                'company_id' => $station->company_id,
                'created_at' => Carbon::now(),
                'route_id' => $route->id,
                'seat' => $passenger['seat'],
            ];
        }
        $station->passengers()->sync($syncData);
    }

    public function delete(Station $station): void
    {
        $this->stationRepository->delete($station);
    }

    public function find(int $id): ?Station
    {
        return $this->stationRepository->find($id);
    }

    public function getByRide(Ride $ride, Passenger $passenger): ?Station
    {
        return $passenger
            ->stations()
            ->where('route_id', $ride->route_id)
            ->first();
    }
}
