<?php

namespace App\Services\Geo;

use App\Enums\General\Locale;
use App\Enums\General\Models;
use App\Enums\General\ModelType;
use App\Enums\Transport\PassengerStatus;
use App\Models\Geo\Location;
use App\Models\Transport\Passenger;
use App\Models\Transport\Route;
use App\Repositories\Company\CompanyRepository;
use App\Repositories\Geo\LocationRepository;
use App\Services\Api\Geo\MapboxGeocodingApiService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

readonly class LocationService
{
    public function __construct(
        private LocationRepository $locationRepository,
        private MapboxGeocodingApiService $mapboxGeocodingApiService,
        private CompanyRepository $companyRepository,
    ) {}

    public function find(int $locationId): ?Location
    {
        return $this->locationRepository->find($locationId);
    }

    public function save(array $data): Location
    {
        $data = $this->fillAddressFields($data);
        $location = $this->locationRepository->create($data);
        $this->ensureOtherLocationsAreNonPrimary($location);

        return $location;
    }

    public function fillAddressFields(array $data): array
    {
        $company = $this->companyRepository->find($data['company_id']);

        $address = $this->mapboxGeocodingApiService->geocodeCoordinates(
            $data['lat'],
            $data['lng'],
            $company->language ?? Locale::English
        );

        return $address ? array_merge($data, $address->toArray()) : $data;
    }

    public function update(Location $location, array $data): Location
    {
        if ($location->lat !== $data['lat'] or $location->lng !== $data['lng']) {
            $data['company_id'] = $location->company_id;
            $data = $this->fillAddressFields($data);
        }

        $location = $this->locationRepository->update($location, $data);
        $this->ensureOtherLocationsAreNonPrimary($location);

        return $location;
    }

    public function delete(Location $location): void
    {
        $locationIsPrimary = $location->isPrimary();
        $model = $location->model;

        $this->locationRepository->delete($location);

        if ($locationIsPrimary && $model) {
            $this->ensureOtherLocationIsPrimary($model);
        }
    }

    public function ensureOtherLocationsAreNonPrimary(Location $location): void
    {
        if (! $location->primary) {
            return;
        }

        Location::where('primary', true)
            ->where('model_type', $location->model_type)
            ->where('model_id', $location->model_id)
            ->where('id', '!=', $location->id)
            ->update([
                'primary' => false,
            ]);
    }

    public function ensureOtherLocationIsPrimary(Model $model): void
    {
        Location::where('model_type', ModelType::getKey(get_class($model)))
            ->where('model_id', $model->id)
            ->inRandomOrder()
            ->first()
            ?->update([
                'primary' => true,
            ]);
    }

    public function getWaypointsForRoute(Route $route): Collection
    {
        return $route->stationLocations()->select([
            'locations.id',
            'lat',
            'lng',
        ])->get();
    }

    public function getForPassengerIds(array $passengerIds): Collection
    {
        return Location::where('model_type', Models::Passenger)
            ->whereIn('model_id', $passengerIds)
            ->primary()
            ->get();
    }

    public function getForAllPassengers(): Collection
    {
        return Location::where('model_type', Models::Passenger)
            ->whereHas('model', function ($query) {
                $query->where('status', PassengerStatus::Active);
            })
            ->primary()
            ->get();
    }

    public function getPassengersByLocationIds(array $locationIds): Collection
    {
        $passengerIds = Location::whereIn('id', $locationIds)
            ->where('model_type', Models::Passenger)
            ->pluck('model_id');

        return Passenger::findMany($passengerIds);
    }
}
