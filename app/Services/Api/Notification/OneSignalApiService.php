<?php

namespace App\Services\Api\Notification;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class OneSignalApiService
{
    private Client $client;

    private const string BASE_URL = 'https://api.onesignal.com/notifications?c=push';

    public function __construct()
    {
        $this->client = new Client;
    }

    public function pushMobileNotification(array $externalIds, array $headings, array $contents, ?string $image = null): void
    {
        $jsonBody = [
            'app_id' => config('services.onesignal.app_id'),
            'include_aliases' => [
                'external_id' => $externalIds,
            ],
            'headings' => $headings,
            'contents' => $contents,
            'target_channel' => 'push',
        ];

        if ($image) {
            $jsonBody['big_picture'] = $image;
            $jsonBody['ios_attachments'] = [
                'id' => $image,
            ];
        }

        if (! app()->environment('local', 'testing')) {
            $this->client->post(self::BASE_URL, [
                'headers' => [
                    'Authorization' => 'Key '.config('services.onesignal.api_key'),
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $jsonBody,
            ]);
        }

        Log::info('OneSignal push notification pushed'.now()->format('H:i'), $jsonBody);
    }
}
