<?php

namespace App\Traits\General;

use App\Enums\Auth\UserStatus;
use App\Enums\General\Models;
use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Notifications\Channels\OneSignalNotificationChannel;
use App\Notifications\Channels\SlackNotificationChannel;
use Illuminate\Database\Eloquent\Builder;

trait AppUser
{
    public function isUser(): bool
    {
        return $this instanceof User;
    }

    public function isDriver(): bool
    {
        return $this instanceof Driver;
    }

    public function isPassenger(): bool
    {
        return $this instanceof Passenger;
    }

    public function isResponsible(): bool
    {
        return $this instanceof Responsible;
    }

    public function isAssistant(): bool
    {
        return $this instanceof Assistant;
    }

    public function isA(string $type): bool
    {
        return $this->type() === $type;
    }

    public function isSuperAdmin(): bool
    {
        return $this->isUser() and in_array($this->id, config('auth.super_admins'));
    }

    public function type(): string
    {
        return match (true) {
            $this->isDriver() => Models::Driver,
            $this->isResponsible() => Models::Responsible,
            $this->isUser() => Models::User,
            $this->isPassenger() => Models::Passenger,
            $this->isAssistant() => Models::Assistant,
        };
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }

    public function getOtpCacheKey(): string
    {
        return strtolower($this->type().'_otp_'.$this->id);
    }

    public function setOtp(string $otp): bool
    {
        return cache()->set(
            $this->getOtpCacheKey(),
            $otp,
            now()->addMinutes(10)
        );
    }

    public function getOtp(): ?string
    {
        return cache()->get($this->getOtpCacheKey());
    }

    public function verifyOtp(string $otp): bool
    {
        return $this->getOtp() === $otp or (! app()->isProduction());
    }

    public function getOneSignalKey(): string
    {
        return $this->type().'.'.$this->id;
    }

    public static function getOneSignal(string $type, int $id): string
    {
        return $type.'.'.$id;
    }

    public function whatsAppRoute(): string
    {
        return 'whatsapp:'.$this->phone;
    }

    public function getNotificationChannels(): array
    {
        if (app()->isProduction()) {
            return [
                OneSignalNotificationChannel::class,
            ];
        }

        if (app()->environment('testing')) {
            return [
                SlackNotificationChannel::class,
            ];
        }

        return [
            OneSignalNotificationChannel::class,
            SlackNotificationChannel::class,
        ];
    }

    public function scopeActive(Builder $builder): void
    {
        $builder->where('status', UserStatus::Active);
    }

    public function resolveRouteBinding($value, $field = null)
    {
        if (! is_numeric($value) || ! $post = $this->where($field ?? 'id', $value)->first()) {
            abort(404);
        }

        return $post;
    }

    public function getNameAttribute(): string
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function isInternalUser(): bool
    {
        return in_array($this->company_id, config('yosr.internal_companies'));
    }
}
