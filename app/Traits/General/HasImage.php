<?php

namespace App\Traits\General;

use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;
use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;

trait HasImage
{
    use InteractsWithMedia;

    const string IMAGE_PLACEHOLDER_PROD = 'https://api.dicebear.com/9.x/initials/png?scale=110&backgroundColor=00acc1,039be5,1e88e5,43a047,5e35b1,7cb342,8e24aa,c0ca33,d81b60,e53935,f4511e,fb8c00,fdd835,ffb300&backgroundType=gradientLinear&backgroundRotation=360,0,40,20,10&fontFamily=Tahoma&seed=';

    const string IMAGE_PLACEHOLDER = 'https://avatar.iran.liara.run/public?username=';

    const string IMAGE_PLACEHOLDER_DRIVER = 'https://avatar.iran.liara.run/public/job/police/male?username=';

    const string IMAGE_PLACEHOLDER_ASSISTANT = 'https://avatar.iran.liara.run/public/job/doctor/female?username=';

    public function getImageAttribute(): string
    {
        if (cache()->has($this->getImageCacheKey())) {
            return cache()->get($this->getImageCacheKey());
        }

        if ($image = $this->getFirstMediaUrl('image', 'thumb')) {
            cache()->put($this->getImageCacheKey(), $image, now()->addHour());

            return $image;
        }

        //        if (app()->isProduction()) {
        return self::IMAGE_PLACEHOLDER_PROD.substr($this->first_name, 0, 1).substr($this->last_name, 0, 1);
        //        }

        if ($this->isDriver()) {
            return self::IMAGE_PLACEHOLDER_DRIVER.$this->name;
        }

        if ($this->isAssistant()) {
            return self::IMAGE_PLACEHOLDER_ASSISTANT.$this->name;
        }

        return self::IMAGE_PLACEHOLDER.$this->name;
    }

    public function getImageCacheKey(): string
    {
        return strtolower($this->type()).'_img_'.$this->id;
    }

    public function clearImage(): void
    {
        $this->clearMediaCollection('image');
        cache()->forget($this->getImageCacheKey());
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(400)
            ->height(400)
            ->keepOriginalImageFormat()
            ->quality(75)
            ->optimize()
            ->nonQueued();
    }
}
