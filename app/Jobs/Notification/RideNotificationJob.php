<?php /** @noinspection ALL */

namespace App\Jobs\Notification;

use App\Enums\General\Models;
use App\Models\Transport\Ride;
use App\Notifications\Transport\PassengerNotification;
use App\Notifications\Transport\ResponsibleNotification;
use App\Services\Auth\AuthService;
use App\Services\Transport\ResponsibleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Notification;

class RideNotificationJob implements ShouldQueue
{
    use Queueable;

    public Collection $passengerIds;

    public function __construct(
        Collection $passengers,
        public string $type,
        public array $attributes,
        public array $skip = []
    ) {
        $this->passengerIds = $passengers->pluck('id')->unique();
    }

    public function handle(): void
    {
        if ($this->passengerIds->isEmpty()) {
            return;
        }

        $authService = app()->make(AuthService::class);
        $ride = Ride::find($this->attributes['ride_id']);

        if (! isset($skip[Models::Passenger])) {

            $authService->getAuthenticatedUsers(Models::Passenger, $this->passengerIds)
                ->each(function ($passenger) use ($ride) {

                    $responsibleNotification = new PassengerNotification(
                        $this->type,
                        [
                            ...$this->attributes,
                            'user_type' => Models::Responsible,
                            'locale' => $passenger->locale,
                            'ride_mode' => $ride->mode,
                        ]
                    );

                    Notification::send($passenger, $responsibleNotification);
                });
        }

//        PassengerRideNotificationJob::dispatch(
//            $authService->getAuthenticatedUsers(Models::Passenger, $this->passengerIds),
//            $this->type,
//            $this->attributes,
//        );

        if (isset($skip[Models::Responsible])) {
            return;
        }

        $responsibles = $this->getResponsibleForPassengers($this->passengerIds);

        $authenticatedResponsibleIds = $authService->getAuthenticatedUsers(
            Models::Responsible,
            $responsibles->pluck('id')->unique()
        );

        $responsibles
            ->whereIn('id', $authenticatedResponsibleIds)
            ->each(function ($responsible) use ($ride) {

                $responsibleNotification = new ResponsibleNotification(
                    $this->type,
                    [
                        ...$this->attributes,
                        'user_type' => Models::Responsible,
                        'locale' => $responsible->locale,
                        'ride_mode' => $ride->mode,
                        'names' => $responsible->passengers->pluck('first_name')->unique()->toArray(),
                    ]
                );

                Notification::send($responsible, $responsibleNotification);
            });
    }

    public function getResponsibleForPassengers(Collection $passengerIds): Collection
    {
        return app()->make(ResponsibleService::class)->getForPassengerIds($passengerIds->toArray());
    }
}
