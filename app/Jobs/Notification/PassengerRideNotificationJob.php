<?php

namespace App\Jobs\Notification;

use App\Enums\General\Models;
use App\Mappers\Notification\OneSignalContentMapper;
use App\Models\Transport\Ride;
use App\Services\Api\Notification\OneSignalApiService;
use App\Services\Api\Notification\SlackApiService;
use App\Services\General\ImagePreviewService;
use App\Support\NotificationMessage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class PassengerRideNotificationJob implements ShouldQueue
{
    use Queueable;

    public const bool SEND_TO_SLACK_CHANNEL = true;

    public function __construct(
        public readonly Collection $passengerIds,
        public readonly string $type,
        public readonly array $attributes,
    ) {}

    public function handle(): void
    {
        $ride = Ride::find($this->attributes['ride_id']);

        $notification = new NotificationMessage($this->type, [
            ...$this->attributes,
            'user_type' => Models::Passenger,
            'ride_mode' => $ride->mode,
        ], );

        $oneSignalContentMapper = app(OneSignalContentMapper::class);
        $imagePreviewService = app(ImagePreviewService::class);

        $externalIds = $oneSignalContentMapper->getExternalIdsFor(Models::Passenger, $this->passengerIds)->toArray();
        $headings = $oneSignalContentMapper->getHeadings($notification);
        $contents = $oneSignalContentMapper->getContents($notification);
        $image = $imagePreviewService->getImageForNotification($this->type, $this->attributes);

        app(OneSignalApiService::class)->pushMobileNotification(
            $externalIds,
            $headings,
            $contents,
            $image
        );

        if (self::SEND_TO_SLACK_CHANNEL) {
            app(SlackApiService::class)->send(
                $notification->toSlackMessage($externalIds)
            );
        }
    }
}
