<?php

namespace App\Jobs\Simulation;

use App\Events\Location\UpdateDriverLocationEvent;
use App\Models\Plan\Schedule;
use App\Models\Transport\Ride;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Auth\User;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SimulateRideJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private int $updatesDelay;

    private int $stopsDelay;

    public function __construct(
        private readonly int $scheduleId,
        private readonly string $speed = 'normal',
        private readonly bool $allowAbsence = true,
        private readonly bool $allowDriverStopSkip = true,
        private readonly bool $allowPassengerStopCancel = true,
        private readonly bool $allowDriverCancelRide = false,
    ) {
        $speedMapping = [
            'slow' => ['updates' => 1000000, 'stops' => 5], // 1 second for updates, 5 seconds between stops
            'normal' => ['updates' => 500000, 'stops' => 3], // 0.5 seconds for updates, 3 seconds between stops
            'fast' => ['updates' => 200000, 'stops' => 1],   // 0.2 seconds for updates, 1 second between stops
        ];

        // Set sleep durations based on speed
        $this->updatesDelay = $speedMapping[$speed]['updates'] ?? 500000;
        $this->stopsDelay = $speedMapping[$speed]['stops'] ?? 3;
    }

    public function handle(): void
    {
        $schedule = Schedule::find($this->scheduleId);
        $driver = $schedule->driver;

        $token = $this->loginUser($driver);

        // Start ride
        $response = Http::withToken($token)
            ->accept('application/json')
            ->post(route('api.transport.rides.start'), [
                'schedule_id' => $schedule->id,
                'start_lat' => fake()->latitude(25.194453, 25.206731),
                'start_lng' => fake()->longitude(55.244370, 55.259432),
                'stations' => $schedule->route->stations->pluck('id')->toArray(),
            ]);

        usleep($this->updatesDelay);

        $ride = Ride::find($response->json('data.id'));

        $totalDuration = 0;
        $totalDistance = 0;

        foreach ($ride->route->stations as $station) {
            // Schedule stop
            $response = Http::withToken(
                $token
            )->accept('application/json')
                ->post(route('api.transport.rides.stops.schedule', [
                    'station_id' => $station->id,
                    'ride_id' => $ride->id,
                    'scheduled_at' => now()->addMinute()->toISOString(),
                ]));

            $stopId = $response->json('data.id');

            $remainingTime = rand(100, 300);
            $remainingDistance = $remainingTime * 5;
            $attendants = $station->passengers;

            foreach (range(1, 10) as $i) {
                $bearing = fake()->randomElement([
                    0, 90, 180, 270,
                ]);
                [$newLat, $newLng] = $this->movePoint($driver->coordinates->lat, $driver->coordinates->lng, 100, 0);

                UpdateDriverLocationEvent::dispatch([
                    'driver_id' => $driver->id,
                    'company_id' => $schedule->company_id,
                    'lng' => $newLng,
                    'lat' => $newLat,
                    'ride_id' => $ride->id,
                    'etas' => [
                        [
                            'stop_id' => $stopId,
                            'distance' => intval($remainingDistance -= ($remainingDistance * ($i / 100))),
                            'duration' => intval($remainingDistance -= ($remainingTime * ($i / 100))),
                        ],
                    ],
                ]);

                usleep($this->updatesDelay);
            }

            if ($this->allowDriverStopSkip) {
                if ($this->chanceIn(7)) {
                    // Simulate skipping the stop
                    Http::withToken($token)
                        ->accept('application/json')
                        ->put(route('api.transport.rides.stops.skip'), ([
                            'station_id' => $station->id,
                            'ride_id' => $ride->id,
                        ]));

                    continue;
                }
            }

            if ($this->allowPassengerStopCancel) {
                $attendants = $station->passengers;
                $cancellingPassenger = $attendants->random();
                $passengerToken = $this->loginUser($cancellingPassenger);

                // todo finish simulation
            }
            // Simulate arriving at the stop
            Http::withToken($token)
                ->accept('application/json')
                ->put(route('api.transport.rides.stops.arrive', [
                    'stop' => $stopId,
                ]));

            sleep($this->stopsDelay);

            $totalDuration += $remainingTime;
            $totalDistance += $remainingDistance;

            if ($this->allowAbsence) {
                if ($this->chanceIn(3)) {
                    $attendants->pop();
                }
            }

            // Simulate departing from the stop
            Http::withToken($token)->accept('application/json')->put(route('api.transport.rides.stops.depart', [
                'stop' => $stopId,
                'attendants' => $attendants->pluck('id')->toArray(),
            ]));
        }

        // Arrive at the final destination
        Http::withToken($token)
            ->accept('application/json')
            ->put(route('api.transport.rides.arrive', $ride->id), [
                'end_lat' => fake()->latitude(25.194453, 25.206731),
                'end_lng' => fake()->longitude(55.244370, 55.259432),
                'duration' => intval($totalDuration),
                'distance' => intval($totalDistance),
            ]);
    }

    public static function movePoint(float $lat, float $lng, float $distance, float $bearing): array
    {
        $R = 6378137.0;
        $latRad = deg2rad($lat);
        $lngRad = deg2rad($lng);
        $bearingRad = deg2rad($bearing);
        $newLatRad = asin(sin($latRad) * cos($distance / $R) +
            cos($latRad) * sin($distance / $R) * cos($bearingRad));
        $newLngRad = $lngRad + atan2(
            sin($bearingRad) * sin($distance / $R) * cos($latRad),
            cos($distance / $R) - sin($latRad) * sin($newLatRad)
        );
        $newLat = rad2deg($newLatRad);
        $newLng = rad2deg($newLngRad);

        return [$newLat, $newLng];
    }

    public function chanceIn(int $number = 2): bool
    {
        return rand(0, 10) % $number === 0;
    }

    public function loginUser(User $user): string
    {
        $response = Http::post(route('api.auth.otp.verify'), [
            'phone' => $user->phone,
            'otp' => '0000',
            'package' => $user->isDriver() ? 'yosr-driver' : 'yosr',
        ]);

        return $response->json('data.token');
    }
}
