<?php

namespace App\Repositories\Transport;

use App\Models\Transport\Passenger;
use Illuminate\Support\Collection;

class PassengerRepository
{
    public function findMany(array $ids, array $with = []): Collection
    {
        return Passenger::whereIn('id', $ids)->with($with)->get();
    }

    public function find(int $id): ?Passenger
    {
        return Passenger::find($id);
    }

    public function create(array $data): Passenger
    {
        $passenger = new Passenger;
        $passenger->fill($data);
        $passenger->save();

        return $passenger;
    }

    public function update(Passenger $passenger, array $data): void
    {
        $passenger->fill($data);
        $passenger->save();
    }

    public function delete(Passenger $passenger): void
    {
        $passenger->media()->delete();
        $passenger->delete();
    }
}
