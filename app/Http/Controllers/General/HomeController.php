<?php

namespace App\Http\Controllers\General;

use App\Enums\Transport\RideStatus;
use App\Http\Controllers\Controller;
use App\Models\Fleet\Vehicle;
use App\Models\Transport\Ride;
use App\Services\Transport\RideService;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __construct(private readonly RideService $rideService) {}

    public function index(Authenticatable $user)
    {
        $today = Carbon::now();
        $nextFiveDays = $today->copy()->addDays(5);

        $startedRides = Ride::where('company_id', $user->company_id)
            ->where('status', RideStatus::Ongoing)
            ->with(['driver', 'vehicle'])
            ->get();

        $scheduledRides = $this->rideService->getScheduledRides($user, $today, $nextFiveDays)
            ->load(['driver', 'vehicle', 'schedule']);

        $vehicles = Vehicle::where('company_id', $user->company_id)->get();

        return Inertia::render('Home/Main', [
            'rides' => [
                'ongoing_rides' => $startedRides,
                'next_rides' => $scheduledRides,
            ],
            'vehicles' => $vehicles,
        ]);
    }

    public function personalSettings(): Response
    {
        return Inertia::render('Settings/Personal/Main');
    }
}
