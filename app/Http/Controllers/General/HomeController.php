<?php

namespace App\Http\Controllers\General;

use App\Enums\Transport\RideStatus;
use App\Http\Controllers\Controller;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\Incident\Incident;
use App\Models\Plan\Absence;
use App\Models\Review\Review;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Services\Transport\RideService;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __construct(private readonly RideService $rideService) {}

    public function index(Authenticatable $user)
    {
        $today = Carbon::now();
        $nextFiveDays = $today->copy()->addDays(5);

        $startedRides = Ride::where('company_id', $user->company_id)
            ->where('status', RideStatus::Ongoing)
            ->with(['driver', 'vehicle'])
            ->get();

        $scheduledRides = $this->rideService->getScheduledRides($user, $today, $nextFiveDays)
            ->load(['driver', 'vehicle', 'schedule']);

        $todayIncidents = Incident::where('company_id', $user->company_id)
            ->whereDate('created_at', $today)
            ->with(['reporter'])
            ->latest()
            ->take(5)
            ->get();

        $todayAbsences = Absence::where('company_id', $user->company_id)
            ->whereDate('from', '<=', $today)
            ->whereDate('to', '>=', $today)
            ->with(['passenger'])
            ->latest()
            ->get();

        $topDrivers = Driver::where('company_id', $user->company_id)
            ->take(5)
            ->get();

        $recentReviews = Review::whereHas('ride', function ($query) use ($user) {
            $query->where('company_id', $user->company_id);
        })
            ->with(['reviewer'])
            ->latest()
            ->take(10)
            ->get();

        return Inertia::render('Home/Main', [
            'rides' => [
                'ongoing_rides' => $startedRides,
                'next_rides' => $scheduledRides,
            ],
            'todayIncidents' => $todayIncidents,
            'todayAbsences' => $todayAbsences,
            'topDrivers' => $topDrivers,
            'recentReviews' => $recentReviews,
            'dashboardStats' => [
                'passengers_count' => Passenger::where('company_id', $user->company_id)->count(),
                'drivers_count' => Driver::where('company_id', $user->company_id)->count(),
                'vehicles_count' => Vehicle::where('company_id', $user->company_id)->count(),
                'assistants_count' => Assistant::where('company_id', $user->company_id)->count(),
                'incidents_today_count' => Incident::where('company_id', $user->company_id)
                    ->whereDate('created_at', $today)->count(),
                'absences_today_count' => Absence::where('company_id', $user->company_id)
                    ->whereDate('from', '<=', $today)
                    ->whereDate('to', '>=', $today)->count(),
            ],
        ]);
    }

    public function personalSettings(): Response
    {
        return Inertia::render('Settings/Personal/Main');
    }
}
