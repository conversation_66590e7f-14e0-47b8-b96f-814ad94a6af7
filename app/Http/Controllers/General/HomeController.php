<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use App\Services\General\HomeService;
use Illuminate\Contracts\Auth\Authenticatable;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __construct(private readonly HomeService $homeService) {}

    public function index(Authenticatable $user): Response
    {
        return Inertia::render('Home/Main', [
            'rides' => $this->homeService->getRidesData($user),
            'todayIncidents' => $this->homeService->getTodayIncidents($user),
            'todayAbsences' => $this->homeService->getTodayAbsences($user),
            'topDrivers' => $this->homeService->getTopDrivers($user),
            'topVehicles' => $this->homeService->getTopVehicles($user),
            'topAssistants' => $this->homeService->getTopAssistants($user),
            'recentReviews' => $this->homeService->getRecentReviews($user),
            'dashboardStats' => $this->homeService->getDashboardStats($user),
        ]);
    }

    public function personalSettings(): Response
    {
        return Inertia::render('Settings/Personal/Main');
    }
}
