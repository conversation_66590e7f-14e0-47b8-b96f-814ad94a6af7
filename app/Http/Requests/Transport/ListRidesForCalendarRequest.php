<?php

namespace App\Http\Requests\Transport;

use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRidesForCalendarRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'route_id' => [
                'nullable',
                'integer',
                'exists:routes,id',
            ],
            'driver_id' => [
                'nullable',
                'integer',
                'exists:drivers,id',
            ],
            'vehicle_id' => [
                'nullable',
                'integer',
                'exists:vehicles,id',
            ],
            'from' => [
                'nullable',
                'date',
            ],
            'to' => [
                'nullable',
                'date',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(RideStatus::getValues()),
            ],
            'mode' => [
                'nullable',
                'string',
                Rule::in(RideMode::getValues()),
            ],
            'assistant_id' => [
                'nullable',
                'integer',
                'exists:assistants,id',
            ],
            'search' => [
                'nullable',
                'string',
            ],
        ];
    }

    public function getFilters(?Collection $rides = null): array
    {
        $filters = $this->validated();

        if (! isset($filters['driver_id']) && $rides) {
            $driverId = $rides->first()?->driver_id;
            if ($driverId) {
                $filters['driver_id'] = $driverId;
            }
        }

        if (isset($filters['from'])) {
            $filters['from'] = Carbon::parse($filters['from'])->startOfDay();
        }

        if (isset($filters['to'])) {
            $filters['to'] = Carbon::parse($filters['to'])->endOfDay();
        }

        return $filters;
    }
}
