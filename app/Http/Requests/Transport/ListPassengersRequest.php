<?php

namespace App\Http\Requests\Transport;

use App\Enums\Auth\UserGender;
use App\Enums\Transport\PassengerStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListPassengersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(PassengerStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'responsible_id' => [
                'nullable',
                'integer',
                'exists:responsibles,id',
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'group_id' => [
                'nullable',
                'integer',
                'exists:groups,id',
            ],
            'route_id' => [
                'nullable',
                'integer',
                'exists:routes,id',
            ],
        ];
    }
}
