<?php

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class AddResponsiblePassengersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'passengers_ids' => [
                'required',
                'array',
            ],
            'passengers_ids.*' => [
                'integer',
                'exists:passengers,id',
            ],
        ];
    }
}
