<?php

namespace App\Http\Requests\Transport;

use App\Enums\Auth\UserGender;
use App\Enums\Auth\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListResponsiblesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(UserStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'passenger_id' => [
                'nullable',
                'integer',
                'exists:passengers,id',
            ],
        ];
    }
}
