<?php

namespace App\Http\Requests\Api\Settings;

use App\Enums\General\Models;
use App\Enums\General\ModelType;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateImageApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'image' => [
                'required',
                'image',
                'max:2048',
            ],
            'user_id' => [
                'required',
                'integer',
            ],
            'user_type' => [
                'required',
                Rule::in([
                    Models::Driver,
                    Models::Passenger,
                    Models::Assistant,
                ]),
            ],
        ];
    }

    public function getSubjectUser(): Passenger|Driver|Assistant
    {
        return ModelType::getValue($this->user_type)::findOrFail($this->user_id);
    }
}
