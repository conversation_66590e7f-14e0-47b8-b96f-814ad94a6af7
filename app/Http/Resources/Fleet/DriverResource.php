<?php

namespace App\Http\Resources\Fleet;

use App\Enums\General\Models;
use App\Http\Resources\General\CompanyResource;
use App\Http\Resources\General\RegionResource;
use App\Http\Resources\Transport\RideResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DriverResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'reference' => $this->reference,
            'name' => $this->name,
            'rating' => $this->rating,
            'phone' => $this->phone,
            'image' => $this->image,
            'status' => $this->status,
            'locale' => $this->locale,
            'region_id' => $this->region_id,
            'default_vehicle_id' => $this->default_vehicle_id,
            'coordinates' => $this->coordinates?->toArray(),
            'ride' => RideResource::make($this->whenLoaded('ride')),
            'region' => RegionResource::make($this->whenLoaded('region')),
            'defaultVehicle' => VehicleResource::make($this->whenLoaded('defaultVehicle')),
            'vehicle' => VehicleResource::make($this->whenLoaded('vehicle')),
            'company' => CompanyResource::make($this->whenLoaded('company')),
            'company_id' => $this->company_id,
            'model' => Models::Driver,
        ];
    }
}
