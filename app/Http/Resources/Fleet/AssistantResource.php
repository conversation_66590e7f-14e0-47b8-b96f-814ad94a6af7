<?php

namespace App\Http\Resources\Fleet;

use App\Enums\General\Models;
use App\Http\Resources\General\CompanyResource;
use App\Http\Resources\General\RegionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssistantResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'model' => Models::Assistant,
            'reference' => $this->reference,
            'name' => $this->name,
            'phone' => $this->phone,
            'image' => $this->image,
            'status' => $this->status,
            'locale' => $this->locale,
            'region_id' => $this->region_id,
            'company_id' => $this->company_id,
            'region' => RegionResource::make($this->whenLoaded('region')),
            'company' => CompanyResource::make($this->whenLoaded('company')),
        ];
    }
}
