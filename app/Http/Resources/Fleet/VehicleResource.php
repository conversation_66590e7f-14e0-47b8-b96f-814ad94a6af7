<?php

namespace App\Http\Resources\Fleet;

use App\Http\Resources\General\RegionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'reference' => $this->reference,
            'name' => $this->name,
            'type' => $this->type,
            'image' => $this->image,
            'status' => $this->status,
            'plate' => $this->plate,
            'capacity' => $this->capacity,
            'region_id' => $this->region_id,
            'company_id' => $this->company_id,
            'region' => RegionResource::make($this->whenLoaded('region')),
        ];
    }
}
