<?php

namespace App\Http\Resources\General;

use App\Http\Resources\Geo\LocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'reference' => $this->reference,
            'logo' => $this->logo,
            'locale' => $this->locale,
            'location_id' => $this->location_id,
            'location' => LocationResource::make($this->whenLoaded('location')),
        ];
    }
}
