<?php

namespace App\Http\Resources\Auth;

use App\Enums\General\Models;
use App\Http\Resources\Fleet\AssistantResource;
use App\Http\Resources\Fleet\DriverResource;
use App\Http\Resources\Transport\PassengerResource;
use App\Http\Resources\Transport\ResponsibleResource;
use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'position' => $this->position,
            'phone' => $this->phone,
            'email' => $this->email,
            'locale' => $this->locale,
            'status' => $this->status,
            'image' => $this->image,
            'model' => Models::User,
            'company_id' => $this->company_id,
            'company' => $this->whenLoaded('company'),
        ];
    }

    public static function getResource($resource): DriverResource|UserResource|PassengerResource|AssistantResource|ResponsibleResource
    {
        return match ($resource::class) {
            User::class => UserResource::make($resource),
            Passenger::class => PassengerResource::make($resource),
            Driver::class => DriverResource::make($resource),
            Responsible::class => ResponsibleResource::make($resource),
            Assistant::class => AssistantResource::make($resource),
        };
    }
}
