<?php

namespace App\Http\Resources\Auth;

use App\Http\Resources\Geo\LocationResource;
use App\Http\Resources\Plan\AttendanceResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserGlobalResource extends JsonResource
{
    public function __construct($resource) {}

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'image' => $this->image,
            'email' => $this->email,
            'phone' => $this->phone,
            'attendance' => AttendanceResource::make($this->whenLoaded('attendance')),
            'location' => LocationResource::make($this->whenLoaded('location')),
        ];
    }
}
