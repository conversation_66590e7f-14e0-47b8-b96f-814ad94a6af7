<?php

namespace App\Http\Resources\Analytics;

use Illuminate\Http\Resources\Json\JsonResource;

class ReportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'key' => $this->resource['key'],
            'type' => $this->resource['type'],
            'icon' => $this->resource['icon'] ?? null,
            'withChart' => $this->resource['withChart'],
            'data' => $this->resource['data'],
        ];
    }
}
