<?php

namespace App\Http\Resources\Transport;

use App\Http\Resources\Fleet\DriverResource;
use App\Http\Resources\Fleet\VehicleResource;
use App\Http\Resources\Plan\AttendanceResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RideResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $duration = isset($this->arrived_at) ? $this->arrived_at->diffInMinutes($this->started_at, true) : null;
        $averageSpeed = $duration ? round($this->distance / 1000, 2) / ($duration / 60) : null;

        return [
            'id' => $this->id,
            'status' => $this->status,
            'note' => $this->note,
            'schedule_id' => $this->schedule_id,
            'distance' => $this->distance,
            'mode' => $this->mode,
            'duration' => intval($duration),
            'start_lat' => $this->start_lat,
            'start_lng' => $this->start_lng,
            'average_speed' => intval($averageSpeed),
            'end_lat' => $this->end_lat,
            'end_lng' => $this->end_lng,
            'started_at' => $this->started_at,
            'arrived_at' => $this->arrived_at,
            'route_id' => $this->route_id,
            'driver_id' => $this->driver_id,
            'vehicle_id' => $this->vehicle_id,
            'assistant_id' => $this->assistant_id,
            'company_id' => $this->company_id,
            'route' => RouteResource::make($this->whenLoaded('route')),
            'driver' => DriverResource::make($this->whenLoaded('driver')),
            'vehicle' => VehicleResource::make($this->whenLoaded('vehicle')),
            'stops' => StopResource::collection($this->whenLoaded('stops')),
            'attendances' => AttendanceResource::collection($this->whenLoaded('attendances')),
            'attendances_count' => $this->whenCounted('attendances'),
            'present_passengers_count' => $this->whenCounted('presentPassengers'),
            'passengers_count' => $this->whenCounted('passengers'),
            'stations_count' => $this->whenCounted('stations'),
            'stops_count' => $this->whenCounted('stops'),
            'stations' => StationResource::collection($this->whenLoaded('stations')),
            'stop' => $this->stop ?? null,
        ];
    }
}
