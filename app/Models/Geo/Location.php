<?php

namespace App\Models\Geo;

use App\Models\BaseModel;
use App\Models\Company\Company;
use App\Services\General\ImagePreviewService;
use App\Support\Coordinates;
use App\Traits\General\HasCompany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property string|null $street_line_1
 * @property string|null $street_line_2
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postal_code
 * @property float $lat
 * @property float $lng
 * @property bool|null $primary
 * @property string|null $model_type
 * @property int|null $model_id
 * @property string $creator_type
 * @property int $creator_id
 * @property int $company_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Company|null $company
 * @property-read Model $creator
 * @property-read Coordinates $coordinates
 * @property-read Model $model
 */
class Location extends BaseModel
{
    use HasCompany;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'country',
        'street_line_1',
        'street_line_2',
        'city',
        'state',
        'postal_code',
        'lat',
        'lng',
        'creator_type',
        'creator_id',
        'primary',
        'model_type',
        'model_id',
        'company_id',
    ];

    protected $casts = [
        'lat' => 'double',
        'lng' => 'double',
        'primary' => 'boolean',
    ];

    protected $appends = [
        'image',
    ];

    public function model(): MorphTo
    {
        return $this->morphTo('model');
    }

    public function scopeFilter($query, $filter): mixed
    {
        if (isset($filter['search'])) {
            $query->where(function ($q) use ($filter) {
                $searchTerm = '%'.$filter['search'].'%';

                $q->orWhere('name', 'ilike', $searchTerm)
                    ->orWhere('street_line_1', 'ilike', $searchTerm)
                    ->orWhere('street_line_2', 'ilike', $searchTerm)
                    ->orWhere('city', 'ilike', $searchTerm)
                    ->orWhere('state', 'ilike', $searchTerm)
                    ->orWhere('postal_code', 'ilike', $searchTerm);
            });
        }

        if (isset($filter['model_ids'])) {
            $query->whereIn('model_id', $filter['model_ids']);
        }

        if (isset($filter['primary'])) {
            $query->where('primary', true);
        }

        return $query;
    }

    public function getNameAttribute(): string
    {
        return $this->attributes['name'] ?? $this->street_line_1 ?? '#'.$this->id;
    }

    public function scopePrimary(Builder $query): Builder
    {
        return $query->where('primary', true);
    }

    public function getCoordinatesAttribute(): Coordinates
    {
        return new Coordinates($this->lat, $this->lng);
    }

    public function creator(): MorphTo
    {
        return $this->morphTo('creator');
    }

    public function isPrimary(): bool
    {
        return (bool) $this->primary;
    }

    public function getImageAttribute(): string
    {
        return app(ImagePreviewService::class)->getImageUrl($this->lat, $this->lng);
    }
}
