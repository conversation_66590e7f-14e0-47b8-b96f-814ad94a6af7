<?php

namespace App\Models\Transport;

use App\Models\Company\Company;
use App\Models\General\Media;
use App\Models\General\Region;
use App\Traits\General\AppUser;
use App\Traits\General\HasCompany;
use App\Traits\General\HasImage;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

/**
 * @property int $id
 * @property string|null $reference
 * @property string $first_name
 * @property string $last_name
 * @property string|null $birthdate
 * @property string|null $gender
 * @property string|null $email
 * @property string $phone
 * @property string|null $channel
 * @property string $locale
 * @property string|null $status
 * @property int|null $region_id
 * @property int $company_id
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Company|null $company
 * @property-read string $image
 * @property-read string $name
 * @property-read MediaCollection<int, Media> $media
 * @property-read int|null $media_count
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection<int, Passenger> $passengers
 * @property-read int|null $passengers_count
 * @property-read Region|null $region
 * @property-read Collection<int, PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 */
class Responsible extends Authenticatable implements HasMedia
{
    use AppUser;
    use HasApiTokens;
    use HasCompany;
    use HasFactory;
    use HasImage;
    use Notifiable;
    use SoftDeletes;

    protected $fillable = [
        'company_id',
        'reference',
        'first_name',
        'last_name',
        'birthdate',
        'phone',
        'email',
        'password',
        'channel',
        'status',
        'locale',
        'region_id',
        'gender',
        'birthdate',
    ];

    protected $appends = [
        'image',
        'name',
    ];

    protected $hidden = [
        'remember_token',
    ];

    protected $casts = [];

    public function scopeFilter($query, $filter): mixed
    {
        if (isset($filter['search'])) {
            $query->where(function ($q) use ($filter) {
                $searchTerm = '%'.$filter['search'].'%';

                $q->orWhere('first_name', 'ilike', $searchTerm)
                    ->orWhere('last_name', 'ilike', $searchTerm)
                    ->orWhere('email', 'ilike', $searchTerm)
                    ->orWhere('phone', 'ilike', $searchTerm)
                    ->orWhere('reference', 'ilike', $searchTerm);
            });
        }

        if (isset($filter['status'])) {
            $query->where('status', $filter['status']);
        }

        if (isset($filter['gender'])) {
            $query->where('gender', $filter['gender']);
        }

        if (isset($filter['region_id'])) {
            $query->where('region_id', $filter['region_id']);
        }

        if (isset($filter['passenger_id'])) {
            $query->whereHas('passengers', function ($q) use ($filter) {
                $q->where('passengers.id', $filter['passenger_id']);
            });
        }

        return $query;
    }

    public function passengers(): BelongsToMany
    {
        return $this->belongsToMany(Passenger::class, PassengerResponsible::class);
    }

    public function passengerResponsibles(): HasMany
    {
        return $this->hasMany(PassengerResponsible::class);
    }

    public function receivesBroadcastNotificationsOn(): string
    {
        return 'responsibles.'.$this->id;
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    public function hasPassenger(Passenger $passenger): bool
    {
        return $this->passengers()->where('passengers.id', $passenger->id)->exists();
    }
}
