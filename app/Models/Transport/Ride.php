<?php

namespace App\Models\Transport;

use App\Enums\Plan\AttendanceStatus;
use App\Enums\Transport\RideMode;
use App\Models\BaseModel;
use App\Models\Company\Company;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\General\Region;
use App\Models\Incident\Incident;
use App\Models\Plan\Attendance;
use App\Models\Plan\Schedule;
use App\Models\Review\Review;
use App\Traits\General\HasCompany;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $status
 * @property string|null $note
 * @property string $mode
 * @property Carbon $started_at
 * @property Carbon|null $arrived_at
 * @property Carbon|null $scheduled_at
 * @property string $start_lat
 * @property string $start_lng
 * @property string|null $end_lat
 * @property string|null $end_lng
 * @property int|null $distance
 * @property int|null $reason_id
 * @property int $route_id
 * @property int $driver_id
 * @property int $vehicle_id
 * @property int|null $schedule_id
 * @property int $company_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, Passenger> $absentPassengers
 * @property-read int|null $absent_passengers_count
 * @property-read Assistant|null $assistant
 * @property-read Collection<int, Attendance> $attendances
 * @property-read int|null $attendances_count
 * @property-read Company|null $company
 * @property-read Driver|null $driver
 * @property-read Collection<int, Incident> $incidents
 * @property-read int|null $incidents_count
 * @property-read Collection<int, Passenger> $noShowPassengers
 * @property-read int|null $no_show_passengers_count
 * @property-read Collection<int, Passenger> $passengers
 * @property-read int|null $passengers_count
 * @property-read Collection<int, Passenger> $presentPassengers
 * @property-read int|null $present_passengers_count
 * @property-read Region|null $region
 * @property-read Collection<int, Review> $reviews
 * @property-read int|null $reviews_count
 * @property-read Route|null $route
 * @property-read Schedule|null $schedule
 * @property-read Collection<int, Stop> $stops
 * @property-read int|null $stops_count
 * @property-read Vehicle|null $vehicle
 */
class Ride extends BaseModel
{
    use HasCompany;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'status',
        'note',
        'mode',
        'start_lat',
        'start_lng',
        'end_lat',
        'end_lng',
        'started_at',
        'arrived_at',
        'scheduled_at',
        'route_id',
        'driver_id',
        'assistant_id',
        'reason_id',
        'vehicle_id',
        'schedule_id',
        'company_id',
        'distance',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'arrived_at' => 'datetime',
        'scheduled_at' => 'datetime',
    ];

    protected $appends = [
        'created_at_diff',
        'average_speed',
    ];

    public function route(): BelongsTo
    {
        return $this->belongsTo(Route::class);
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function stops(): HasMany
    {
        return $this->hasMany(Stop::class)->orderBy('scheduled_at');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class, 'schedule_id', 'schedule_id');
    }

    public function hasDriver(Driver $driver): bool
    {
        return $this->driver_id === $driver->id;
    }

    public function hasPassenger(Passenger $passenger): bool
    {
        return $this->route->hasPassenger($passenger);
    }

    public function hasResponsible(Responsible $responsible): bool
    {
        return $this->route->hasResponsible($responsible);
    }

    public function hasAssistant(Assistant $assistant): bool
    {
        return $this->schedule->assistant_id === $assistant->id;
    }

    public function isDropOff(): bool
    {
        return $this->mode === RideMode::DropOff;
    }

    public function isPickUp(): bool
    {
        return $this->mode === RideMode::PickUp;
    }

    public function scopeBasic($query)
    {
        return $query->select([
            'started_at',
            'arrived_at',
            'scheduled_at',
            'route_id',
            'driver_id',
            'assistant_id',
            'reason_id',
            'vehicle_id',
            'schedule_id',
            'company_id',
        ]);
    }

    public function scopeFilter($query, $filter): mixed
    {
        if (isset($filter['search'])) {
            $searchTerm = $filter['search'];

            $query->where(function ($q) use ($searchTerm) {
                if (preg_match('/^#\d+$/', $searchTerm)) {
                    $id = (int) substr($searchTerm, 1);
                    $q->where('id', $id);
                } elseif (is_numeric($searchTerm)) {
                    $q->orWhere('id', $searchTerm);
                } else {
                    $searchTerm = '%'.$searchTerm.'%';
                    $q->where('note', 'ilike', $searchTerm);
                }
            });
        }

        if (isset($filter['mode'])) {
            $query->where('mode', $filter['mode']);
        }

        if (isset($filter['status'])) {
            $query->where('status', $filter['status']);
        }

        if (isset($filter['driver_id'])) {
            $query->where('driver_id', $filter['driver_id']);
        }

        if (isset($filter['route_id'])) {
            $query->where('route_id', $filter['route_id']);
        }

        if (isset($filter['vehicle_id'])) {
            $query->where('vehicle_id', $filter['vehicle_id']);
        }

        if (isset($filter['from']) && isset($filter['to'])) {
            $from = $filter['from'];
            $to = $filter['to'];

            $query->where(function ($q) use ($from, $to) {
                $q->whereBetween('started_at', [$from, $to])
                    ->orWhere(function ($q) use ($from, $to) {
                        $q->where('started_at', '<', $from)
                            ->where('arrived_at', '>', $to);
                    });
            });
        }

        if (isset($filter['day'])) {
            $query->whereDate('created_at', Carbon::make($filter['day']));
        }

        return $query;
    }

    public function passengers(): HasManyThrough
    {
        return $this->hasManyThrough(Passenger::class, Attendance::class, 'ride_id', 'id', null, 'passenger_id');
    }

    public function presentPassengers(): HasManyThrough
    {
        return $this->passengers()->where('attendances.status', AttendanceStatus::Present);
    }

    public function canceledPassengers(): HasManyThrough
    {
        return $this->passengers()->where('attendances.status', AttendanceStatus::Canceled);
    }

    public function noShowPassengers(): HasManyThrough
    {
        return $this->passengers()->where('attendances.status', AttendanceStatus::NoShow);
    }

    public function absentPassengers(): HasManyThrough
    {
        return $this->passengers()->where('attendances.status', AttendanceStatus::Absent);
    }

    public function incidents(): HasMany
    {
        return $this->hasMany(Incident::class);
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function assistant(): BelongsTo
    {
        return $this->belongsTo(Assistant::class);
    }

    public function rating()
    {
        return $this->reviews()->average('rating');
    }

    public function region(): HasOneThrough
    {
        return $this->hasOneThrough(Region::class, Driver::class);
    }

    public function stations(): HasManyThrough
    {
        return $this->hasManyThrough(Station::class, Route::class, 'id', 'route_id', 'route_id', 'id');
    }

    public function getCreatedAtDiffAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    public function getAverageSpeedAttribute(): float
    {
        if (! $this->arrived_at || ! $this->started_at || ! $this->distance) {
            return 0;
        }

        $durationInMinutes = $this->started_at->diffInMinutes($this->arrived_at);

        if ($durationInMinutes <= 0) {
            return 0;
        }

        $durationInHours = $durationInMinutes / 60;
        $distanceInKm = $this->distance / 1000;

        return round($distanceInKm / $durationInHours, 2);
    }
}
