<?php

namespace App\Models\Transport;

use App\Enums\Transport\StopStatus;
use App\Models\BaseModel;
use App\Models\Company\Company;
use App\Traits\General\HasCompany;
use Eloquent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $status
 * @property int $ride_id
 * @property int $station_id
 * @property Carbon|null $departed_at
 * @property Carbon|null $arrived_at
 * @property Carbon|null $scheduled_at
 * @property int $company_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Company|null $company
 * @property-read Ride|null $ride
 * @property-read Station|null $station
 *
 * @mixin Eloquent
 */
class Stop extends BaseModel
{
    use HasCompany;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'status',
        'ride_id',
        'station_id',
        'departed_at',
        'scheduled_at',
        'arrived_at',
        'company_id',
    ];

    protected $casts = [
        'departed_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'arrived_at' => 'datetime',
    ];

    public function ride(): BelongsTo
    {
        return $this->belongsTo(Ride::class);
    }

    public function station(): BelongsTo
    {
        return $this->belongsTo(Station::class);
    }

    public function isScheduled(): bool
    {
        return $this->status === StopStatus::Scheduled;
    }

    public function isSkipped(): bool
    {
        return $this->status === StopStatus::Skipped;
    }

    public function isDeparted(): bool
    {
        return $this->status === StopStatus::Departed;
    }
}
