<?php

namespace App\Models\Transport;

use App\Models\Company\Company;
use App\Models\General\Media;
use App\Models\General\Region;
use App\Models\Geo\Location;
use App\Models\Plan\Absence;
use App\Models\Plan\Attendance;
use App\Traits\General\AppUser;
use App\Traits\General\HasCompany;
use App\Traits\General\HasImage;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

/**
 * @property int $id
 * @property string|null $reference
 * @property string $first_name
 * @property string $last_name
 * @property string|null $birthdate
 * @property string|null $gender
 * @property string|null $email
 * @property string $phone
 * @property string|null $channel
 * @property string $locale
 * @property string $status
 * @property int|null $region_id
 * @property int|null $group_id
 * @property int $company_id
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Collection<int, Attendance> $attendances
 * @property-read int|null $attendances_count
 * @property-read Company|null $company
 * @property-read string $image
 * @property-read string $name
 * @property-read Group|null $group
 * @property-read Location|null $location
 * @property-read Collection<int, Location> $locations
 * @property-read int|null $locations_count
 * @property-read MediaCollection<int, Media> $media
 * @property-read int|null $media_count
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Region|null $region
 * @property-read Collection<int, Responsible> $responsibles
 * @property-read int|null $responsibles_count
 * @property-read Collection<int, Ride> $rides
 * @property-read int|null $rides_count
 * @property-read Collection<int, Route> $routes
 * @property-read int|null $routes_count
 * @property-read Collection<int, Station> $stations
 * @property-read int|null $stations_count
 * @property-read Collection<int, Absence> $absences
 * @property-read int|null $absences_count
 * @property-read Collection<int, PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 */
class Passenger extends Authenticatable implements HasMedia
{
    use AppUser;
    use HasApiTokens;
    use HasCompany;
    use HasFactory;
    use HasImage;
    use Notifiable;
    use SoftDeletes;

    protected $fillable = [
        'company_id',
        'reference',
        'first_name',
        'last_name',
        'birthdate',
        'gender',
        'phone',
        'email',
        'password',
        'channel',
        'status',
        'locale',
        'group_id',
        'region_id',
    ];

    protected $appends = [
        'image',
        'name',
    ];

    protected $hidden = [
        'remember_token',
    ];

    protected $casts = [];

    public function scopeFilter($query, $filter): mixed
    {
        if (isset($filter['search'])) {
            $query->where(function ($q) use ($filter) {
                $searchTerm = '%'.$filter['search'].'%';

                $q->orWhere('first_name', 'ilike', $searchTerm)
                    ->orWhere('last_name', 'ilike', $searchTerm)
                    ->orWhere('email', 'ilike', $searchTerm)
                    ->orWhere('reference', 'ilike', $searchTerm)
                    ->orWhere('phone', 'ilike', $searchTerm);
            });
        }

        if (isset($filter['status'])) {
            $query->where('status', $filter['status']);
        }

        if (isset($filter['gender'])) {
            $query->where('gender', $filter['gender']);
        }

        if (isset($filter['region_id'])) {
            $query->where('region_id', $filter['region_id']);
        }

        if (isset($filter['group_id'])) {
            $query->where('group_id', $filter['group_id']);
        }

        if (isset($filter['route_id'])) {
            $query->whereHas('routes', function ($q) use ($filter) {
                $q->where('routes.id', $filter['route_id']);
            });
        }

        if (isset($filter['responsible_id'])) {
            $query->whereHas('responsibles', function ($q) use ($filter) {
                $q->where('responsibles.id', $filter['responsible_id']);
            });
        }

        return $query;
    }

    public function locations(): MorphMany
    {
        return $this->morphMany(Location::class, 'model');
    }

    public function location(): MorphOne
    {
        return $this->morphOne(Location::class, 'model')->where('primary', true);
    }

    public function receivesBroadcastNotificationsOn(): string
    {
        return 'passengers.'.$this->id;
    }

    public function stations(): HasManyThrough
    {
        return $this->hasManyThrough(Station::class, StationPassenger::class, 'passenger_id', 'id', null, 'station_id');
    }

    public function stops(): HasManyThrough
    {
        return $this->hasManyThrough(
            Stop::class,
            StationPassenger::class,
            'passenger_id',
            'station_id',
            'id',
            'station_id'
        );
    }

    public function routes(): HasManyThrough
    {
        return $this->hasManyThrough(Route::class, StationPassenger::class, 'passenger_id', 'id', null, 'route_id');
    }

    public function rides(): HasManyThrough
    {
        return $this->hasManyThrough(ride::class, Attendance::class, 'passenger_id', 'id', 'id', 'ride_id');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function responsibles(): BelongsToMany
    {
        return $this->belongsToMany(Responsible::class, PassengerResponsible::class);
    }

    public function hasResponsible(Responsible $responsible): bool
    {
        return $this->responsibles()->where('responsibles.id', $responsible->id)->exists();
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function absences(): HasMany
    {
        return $this->hasMany(Absence::class);
    }
}
