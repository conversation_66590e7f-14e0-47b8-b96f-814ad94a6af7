<?php

namespace App\Models\Transport;

use App\Models\Auth\User;
use App\Models\BaseModel;
use App\Models\Company\Company;
use App\Models\Geo\Location;
use App\Traits\General\HasCompany;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string|null $name
 * @property int $order
 * @property int $route_id
 * @property int $location_id
 * @property int $time
 * @property int $distance
 * @property int $company_id
 * @property int $created_by
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Company|null $company
 * @property-read User|null $createdBy
 * @property-read Location|null $location
 * @property-read Collection<int, Passenger> $passengers
 * @property-read int|null $passengers_count
 */
class Station extends BaseModel
{
    use HasCompany;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'location_id',
        'created_by',
        'route_id',
        'order',
        'duration',
        'distance',
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeFilter($query, array $filters): void
    {
        if (isset($filters['search'])) {
            $query->where('name', 'ilike', '%'.$filters['search'].'%');
        }
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function passengers(): BelongsToMany
    {
        return $this->belongsToMany(
            Passenger::class,
            StationPassenger::class,
            'station_id',
            'passenger_id'
        )->withPivot('seat');
    }
}
