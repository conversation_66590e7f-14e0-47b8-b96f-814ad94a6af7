<?php

namespace App\Providers;

use App\Enums\General\ModelType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        Paginator::useBootstrapFive();

        Model::preventLazyLoading(app()->isLocal());

        Gate::before(function () {
            if (auth()->user()->isSuperAdmin()) {
                return true;
            }
        });
    }

    public function boot(): void
    {
        Relation::enforceMorphMap(ModelType::asArray());
    }
}
