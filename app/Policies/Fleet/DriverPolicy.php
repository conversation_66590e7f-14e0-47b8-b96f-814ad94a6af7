<?php

namespace App\Policies\Fleet;

use App\Enums\General\Models;
use App\Models\Fleet\Driver;
use Illuminate\Contracts\Auth\Authenticatable;

class DriverPolicy
{
    public function hasAccess(Authenticatable $user, Driver $driver): bool
    {
        return match ($user->type()) {
            Models::User => $user->company_id === $driver->company_id,
            Models::Driver => $user->id === $driver->id,
            default => false
        };
    }

    public function list(Authenticatable $user): bool
    {
        return $user->isUser();
    }

    public function view(Authenticatable $user, Driver $driver): bool
    {
        return $this->hasAccess($user, $driver);
    }

    public function create(Authenticatable $user): bool
    {
        return $user->isUser();
    }

    public function update(Authenticatable $user, Driver $driver): bool
    {
        return $this->hasAccess($user, $driver);
    }

    public function delete(Authenticatable $user, Driver $driver): bool
    {
        return $this->hasAccess($user, $driver);
    }
}
