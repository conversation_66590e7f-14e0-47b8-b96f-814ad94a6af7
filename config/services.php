<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_AUTH_TOKEN'),
        'whatsapp_from' => env('TWILIO_WHATSAPP_FROM'),
    ],

    'google' => [
        'api_key' => env('GOOGLE_API_KEY'),
    ],

    'here' => [
        'api_key' => env('HERE_API_KEY'),
    ],

    'slack' => [
        'webhook_url' => env('SLACK_WEBHOOK_URL'),
    ],

    'infobip' => [
        'api_key' => env('INFOBIP_API_KEY'),
        'sender' => env('INFOBIP_SENDER'),
    ],

    'mapbox' => [
        'access_token' => env('MAPBOX_ACCESS_TOKEN'),
        'public_access_token' => env('MAPBOX_PUBLIC_ACCESS_TOKEN'),
    ],

    'meta' => [
        'account_id' => env('META_ACCOUNT_ID'),
        'test_account_id' => env('META_TEST_ACCOUNT_ID'),
        'access_token' => env('META_ACCESS_TOKEN'),
    ],

    'newrelic' => [
        'api_key' => env('NEWRELIC_API_KEY'),
        'base_url' => env('NEWRELIC_BASE_URL', 'https://log-api.eu.newrelic.com/log/v1'),
    ],

    'mixpanel' => [
        'token' => env('MIXPANEL_TOKEN'),
    ],

    'onesignal' => [
        'api_key' => env('ONESIGNAL_API_KEY'),
        'app_id' => env('ONESIGNAL_APP_ID'),
        'driver_api_key' => env('ONESIGNAL_DRIVER_API_KEY'),
        'driver_app_id' => env('ONESIGNAL_DRIVER_APP_ID'),
    ],
];
