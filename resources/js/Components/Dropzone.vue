<template>
	<div class="fv-row">
		<div class="dropzone" id="dropzonejs">
			<div class="dz-message needsclick d-flex align-items-center flex-column">
				<i class="fas fa-cloud-upload-alt text-primary fs-3x my-3"></i>
				<h3 class="fs-5 fw-bold text-gray-900 my-3">
					{{ $t('import.drop_file_here') }}
				</h3>
				<span class="fs-7 fw-semibold text-gray-400 my-3">
					{{ $t('import.upload_document') }}
				</span>
			</div>
			<div class="mh-300px overflow-auto" id="previews"></div>
		</div>
	</div>
</template>

<script>
import Dropzone from 'dropzone';
import { router, usePage } from '@inertiajs/vue3';

export default {
	name: 'DropZone',
	props: {
		modelValue: {
			type: Array,
			required: true,
			default: () => []
		},
		acceptedFileTypes: {
			type: String,
			default: null
		},
		maxFiles: {
			type: Number,
			default: null
		}
	},
	data() {
		return {
			myDropzone: null,
			temporaryMediaItems: [],
			removeFromServer: true
		};
	},
	mounted() {
		var vm = this;
		this.myDropzone = new Dropzone('#dropzonejs', {
			url: route('media.temporary.save'),
			headers: {
				'X-CSRF-TOKEN': usePage().props?.csrf
			},
			paramName: 'file',
			addRemoveLinks: true,
			acceptedFiles: this.acceptedFileTypes,
			maxFiles: this.maxFiles,
			previewsContainer: '#previews',
			success: function (file, response) {
				const newItem = {
					id: response.temporary_media_id,
					file_uuid: file.upload.uuid
				};
				vm.$emit('update:modelValue', [newItem.id]);
			},
			removedfile: function (file) {
				if (file.status === 'success') {
					let temporaryFileId = vm.temporaryMediaItems.find(
						(el) => el.file_uuid === file.upload.uuid
					)?.id;
					vm.temporaryMediaItems = vm.temporaryMediaItems.filter(
						(el) => el.file_uuid != file.upload.uuid
					);
					vm.$emit(
						'update:modelValue',
						vm.temporaryMediaItems.map((el) => el.id)
					);
					if (vm.removeFromServer) {
						router.delete(route('media.temporary.delete', temporaryFileId), {
							preserveScroll: true
						});
					}
				}

				if (file.previewElement != null && file.previewElement.parentNode != null) {
					file.previewElement.parentNode.removeChild(file.previewElement);
				}

				return this._updateMaxFilesReachedClass();
			}
		});

		// Load initial file ID if present
		if (this.modelValue.length > 0) {
			const userMedia = usePage().props.user.media;
			const media = userMedia.find((media) => media.id === this.modelValue[0]);
			if (media) {
				let mockFile = {
					name: media.file_name,
					size: media.size,
					type: media.mime_type,
					upload: { uuid: media.file_uuid }
				};
				mockFile.status = Dropzone.SUCCESS;
				this.myDropzone.emit('addedfile', mockFile);
				this.myDropzone.emit('complete', mockFile);
			}
		}
	}
};
</script>

<style scoped></style>
