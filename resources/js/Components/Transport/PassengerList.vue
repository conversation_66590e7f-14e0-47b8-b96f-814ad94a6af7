<template>
	<div v-if="passengers && passengers.length" class="symbol-group symbol-hover">
		<template v-if="passengers.length === 1">
			<div class="d-flex align-items-center">
				<div class="symbol symbol-40px">
					<img :alt="passengers[0].name" :src="passengers[0].image" class="img-fluid" />
				</div>
				<div class="d-flex flex-column align-items-start">
					<div class="ms-2">{{ passengers[0].name }}</div>
					<div
						v-if="showAttendanceStatus && passengers[0].attendanceStatus"
						class="badge badge-sm text-capitalize fs-8 mt-2 ms-2"
						:class="badgeClass(passengers[0].attendanceStatus)">
						{{ $t('general.enums.' + passengers[0]?.attendanceStatus) }}
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<template v-for="(passenger, i) in visiblePassengers" :key="i">
				<template v-if="showNames">
					<div class="d-flex align-items-center me-5">
						<div class="symbol symbol-40px">
							<img :alt="passenger.name" :src="passenger.image" class="img-fluid" />
						</div>
						<div class="d-flex flex-column align-items-start">
							<div class="ms-2">{{ passenger.name }}</div>
							<div
								v-if="showAttendanceStatus && passenger.attendanceStatus"
								class="badge badge-sm text-capitalize fs-8 mt-2 ms-2"
								:class="badgeClass(passenger.attendanceStatus)">
								{{ $t('general.enums.' + passenger?.attendanceStatus) }}
							</div>
						</div>
					</div>
				</template>
				<Tooltip v-else :content="passenger.name">
					<div class="symbol symbol-40px">
						<img :alt="passenger.name" :src="passenger.image" class="img-fluid" />
					</div>
				</Tooltip>
			</template>
			<template v-if="hasHiddenPassengers">
				<Tooltip :content="getPassengerNames(hiddenPassengers)">
					<div class="symbol symbol-40px border">
						<span class="symbol-label font-weight-bold text-primary">
							{{ hiddenPassengersCount }}+
						</span>
					</div>
				</Tooltip>
			</template>
		</template>
	</div>
</template>

<script>
import Tooltip from '@/Components/Tooltip.vue';

export default {
	name: 'PassengerList',
	components: {
		Tooltip
	},
	props: {
		passengers: {
			type: Array,
			required: true
		},
		maxVisible: {
			type: Number,
			default: 3
		},
		showNames: {
			type: Boolean,
			default: false
		},
		showAttendanceStatus: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		visiblePassengers() {
			return this.maxVisible === -1
				? this.passengers
				: this.passengers.slice(0, this.maxVisible);
		},
		hasHiddenPassengers() {
			return this.passengers.length > this.maxVisible && this.maxVisible !== -1;
		},
		hiddenPassengers() {
			return this.passengers.slice(this.maxVisible);
		},
		hiddenPassengersCount() {
			return this.passengers.length - this.maxVisible;
		}
	},
	methods: {
		getPassengerNames(passengers) {
			return passengers.map((p) => p.name).join(' - ');
		},
		badgeClass(status) {
			const classes = {
				present: 'badge-light-success',
				absent: 'badge-light-danger',
				canceled: 'badge-light-dark',
				no_show: 'badge-light-warning',
				skipped: 'badge-light-warning',
				expected: 'badge-light-info'
			};
			return classes[status] || '';
		}
	}
};
</script>
