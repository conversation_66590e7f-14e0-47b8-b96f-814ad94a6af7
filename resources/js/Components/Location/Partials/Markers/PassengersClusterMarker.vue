<template>
	<div></div>
</template>

<script>
import Supercluster from 'supercluster';
import mapboxgl from 'mapbox-gl';
import PassengerMarker from '@/Components/Location/Partials/Markers/PassengerMarker.vue';
import { createVNode, render } from 'vue';

export default {
	name: 'PassengersClusterMarker',
	props: {
		passengers: {
			type: Array,
			required: true
		},
		map: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			supercluster: null,
			markers: {},
			clusters: {},
			markerContainers: {}
		};
	},
	watch: {
		passengers: {
			handler: 'updateClusters',
			deep: true
		}
	},
	mounted() {
		this.initializeClustering();
	},
	beforeUnmount() {
		this.clearMarkers();
		this.map.off('moveend', this.updateClusters);
	},
	methods: {
		initializeClustering() {
			this.supercluster = new Supercluster({
				// 0 means clustering is disabled.
				radius: 0,
				maxZoom: 16
			});

			this.updateClusters();
			this.map.on('moveend', this.updateClusters);
		},
		updateClusters() {
			this.clearMarkers();

			const points = this.passengers.map((passenger) => ({
				type: 'Feature',
				properties: { passenger },
				geometry: {
					type: 'Point',
					coordinates: [passenger.location.lng, passenger.location.lat]
				}
			}));

			this.supercluster.load(points);

			const bounds = this.map.getBounds();
			const zoom = Math.floor(this.map.getZoom());

			const clusters = this.supercluster.getClusters(
				[bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
				zoom
			);

			clusters.forEach((cluster) => {
				if (cluster.properties.cluster) {
					this.createClusterMarker(cluster);
				} else {
					this.createPassengerMarker(cluster.properties.passenger);
				}
			});
		},
		createClusterMarker(cluster) {
			const el = document.createElement('div');
			el.className = 'cluster-marker';

			const content = `
    <div class="cluster-pulse"></div>
    <div class="cluster-inner">
      <span class="cluster-count">${cluster.properties.point_count}</span>
    </div>
  `;

			el.innerHTML = content;

			const marker = new mapboxgl.Marker(el)
				.setLngLat(cluster.geometry.coordinates)
				.addTo(this.map);

			el.addEventListener('click', () => {
				const expansionZoom = this.supercluster.getClusterExpansionZoom(cluster.id);
				this.map.flyTo({
					center: cluster.geometry.coordinates,
					zoom: expansionZoom
				});
			});

			this.clusters[cluster.id] = marker;
		},
		createPassengerMarker(passenger) {
			const container = document.createElement('div');

			const vNode = createVNode(PassengerMarker, {
				passenger,
				'onShow-info': () => this.$emit('show-info', passenger.id),
				'onHide-info': () => this.$emit('hide-info', passenger.id)
			});

			vNode.appContext = this.$root.$?.appContext;
			render(vNode, container);

			const marker = new mapboxgl.Marker({
				element: container,
				anchor: 'center'
			})
				.setLngLat([passenger.location.lng, passenger.location.lat])
				.addTo(this.map);

			this.markers[passenger.id] = marker;
			this.markerContainers[passenger.id] = container;
		},
		clearMarkers() {
			Object.values(this.markers).forEach((marker) => marker.remove());
			Object.values(this.clusters).forEach((marker) => marker.remove());
			Object.values(this.markerContainers).forEach((container) => {
				render(null, container);
			});
			this.markers = {};
			this.clusters = {};
			this.markerContainers = {};
		}
	}
};
</script>

<style>
.cluster-marker {
	position: relative;
	cursor: pointer;
}

.cluster-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 34px;
	height: 34px;
	background: var(--kt-primary);
	/* border: 2px solid #fff; */
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	z-index: 2;
	color: white;
}

.cluster-count {
	/* font-size: 16px; */
	font-weight: bold;
	line-height: 1;
}

.cluster-label {
	font-size: 10px;
	margin-top: 2px;
}

.cluster-pulse {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 34px;
	height: 34px;
	background: var(--kt-primary);
	border-radius: 50%;
	opacity: 0.4;
	z-index: 1;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.4;
	}
	70% {
		transform: translate(-50%, -50%) scale(1.5);
		opacity: 0;
	}
	100% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0;
	}
}
</style>
