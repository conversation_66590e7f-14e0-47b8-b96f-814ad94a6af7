<template>
	<button :class="buttonClass" @click="showModal = true" class="text-capitalize text-nowrap">
		<i class="fa-duotone fa-location-dot fs-5"></i>
		{{ buttonLabel }}
	</button>

	<VueFinalModal v-model="showModal">
		<div class="modal fade show d-block">
			<div class="modal-dialog modal-xl">
				<div class="modal-content">
					<div class="modal-header">
						<h3 class="modal-title text-capitalize">{{ modalTitle }}</h3>
						<div
							@click="cancel"
							class="btn btn-icon btn-sm btn-active-light-primary ms-2">
							<i class="fas fa-times fs-1"></i>
						</div>
					</div>
					<div class="modal-content">
						<div ref="mapContainer" class="w-100 h-500px"></div>
					</div>
					<div v-if="!viewMode" class="modal-footer">
						<button type="button" class="btn btn-light text-capitalize" @click="cancel">
							{{ $t('general.cancel') }}
						</button>
						<button
							type="button"
							class="btn btn-primary text-capitalize"
							@click="accept">
							{{ $t('general.accept') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</VueFinalModal>
</template>

<script>
import { trans } from 'laravel-vue-i18n';
import { VueFinalModal } from 'vue-final-modal';
import { usePage } from '@inertiajs/vue3';
import mapboxgl from 'mapbox-gl';
import { createVNode, render } from 'vue';
import StationMarker from '@/Components/Location/Partials/Markers/StationMarker.vue';
import 'mapbox-gl/dist/mapbox-gl.css';

const MAP_STYLES = {
	LIGHT: 'mapbox://styles/mapbox/standard',
	SATELLITE: 'mapbox://styles/mapbox/satellite-streets-v12'
};

export default {
	name: 'LocationPicker',
	components: { VueFinalModal },
	props: {
		modelValue: {
			type: Object,
			required: true,
			default: () => ({ lat: null, lng: null })
		},
		viewMode: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			showModal: false,
			zoom: 9,
			mapStyle: MAP_STYLES.LIGHT,
			isSatellite: false,
			center: [-7.5898, 33.5731],
			markerPosition: this.modelValue,
			map: null,
			marker: null,
			companyLocationId: usePage().props?.user?.company?.location_id,
			satelliteButton: null
		};
	},

	watch: {
		showModal(newVal) {
			if (newVal) {
				this.$nextTick(async () => {
					await this.initializeMapWithLocation();
				});
			}
		},
		modelValue: {
			handler(newVal) {
				if (newVal?.lat && newVal?.lng) {
					this.markerPosition = { ...newVal };
					if (this.map && this.marker) {
						this.marker.setLngLat([newVal.lng, newVal.lat]);
						this.map.flyTo({
							center: [newVal.lng, newVal.lat],
							zoom: 15,
							duration: 1000
						});
					}
				}
			},
			deep: true
		}
	},

	computed: {
		buttonLabel() {
			return trans(`geo.location.${this.viewMode ? 'show_on_map' : 'pick_on_map'}`);
		},
		modalTitle() {
			return trans(`geo.location.${this.viewMode ? 'view' : 'pick_on_map'}`);
		},
		buttonClass() {
			return this.viewMode ? 'btn btn-sm btn-light btn-active-light-info' : 'btn btn-primary';
		},
		hasValidCoordinates() {
			return (
				(this.modelValue?.lat && this.modelValue?.lng) ||
				(this.markerPosition?.lat && this.markerPosition?.lng)
			);
		}
	},

	methods: {
		createSatelliteControl() {
			const button = document.createElement('button');
			button.className = 'mapboxgl-ctrl-icon mapboxgl-ctrl-satellite';
			button.innerHTML = '<i class="fas fa-satellite"></i>';
			button.title = this.$t('general.switch_satellite');
			button.addEventListener('click', this.toggleSatellite);

			return {
				onAdd: () => {
					this.satelliteButton = button;
					const container = document.createElement('div');
					container.className = 'mapboxgl-ctrl mapboxgl-ctrl-group';
					container.appendChild(button);
					return container;
				},
				onRemove: () => {
					button.removeEventListener('click', this.toggleSatellite);
					this.satelliteButton = null;
				}
			};
		},

		async toggleSatellite() {
			this.isSatellite = !this.isSatellite;
			const newStyle = this.isSatellite ? MAP_STYLES.SATELLITE : MAP_STYLES.LIGHT;

			const center = this.map.getCenter();
			const zoom = this.map.getZoom();
			const bearing = this.map.getBearing();
			const pitch = this.map.getPitch();
			const hasMarker = !!this.marker;

			if (this.satelliteButton) {
				this.satelliteButton.innerHTML = this.isSatellite
					? '<i class="fas fa-globe"></i>'
					: '<i class="fas fa-satellite"></i>';
				this.satelliteButton.title = this.isSatellite
					? this.$t('general.switch_light')
					: this.$t('general.switch_satellite');
			}

			this.map.setStyle(newStyle);

			this.map.once('style.load', () => {
				this.map.setCenter(center);
				this.map.setZoom(zoom);
				this.map.setBearing(bearing);
				this.map.setPitch(pitch);

				try {
					if (this.map.getLayer('country-label')) {
						this.map.setLayoutProperty('country-label', 'text-field', [
							'get',
							`name_${this.$locale()}`
						]);
					}
				} catch (e) {
					console.log('Country labels not available in this style');
				}

				if (hasMarker) {
					this.createMarker();
				}

				if (!this.viewMode) {
					this.map.on('click', this.handleMapClick);
				}
			});
		},

		async fetchCompanyLocation() {
			try {
				const response = await fetch(route('geo.locations.get', this.companyLocationId));
				return await response.json().then((data) => data.location);
			} catch (error) {
				console.error('Error fetching company location:', error);
				return null;
			}
		},

		async initializeMapWithLocation() {
			let center = this.center;
			let zoom = this.zoom;

			if (this.hasValidCoordinates) {
				center = [this.markerPosition.lng, this.markerPosition.lat];
				zoom = 15;
			} else if (this.companyLocationId) {
				const location = await this.fetchCompanyLocation();
				if (location) {
					center = [location.lng, location.lat];
					zoom = 12;
				}
			}

			await this.initializeMap(center, zoom);
		},

		async initializeMap(center, zoom) {
			const currentLocale = this.$locale();
			mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

			if (mapboxgl.getRTLTextPluginStatus() === 'unavailable') {
				mapboxgl.setRTLTextPlugin(
					'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.3.0/mapbox-gl-rtl-text.js',
					null,
					true
				);
			}

			this.map = new mapboxgl.Map({
				container: this.$refs.mapContainer,
				style: this.mapStyle,
				center,
				zoom
			});

			const controlPosition = currentLocale === 'ar' ? 'top-left' : 'top-right';
			this.map.addControl(new mapboxgl.NavigationControl(), controlPosition);
			this.map.addControl(this.createSatelliteControl(), controlPosition);

			await new Promise((resolve) => this.map.on('load', resolve));

			try {
				this.map.setLayoutProperty('country-label', 'text-field', [
					'get',
					`name_${currentLocale}`
				]);
			} catch (e) {
				console.log('Country labels not available in initial style');
			}

			if (!this.viewMode) {
				this.map.on('click', this.handleMapClick);
			}

			if (this.hasValidCoordinates) {
				this.createMarker();
			}
		},

		createMarker() {
			if (this.marker) {
				this.marker.remove();
				this.marker = null;
			}

			const container = document.createElement('div');

			const vNode = createVNode(StationMarker, {
				station: { coordinates: this.markerPosition }
			});

			vNode.appContext = this.$root.$?.appContext;
			render(vNode, container);

			this.marker = new mapboxgl.Marker({
				element: container,
				anchor: 'bottom',
				draggable: !this.viewMode
			})
				.setLngLat([this.markerPosition.lng, this.markerPosition.lat])
				.addTo(this.map);

			if (!this.viewMode) {
				this.marker.on('dragend', this.handleMarkerDragEnd);
			}
		},

		handleMapClick(event) {
			if (!this.viewMode) {
				this.markerPosition = {
					lat: event.lngLat.lat,
					lng: event.lngLat.lng
				};
				this.createMarker();
			}
		},

		handleMarkerDragEnd() {
			const lngLat = this.marker.getLngLat();
			this.markerPosition = {
				lat: lngLat.lat,
				lng: lngLat.lng
			};
		},

		cancel() {
			this.showModal = false;
			if (this.map) {
				this.map.remove();
				this.map = null;
			}
		},

		accept() {
			this.$emit('update:modelValue', this.markerPosition);
			this.cancel();
		}
	},

	beforeUnmount() {
		if (this.map) {
			this.map.remove();
		}
	}
};
</script>

<style scoped>
.marker {
	transform: translate(-50%, -100%);
}

.mapboxgl-ctrl-satellite {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.mapboxgl-ctrl-satellite:hover {
	background-color: #f5f5f5;
}

.mapboxgl-ctrl-satellite i {
	font-size: 16px;
	color: #333;
}
</style>
