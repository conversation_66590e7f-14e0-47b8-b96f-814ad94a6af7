<template>
	<div class="map-wrapper position-relative" :class="{ rounded }">
		<div ref="mapContainer" class="absolute inset-0 w-100 h-100" :class="{ rounded }" />
		<div class="absolute inset-0 pointer-events-none">
			<div ref="markersContainer" class="h-full" />
			<component
				v-if="openedMarker"
				ref="infoWindow"
				:is="markerTypeToInfoWindow[openedMarker.type]"
				:data="getInfoWindowData(openedMarker.type, openedMarker.index)"
				class="info-window"
				:style="infoWindowStyle"
				@mouseenter="onInfoWindowMouseEnter"
				@mouseleave="onInfoWindowMouseLeave" />
		</div>
	</div>
</template>

<script>
import mapboxgl from 'mapbox-gl';
import { createVNode, render } from 'vue';
import { trans } from 'laravel-vue-i18n';
import { usePage } from '@inertiajs/vue3';

import PassengersClusterMarker from '@/Components/Location/Partials/Markers/PassengersClusterMarker.vue';
import DriverMarker from '@/Components/Location/Partials/Markers/DriverMarker.vue';
import StopMarker from '@/Components/Location/Partials/Markers/StopMarker.vue';
import OriginMarker from '@/Components/Location/Partials/Markers/OriginMarker.vue';
import DestinationMarker from '@/Components/Location/Partials/Markers/DestinationMarker.vue';
import StationMarker from '@/Components/Location/Partials/Markers/StationMarker.vue';
import RouteStationMarker from '@/Components/Location/Partials/Markers/RouteStationMarker.vue';

import PassengerInfoWindow from '@/Components/Location/Partials/InfoWindows/PassengerInfoWindow.vue';
import DriverInfoWindow from '@/Components/Location/Partials/InfoWindows/DriverInfoWindow.vue';
import StopInfoWindow from '@/Components/Location/Partials/InfoWindows/StopInfoWindow.vue';
import StationInfoWindow from '@/Components/Location/Partials/InfoWindows/StationInfoWindow.vue';

const MAP_STYLES = {
	LIGHT: 'mapbox://styles/mapbox/standard',
	SATELLITE: 'mapbox://styles/mapbox/satellite-streets-v12'
};
const DEFAULT_CENTER = [-7.6, 33.56];
const DEFAULT_ZOOM = 12;
const HOVER_DELAY = 200;

export default {
	name: 'DynamicMap',
	components: {
		PassengerInfoWindow,
		DriverInfoWindow,
		StopInfoWindow,
		StationInfoWindow
	},

	props: {
		drivers: Array,
		ride: Object,
		routes: Array,
		stations: Array,
		passengers: {
			type: Array,
			default: () => []
		},
		showRoute: Boolean,
		rounded: {
			type: Boolean,
			default: true
		},
		mode: {
			type: String,
			default: 'normal'
		},
		shouldFitBound: {
			type: Boolean,
			default: true
		}
	},

	data() {
		return {
			map: null,
			mapInitialized: false,
			companyLocation: null,
			openedMarker: null,
			debounceTimeout: null,
			isInfoWindowHovered: false,
			markersByType: this.initializeMarkerTypes(),
			infoWindowStyle: this.getDefaultInfoWindowStyle(),
			activeMarker: null,
			markerStates: new Map(),
			companyLocationId: usePage().props?.user?.company?.location_id,
			isSatellite: false,
			satelliteButton: null,
			routeIds: [],
			savedRoutesData: null,
			currentRouteId: null,
			currentRouteGeometry: null,
			routeColors: [
				'#6210cc', // Primary
				'#009ef7', // Info color
				'#50cd89', // Success color
				'#f1416c', // Danger color
				'#7239ea', // Purple color
				'#ffc700', // Warning color
				'#181c32', // Dark color
				'#e4e6ef' // Light color
			]
		};
	},

	computed: {
		stops() {
			if (!this.ride) return [];

			if (
				this.ride.stops.filter(
					(stop) => stop.station?.location?.lng && stop.station?.location?.lat
				).length === 0
			) {
				console.error('No location details for stops in this ride: ', this.ride);
				return;
			}

			const stops = this.ride.stops.map((stop) => ({
				lat: stop.station?.location?.lat,
				lng: stop.station?.location?.lng,
				stationName: stop.station?.name || stop.station?.location.name,
				station: stop,
				status: stop.status,
				order: stop.station?.order
			}));

			return [
				this.createEndpoint('origin', -1),
				...stops,
				this.createEndpoint('destination', stops.length + 1)
			];
		},

		markerTypeToComponent() {
			return {
				'driver': DriverMarker,
				'stop': StopMarker,
				'stop.origin': OriginMarker,
				'stop.destination': DestinationMarker,
				'station': StationMarker,
				'station.origin': OriginMarker,
				'station.destination': DestinationMarker,
				'route-station': RouteStationMarker
			};
		},

		markerTypeToInfoWindow() {
			return {
				'passenger': PassengerInfoWindow,
				'driver': DriverInfoWindow,
				'stop': StopInfoWindow,
				'station': StationInfoWindow,
				'station.origin': StationInfoWindow,
				'station.destination': StationInfoWindow,
				'stop.origin': StationInfoWindow,
				'stop.destination': StationInfoWindow
			};
		}
	},

	watch: {
		ride: {
			handler(newRide) {
				if (!this.map) return;
				this.clearRoute();
				this.clearMarkers(['stop', 'stop.origin', 'stop.destination']);

				if (!this.stops) return;

				this.calculateRoute(newRide);
				this.renderStops();
				this.fitMapBounds(
					'ride',
					this.mode == 'live-map' ? { top: 50, bottom: 50, left: 350, right: 50 } : 50
				);
			}
		},

		routes: {
			handler(newRoutes) {
				if (!this.map || !newRoutes?.length) return;

				this.clearAllRoutes();
				this.clearMarkers(['route-station']);

				newRoutes.forEach((route, index) => {
					this.calculateMultipleRoutes(route, index);
				});

				if (this.shouldFitBound) {
					this.fitMapBounds(
						'routes',
						this.mode === 'routes-map'
							? { top: 50, bottom: 50, left: 350, right: 50 }
							: 50
					);
				}
			},
			deep: true
		},

		drivers: {
			handler(newDrivers) {
				if (!this.map) return;
				this.clearMarkers(['driver']);

				if (!newDrivers?.length) return;

				this.renderDrivers(newDrivers);
				if (!this.ride && this.shouldFitBound) {
					this.fitMapBounds(
						'driver',
						this.mode == 'live-map'
							? { top: 100, bottom: 200, left: 100, right: 100 }
							: 50
					);
				}
			},
			deep: true
		},

		stations: {
			handler(newStations) {
				if (!this.map) return;
				this.clearRoute();
				this.calculateStationsRoute(newStations);
			}
		},

		openedMarker: {
			handler(newMarker) {
				if (newMarker) {
					this.updateInfoWindowPosition();
				}
			},
			immediate: true
		}
	},

	async mounted() {
		await this.initializeMapWithLocation();
	},

	beforeUnmount() {
		if (this.map) {
			this.clearRoute();
			this.clearMarkers();
			this.map.remove();
		}
	},

	methods: {
		createSatelliteControl() {
			const button = document.createElement('button');
			button.className = 'mapboxgl-ctrl-icon mapboxgl-ctrl-satellite';
			button.innerHTML = '<i class="fas fa-satellite"></i>';
			button.title = this.$t('general.switch_satellite');
			button.addEventListener('click', this.toggleSatellite);

			return {
				onAdd: () => {
					this.satelliteButton = button;
					const container = document.createElement('div');
					container.className = 'mapboxgl-ctrl mapboxgl-ctrl-group';
					container.appendChild(button);
					return container;
				},
				onRemove: () => {
					button.removeEventListener('click', this.toggleSatellite);
					this.satelliteButton = null;
				}
			};
		},

		async toggleSatellite() {
			this.isSatellite = !this.isSatellite;
			const newStyle = this.isSatellite ? MAP_STYLES.SATELLITE : MAP_STYLES.LIGHT;

			const currentRouteData = this.currentRouteId
				? {
						id: this.currentRouteId,
						geometry: this.currentRouteGeometry
					}
				: null;

			if (this.routes?.length > 0) {
				this.savedRoutesData = JSON.parse(JSON.stringify(this.routes));
			}

			const center = this.map.getCenter();
			const zoom = this.map.getZoom();
			const bearing = this.map.getBearing();
			const pitch = this.map.getPitch();

			if (this.satelliteButton) {
				this.satelliteButton.innerHTML = this.isSatellite
					? '<i class="fas fa-globe"></i>'
					: '<i class="fas fa-satellite"></i>';
				this.satelliteButton.title = this.isSatellite
					? this.$t('general.switch_light')
					: this.$t('general.switch_satellite');
			}

			this.map.setStyle(newStyle);

			this.map.once('style.load', () => {
				this.map.jumpTo({ center, zoom, bearing, pitch });

				try {
					this.map.setLayoutProperty('country-label', 'text-field', [
						'get',
						`name_${this.$locale()}`
					]);
				} catch (e) {
					console.log('Country labels not available in this style');
				}

				if (currentRouteData) {
					this.addRouteLayers(currentRouteData.id, currentRouteData.geometry);
				}

				if (this.savedRoutesData?.length > 0) {
					this.clearMarkers(['route-station']);

					this.savedRoutesData.forEach((route, index) => {
						this.calculateMultipleRoutes(route, index);
					});
				}

				if (this.drivers?.length) {
					this.renderDrivers(this.drivers);
				}
				if (this.stops?.length) {
					this.renderStops();
				}
				if (this.stations?.length && this.mode !== 'routes-map') {
					this.renderStations();
				}
				if (this.passengers?.length) {
					this.renderPassengers();
				}

				this.map.on('move', this.updateInfoWindowPosition);
			});
		},

		async measureInfoWindow() {
			return new Promise((resolve) => {
				const observer = new MutationObserver((_, obs) => {
					const infoWindow = this.$refs.markersContainer?.querySelector('.info-window');
					if (infoWindow) {
						const bounds = infoWindow.getBoundingClientRect();
						obs.disconnect();
						resolve({ width: bounds.width, height: bounds.height });
					}
				});

				observer.observe(this.$refs.markersContainer, {
					childList: true,
					subtree: true,
					attributes: true
				});

				setTimeout(() => {
					observer.disconnect();
					resolve({ width: 300, height: 200 });
				}, 100);
			});
		},

		async adjustMapForInfoWindow(coordinates) {
			if (!this.map || !coordinates) return;

			const dimensions = await this.measureInfoWindow();
			const point = this.map.project(coordinates);
			const mapBounds = this.map.getContainer().getBoundingClientRect();
			const offset = this.getPopupOffset(this.openedMarker?.type);

			const padding = 35;
			let dx = 0;
			let dy = 0;

			const leftOverflow = -(point.x + offset[0] - dimensions.width / 2 - padding);
			const rightOverflow =
				point.x + offset[0] + dimensions.width / 2 + padding - mapBounds.width;

			if (leftOverflow > 0) {
				dx = leftOverflow;
			} else if (rightOverflow > 0) {
				dx = -rightOverflow;
			}

			const topOverflow = -(point.y + offset[1] - dimensions.height - padding);
			const bottomOverflow = point.y + offset[1] + padding - mapBounds.height;

			if (topOverflow > 0) {
				dy = topOverflow;
			} else if (bottomOverflow > 0) {
				dy = -bottomOverflow;
			}

			if (dx !== 0 || dy !== 0) {
				const currentCenter = this.map.getCenter();
				const pointDelta = this.map.project(currentCenter);
				const newCenter = this.map.unproject([pointDelta.x - dx, pointDelta.y - dy]);

				this.map.easeTo({
					center: newCenter,
					duration: 150,
					easing: (t) => t
				});
			}
		},

		async initializeMapWithLocation() {
			let center = DEFAULT_CENTER;

			if (this.drivers?.length) {
				center = [this.drivers[0].coordinates.lng, this.drivers[0].coordinates.lat];
			} else if (this.ride?.route?.origin) {
				center = [this.ride.route.origin.lng, this.ride.route.origin.lat];
			} else if (this.stations?.length) {
				center = [this.stations[0].coordinates.lng, this.stations[0].coordinates.lat];
			} else if (this.passengers?.length) {
				center = [this.passengers[0].location.lng, this.passengers[0].location.lat];
			} else if (this.companyLocationId) {
				try {
					const location = await this.fetchCompanyLocation();
					if (location) {
						center = [location.lng, location.lat];
						this.companyLocation = location;
					}
				} catch (error) {
					console.error('Error fetching company location:', error);
				}
			}

			await this.initializeMap(center);
			this.mapInitialized = true;
		},

		async fetchCompanyLocation() {
			try {
				const response = await fetch(route('geo.locations.get', this.companyLocationId));
				return await response.json().then((data) => data.location);
			} catch (error) {
				console.error('Error fetching company location:', error);
				return null;
			}
		},

		initializeMarkerTypes() {
			return {
				'driver': new Map(),
				'stop.origin': new Map(),
				'stop.destination': new Map(),
				'stop': new Map(),
				'station': new Map(),
				'station.origin': new Map(),
				'station.destination': new Map(),
				'passengers-cluster': new Map(),
				'route-station': new Map()
			};
		},

		getDefaultInfoWindowStyle() {
			return {
				position: 'absolute',
				left: '0px',
				top: '0px',
				transform: 'translate(-50%, -100%)',
				zIndex: 1000
			};
		},

		createEndpoint(type, order) {
			return {
				name: this.ride?.route?.[type]?.name ?? trans(`transport.route.${type}.name`),
				lat: this.ride?.route?.[type]?.lat,
				lng: this.ride?.route?.[type]?.lng,
				status: type,
				order
			};
		},

		async initializeMap(center) {
			const currentLocale = this.$locale();
			mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

			if (mapboxgl.getRTLTextPluginStatus() === 'unavailable') {
				mapboxgl.setRTLTextPlugin(
					'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.3.0/mapbox-gl-rtl-text.js',
					null,
					true
				);
			}

			this.map = new mapboxgl.Map({
				container: this.$refs.mapContainer,
				style: this.isSatellite ? MAP_STYLES.SATELLITE : MAP_STYLES.LIGHT,
				center,
				zoom: DEFAULT_ZOOM
			});

			const controlPosition = currentLocale === 'ar' ? 'top-left' : 'top-right';
			this.map.addControl(new mapboxgl.NavigationControl(), controlPosition);
			this.map.addControl(this.createSatelliteControl(), controlPosition);

			this.map.on('move', this.updateInfoWindowPosition);

			return new Promise((resolve) => {
				this.map.on('load', () => {
					try {
						if (this.map.getLayer('country-label')) {
							this.map.setLayoutProperty('country-label', 'text-field', [
								'get',
								`name_${currentLocale}`
							]);
						}
					} catch (e) {
						console.log('Country labels not available in initial style');
					}

					this.initializeRoute();
					this.setupMarkers();
					this.checkInitialData();
					resolve();
				});
			});
		},

		initializeRoute() {
			this.map.addSource('route', {
				type: 'geojson',
				data: this.getEmptyLineString()
			});

			this.map.addLayer({
				id: 'route',
				type: 'line',
				source: 'route',
				layout: {
					'line-join': 'round',
					'line-cap': 'round'
				},
				paint: {
					'line-color': '#6210cc',
					'line-opacity': 0.2,
					'line-width': 9
				}
			});
			this.map.addLayer({
				id: 'route_',
				type: 'line',
				source: 'route',
				layout: {
					'line-join': 'round',
					'line-cap': 'round'
				},
				paint: {
					'line-color': '#6210cc',
					'line-opacity': 0.7,
					'line-width': 3
				}
			});
		},

		getEmptyLineString() {
			return {
				type: 'Feature',
				properties: {},
				geometry: {
					type: 'LineString',
					coordinates: []
				}
			};
		},

		createMarkerElement(type, data, index) {
			const container = document.createElement('div');
			const component = this.markerTypeToComponent[type];

			const vNode = createVNode(component, {
				[type.toLowerCase()]: data,
				index,
				onShowInfo: () => this.showInfoWindow(index, type),
				onHideInfo: () => this.hideInfoWindow(index, type),
				onShowRide: () => this.showRide(index, type)
			});

			vNode.appContext = this.$root.$?.appContext;
			render(vNode, container);

			return {
				element: container,
				anchor: 'bottom'
			};
		},

		async calculateRoute(ride) {
			if (!ride) return;

			const coordinates = [
				[ride.route.origin.lng, ride.route.origin.lat],
				...ride.stops.map((stop) => [
					stop.station?.location?.lng,
					stop.station?.location?.lat
				]),
				[ride.route.destination.lng, ride.route.destination.lat]
			].filter((coord) => coord[0] && coord[1]);

			await this.fetchAndSetRoute(coordinates);
		},

		async calculateStationsRoute(stations) {
			if (!this.showRoute || !stations?.length) return;

			const coordinates = stations
				.sort((a, b) => a.order - b.order)
				.filter((station) => station.coordinates?.lng && station.coordinates?.lat)
				.map((station) => [station.coordinates.lng, station.coordinates.lat]);

			await this.fetchAndSetRoute(coordinates);
		},

		async calculateMultipleRoutes(route, colorIndex) {
			if (!route) return;
			if (!route.origin || !route.destination) return;

			const color = this.routeColors[colorIndex % this.routeColors.length];

			const isSingleRoute = this.routes && this.routes.length === 1;

			if (route.origin) {
				const container = document.createElement('div');
				const vNode = createVNode(RouteStationMarker, {
					station: { name: route.origin.name },
					color,
					isOrigin: true,
					isEndpoint: true
				});

				vNode.appContext = this.$root.$?.appContext;
				render(vNode, container);

				const marker = new mapboxgl.Marker({
					element: container,
					anchor: 'center'
				})
					.setLngLat([route.origin.lng, route.origin.lat])
					.addTo(this.map);

				this.markersByType['route-station'].set(`${route.id}-origin`, marker);
			}

			if (route.destination) {
				const container = document.createElement('div');
				const vNode = createVNode(RouteStationMarker, {
					station: { name: route.destination.name },
					color,
					isDestination: true,
					isEndpoint: true
				});

				vNode.appContext = this.$root.$?.appContext;
				render(vNode, container);

				const marker = new mapboxgl.Marker({
					element: container,
					anchor: 'center'
				})
					.setLngLat([route.destination.lng, route.destination.lat])
					.addTo(this.map);

				this.markersByType['route-station'].set(`${route.id}-destination`, marker);
			}

			if (isSingleRoute && route.stations?.length) {
				route.stations.forEach((station, index) => {
					if (!station.location) return;

					const container = document.createElement('div');
					const vNode = createVNode(RouteStationMarker, {
						station,
						color
					});

					vNode.appContext = this.$root.$?.appContext;
					render(vNode, container);

					const marker = new mapboxgl.Marker({
						element: container,
						anchor: 'center'
					})
						.setLngLat([station.location.lng, station.location.lat])
						.addTo(this.map);

					this.markersByType['route-station'].set(`${route.id}-station-${index}`, marker);
				});
			}

			const coordinates = [];

			if (route.origin) {
				coordinates.push([route.origin.lng, route.origin.lat]);
			}

			route.stations
				.sort((a, b) => a.order - b.order)
				.filter((station) => station.location?.lng && station.location?.lat)
				.forEach((station) => {
					coordinates.push([station.location.lng, station.location.lat]);
				});

			if (route.destination) {
				coordinates.push([route.destination.lng, route.destination.lat]);
			}

			if (coordinates.length < 2) return;

			try {
				const waypoints = coordinates.map((coord) => coord.join(',')).join(';');
				const response = await fetch(
					`https://api.mapbox.com/directions/v5/mapbox/driving/${waypoints}?` +
						new URLSearchParams({
							geometries: 'geojson',
							access_token: mapboxgl.accessToken,
							overview: 'full',
							alternatives: 'false',
							continue_straight: 'true',
							annotations: 'distance,duration',
							steps: 'true',
							banner_instructions: 'true',
							language: this.$locale()
						})
				);

				const data = await response.json();

				if (data.routes?.[0]) {
					const routeId = `route-${route.id}-${Date.now()}`;
					this.addRouteLayers(routeId, data.routes[0].geometry, colorIndex);
				}
			} catch (error) {
				console.error('Error calculating route:', error);
			}
		},

		async fetchAndSetRoute(coordinates) {
			if (!coordinates.length || coordinates.length < 2) return;

			const waypoints = coordinates.map((coord) => coord.join(',')).join(';');

			try {
				const response = await fetch(
					`https://api.mapbox.com/directions/v5/mapbox/driving/${waypoints}?` +
						new URLSearchParams({
							geometries: 'geojson',
							access_token: mapboxgl.accessToken,
							overview: 'full',
							alternatives: 'false',
							continue_straight: 'true',
							annotations: 'distance,duration',
							steps: 'true',
							banner_instructions: 'true',
							language: this.$locale()
						})
				);

				const data = await response.json();

				if (data.routes?.[0]) {
					this.currentRouteId = `route-${Date.now()}`;
					this.currentRouteGeometry = data.routes[0].geometry;
					this.clearPreviousRouteLayers();
					this.addRouteLayers(this.currentRouteId, this.currentRouteGeometry);
				}
			} catch (error) {
				console.error('Error calculating route:', error);
			}
		},

		clearPreviousRouteLayers() {
			if (this.map) {
				const layers = this.map.getStyle().layers;
				layers.forEach((layer) => {
					if (layer.id.startsWith('route-')) {
						this.map.removeLayer(layer.id);
					}
				});

				const sources = Object.keys(this.map.getStyle().sources);
				sources.forEach((source) => {
					if (source.startsWith('route-')) {
						this.map.removeSource(source);
					}
				});
			}
		},

		addRouteLayers(routeId, geometry, colorIndex = 0) {
			const color = this.routeColors[colorIndex % this.routeColors.length];
			const isMultipleRoutes = this.routes && this.routes.length > 1;

			const bgLineWidth = isMultipleRoutes ? 6 : 9;
			const mainLineWidth = isMultipleRoutes ? 2 : 3;

			this.map.addSource(routeId, {
				type: 'geojson',
				data: {
					type: 'Feature',
					properties: {},
					geometry
				}
			});

			this.map.addLayer({
				id: `${routeId}-bg`,
				type: 'line',
				source: routeId,
				layout: {
					'line-join': 'round',
					'line-cap': 'round',
					'visibility': 'visible'
				},
				paint: {
					'line-color': color,
					'line-opacity': 0.2,
					'line-width': bgLineWidth,
					'line-blur': 0.5
				}
			});

			this.map.addLayer({
				id: `${routeId}-main`,
				type: 'line',
				source: routeId,
				layout: {
					'line-join': 'round',
					'line-cap': 'round',
					'visibility': 'visible'
				},
				paint: {
					'line-color': color,
					'line-opacity': 0.7,
					'line-width': mainLineWidth
				}
			});

			this.routeIds.push(routeId);
		},

		renderStops() {
			if (!this.stops?.length) return;

			this.stops.forEach((stop, index) => {
				const type = this.getStopType(stop);
				const el = this.createMarkerElement(type, stop, index);
				const marker = new mapboxgl.Marker(el)
					.setLngLat([stop.lng, stop.lat])
					.addTo(this.map);
				this.markersByType[type].set(index, marker);
			});
		},

		renderDrivers(drivers) {
			drivers.forEach((driver, index) => {
				const el = this.createMarkerElement('driver', driver, index);
				const marker = new mapboxgl.Marker(el)
					.setLngLat([driver.coordinates.lng, driver.coordinates.lat])
					.addTo(this.map);
				this.markersByType.driver.set(index, marker);
			});
		},

		setupMarkers() {
			this.clearMarkers();
			this.renderDrivers(this.drivers || []);
			this.renderStops();
			this.renderStations();
			this.renderPassengers();
		},

		renderStations() {
			if (!this.stations?.length) return;

			const sortedStations = [...this.stations].sort((a, b) => a.order - b.order);

			sortedStations.forEach((station, index) => {
				let type = 'station';

				if (this.mode !== 'assets') {
					if (index === 0) {
						type = 'station.origin';
					} else if (index === sortedStations.length - 1) {
						type = 'station.destination';
					}
				}

				const el = this.createMarkerElement(type, station, this.stations.indexOf(station));
				const marker = new mapboxgl.Marker(el)
					.setLngLat([station.coordinates.lng, station.coordinates.lat])
					.addTo(this.map);
				this.markersByType[type].set(this.stations.indexOf(station), marker);
			});
		},

		renderPassengers() {
			if (!this.passengers?.length) return;

			const container = document.createElement('div');
			const vNode = createVNode(PassengersClusterMarker, {
				passengers: this.passengers,
				map: this.map,
				onShowInfo: (index) => this.showInfoWindow(index, 'passenger'),
				onHideInfo: (index) => this.hideInfoWindow(index, 'passenger')
			});

			vNode.appContext = this.$root.$?.appContext;
			render(vNode, container);
			this.$refs.markersContainer.appendChild(container);
			this.markersByType['passengers-cluster'].set(0, {
				remove: () => container.remove()
			});
		},

		clearRoute() {
			this.clearPreviousRouteLayers();
			this.currentRouteId = null;
		},

		clearAllRoutes() {
			this.routeIds.forEach((id) => {
				if (this.map.getSource(id)) {
					this.map.removeLayer(`${id}-bg`);
					this.map.removeLayer(`${id}-main`);
					this.map.removeSource(id);
				}
			});
			this.routeIds = [];
		},

		clearMarkers(types = null) {
			const typesToClear = types || Object.keys(this.markersByType);
			typesToClear.forEach((type) => {
				this.markersByType[type].forEach((marker) => marker.remove());
				this.markersByType[type].clear();
			});
		},

		fitMapBounds(type, padding = 50) {
			const bounds = new mapboxgl.LngLatBounds();
			const itemMap = {
				driver: () =>
					this.drivers?.forEach((item) =>
						bounds.extend([item.coordinates.lng, item.coordinates.lat])
					),
				station: () =>
					this.stations?.forEach((item) =>
						bounds.extend([item.coordinates.lng, item.coordinates.lat])
					),
				ride: () => this.stops?.forEach((item) => bounds.extend([item.lng, item.lat])),
				passenger: () =>
					this.passengers?.forEach((item) =>
						bounds.extend([item.location.lng, item.location.lat])
					),
				routes: () => {
					this.routes?.forEach((route) => {
						if (route.origin && route.destination) {
							bounds.extend([route.origin.lng, route.origin.lat]);
							bounds.extend([route.destination.lng, route.destination.lat]);
						}
						route.stations?.forEach((station) => {
							if (station.location) {
								bounds.extend([station.location.lng, station.location.lat]);
							}
						});
					});
				}
			};

			itemMap[type]?.();

			if (!bounds.isEmpty()) {
				let adjustedPadding = padding;
				if (
					type === 'routes' &&
					this.mode === 'routes-map' &&
					this.mode === 'passengers-map' &&
					this.routes?.length > 1
				) {
					if (typeof padding === 'number') {
						adjustedPadding = {
							top: padding,
							right: padding,
							bottom: padding + 150,
							left: padding
						};
					} else if (typeof padding === 'object') {
						adjustedPadding = { ...padding };
						adjustedPadding.bottom = (padding.bottom || 0) + 150;
					}
				}
				this.map.fitBounds(bounds, { padding: adjustedPadding, maxZoom: 15 });
			}
		},

		showInfoWindow(index, type) {
			const markerId = `${type}-${index}`;
			clearTimeout(this.markerStates.get(markerId)?.timeout);

			this.activeMarker = markerId;
			this.markerStates.set(markerId, {
				timeout: setTimeout(async () => {
					if (this.activeMarker === markerId && !this.isInfoWindowHovered) {
						this.openedMarker = { index, type };

						const coordinates = this.getPosition(type, index);
						if (coordinates) {
							await this.adjustMapForInfoWindow([coordinates.lng, coordinates.lat]);
						}
					}
				}, HOVER_DELAY)
			});
		},

		hideInfoWindow(index, type) {
			const markerId = `${type}-${index}`;
			clearTimeout(this.markerStates.get(markerId)?.timeout);

			this.markerStates.set(markerId, {
				timeout: setTimeout(() => {
					if (this.activeMarker === markerId && !this.isInfoWindowHovered) {
						this.openedMarker = null;
						this.activeMarker = null;
					}
				}, HOVER_DELAY)
			});
		},

		updateInfoWindowPosition() {
			if (!this.openedMarker || !this.map) return;

			const coordinates = this.getPosition(this.openedMarker.type, this.openedMarker.index);
			if (!coordinates) return;

			const point = this.map.project([coordinates.lng, coordinates.lat]);
			const offset = this.getPopupOffset(this.openedMarker.type);

			this.infoWindowStyle = {
				...this.infoWindowStyle,
				left: `${point.x + offset[0]}px`,
				top: `${point.y + offset[1]}px`
			};
		},

		onInfoWindowMouseEnter() {
			this.isInfoWindowHovered = true;
		},

		onInfoWindowMouseLeave() {
			this.isInfoWindowHovered = false;
			if (this.openedMarker) {
				this.hideInfoWindow(this.openedMarker.index, this.openedMarker.type);
			}
		},

		getPosition(type, index) {
			const positionMap = {
				'passenger': () => this.passengers.find((p) => p.id === index)?.location,
				'stop': () => this.stops[index],
				'stop.origin': () => this.stops[index],
				'stop.destination': () => this.stops[index],
				'station': () => this.stations[index]?.coordinates,
				'station.origin': () => this.stations[index]?.coordinates,
				'station.destination': () => this.stations[index]?.coordinates,
				'driver': () => this.drivers[index]?.coordinates
			};

			return positionMap[type]?.();
		},
		getPopupOffset(type) {
			const MARKER_OFFSETS = {
				'driver': [0, -60],
				'passenger': [0, -40],
				'stop': [0, -40],
				'stop.origin': [0, -40],
				'stop.destination': [0, -40],
				'station': [0, -40],
				'station.origin': [0, -40],
				'station.destination': [0, -40]
			};

			return Object.freeze(MARKER_OFFSETS[type] || [0, -60]);
		},

		getInfoWindowData(type, index) {
			const dataMap = {
				'passenger': () => this.passengers.find((p) => p.id === index),
				'driver': () => this.drivers[index],
				'stop': () => ({
					attendances: this.ride?.attendances,
					station: this.stops[index]?.station
				}),
				'stop.origin': () => ({ name: this.stops[index]?.name }),
				'stop.destination': () => ({ name: this.stops[index]?.name }),
				'station': () => this.stations[index],
				'station.origin': () => this.stations[index],
				'station.destination': () => this.stations[index]
			};

			return dataMap[type]?.();
		},

		showRide(index, type) {
			if (
				type !== 'driver' ||
				this.ride?.driver_id === this.drivers[index]?.id ||
				this.mode !== 'live-map'
			)
				return;

			const driver = this.drivers[index];
			this.isInfoWindowHovered = false;
			this.openedMarker = null;
			this.clearMarkers();
			this.$emit('showRide', driver);
		},

		getStopType(stop) {
			if (stop.status === 'origin') return 'stop.origin';
			if (stop.status === 'destination') return 'stop.destination';
			return 'stop';
		},

		checkInitialData() {
			if (this.drivers?.length) {
				this.fitMapBounds(
					'driver',
					this.mode == 'live-map' ? { top: 100, bottom: 200, left: 100, right: 100 } : 70
				);
			} else if (this.ride) {
				this.calculateRoute(this.ride);
				this.fitMapBounds('ride');
			} else if (this.routes?.length) {
				this.routes.forEach((route, index) => {
					this.calculateMultipleRoutes(route, index);
				});
				this.fitMapBounds('routes');
			} else if (this.stations?.length) {
				this.calculateStationsRoute(this.stations);
				this.fitMapBounds('station');
			} else if (this.passengers?.length) {
				this.fitMapBounds('passenger');
			}
		}
	}
};
</script>

<style scoped>
.marker {
	transform: translate(-50%, -100%);
}

.mapboxgl-ctrl-satellite {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.mapboxgl-ctrl-satellite:hover {
	background-color: #f5f5f5;
}

.mapboxgl-ctrl-satellite i {
	font-size: 16px;
	color: #333;
}
</style>
