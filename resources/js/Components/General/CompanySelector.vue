<template>
	<div
		v-if="shouldShowSelector"
		class="d-flex justify-content-around border-bottom-0 border-end border-gray-300 w-100 py-5">
		<div class="dropdown w-100 mx-3" v-click-away="() => (isOpen = false)">
			<div class="card bg-light p-0 w-100" @click="toggleDropdown" role="button">
				<div class="card-body p-1">
					<div class="d-flex w-100">
						<div class="symbol symbol-35px">
							<div class="symbol-label">
								<img
									:src="selectedCompany.logo"
									alt=""
									class="object-fit-contain ratio ratio-1x1 rounded" />
							</div>
						</div>
						<div class="d-flex flex-column justify-content-center ms-3 overflow-hidden">
							<div
								class="fw-bold text-capitalize text-truncate"
								style="max-width: 120px">
								{{ selectedCompany.name }}
							</div>
						</div>
						<div
							class="d-flex flex-column justify-content-center align-items-end ms-auto">
							<div class="px-2">
								<i class="fa-duotone fa-solid fa-angles-up-down"></i>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div
				class="dropdown-menu w-100 mb-1"
				:class="{ show: isOpen }"
				style="transform-origin: bottom">
				<div
					v-for="company in companies"
					:key="company.id"
					class="dropdown-item p-1"
					:class="{ active: company.id === selectedCompany.id }"
					@click="selectCompany(company)">
					<div class="d-flex">
						<div class="symbol symbol-35px border">
							<div class="symbol-label">
								<img
									:src="company.logo"
									alt=""
									class="object-fit-contain ratio ratio-1x1 rounded" />
							</div>
						</div>
						<div class="d-flex flex-column justify-content-center ms-3 overflow-hidden">
							<div
								class="fw-bold text-capitalize text-truncate"
								style="max-width: 200px">
								{{ company.name }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { directive } from 'vue3-click-away';
import { usePage, router } from '@inertiajs/vue3';

export default {
	name: 'CompanySelector',
	directives: {
		ClickAway: directive
	},
	data() {
		return {
			isOpen: false,
			companies: []
		};
	},
	computed: {
		selectedCompany() {
			return usePage().props.user.company;
		},
		shouldShowSelector() {
			return this.companies.length > 1;
		}
	},
	mounted() {
		this.fetchCompanies();
	},
	methods: {
		async fetchCompanies() {
			try {
				const response = await fetch(route('settings.company.list'));
				const data = await response.json();
				this.companies = data.data;
			} catch (error) {
				console.error('Failed to fetch companies:', error);
			}
		},
		toggleDropdown() {
			this.isOpen = !this.isOpen;
		},
		selectCompany(company) {
			router.put(
				route('settings.company.select', { company: company.id }),
				{},
				{
					preserveState: false
				}
			);
			this.isOpen = false;
		}
	}
};
</script>

<style scoped>
.dropdown-menu {
	bottom: 100%;
	top: auto;
	transition:
		transform 0.2s ease,
		opacity 0.2s ease;
	transform: scaleY(0);
	opacity: 0;
	max-height: 300px;
	overflow-y: auto;
}

.dropdown-menu.show {
	transform: scaleY(1);
	opacity: 1;
}

.dropdown-item {
	transition: background-color 0.2s ease;
}

.dropdown-item:hover {
	background-color: #f8f9fa;
	cursor: pointer;
}

.dropdown-item.active {
	background-color: #e9ecef;
	color: inherit;
}

.text-truncate {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
