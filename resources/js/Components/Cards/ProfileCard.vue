<template>
	<div v-if="profile" class="d-flex align-items-center">
		<Link :href="route(baseRoute, profile.id)">
			<div :class="'symbol symbol-' + size + 'px'">
				<img :src="profile?.image ?? profile?.user?.image" :alt="profile?.name" />
			</div>
		</Link>

		<div class="d-flex flex-column ms-3">
			<div class="d-flex">
				<Link
					:href="route(baseRoute, profile.id)"
					class="text-gray-800 text-hover-primary fs-6 fw-semibold pe-3">
					{{ profile?.name }}
				</Link>
				<div v-if="showStatus" class="badge text-capitalize" :class="badgeClass">
					{{ $t('general.enums.' + profile?.status) }}
				</div>
			</div>
			<div class="d-flex flex-column">
				<span v-if="profileType" class="text-gray-500 fs-7">
					{{ $t('general.models.' + profileType) }}
				</span>
			</div>
		</div>
	</div>
	<div v-else>
		{{ $t('general.profile_not_found') }}
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';

export default {
	name: 'ProfileCard',
	components: {
		Link
	},
	props: {
		profile: {
			type: Object,
			required: true
		},
		baseRoute: {
			type: String,
			required: true
		},
		size: {
			type: String,
			default: '50px'
		},
		showStatus: {
			type: Boolean,
			default: false
		},
		profileType: {
			type: String,
			default: null
		}
	},
	computed: {
		badgeClass() {
			switch (this.profile?.status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
					return 'badge-light-dark';
			}
		}
	}
};
</script>
