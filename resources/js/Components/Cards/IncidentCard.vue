<template>
	<div class="card card-flush">
		<div class="card-header">
			<div v-if="incident?.reporter" class="card-title">
				<Link :href="route(baseRoute, incident.reporter_id)">
					<div class="symbol symbol-45px border bg-white border-hover-primary">
						<img
							:alt="incident.reporter.name"
							:src="incident.reporter.image"
							class="img-fluid" />
					</div>
				</Link>
				<div class="d-flex flex-column">
					<Link :href="route(baseRoute, incident.reporter_id)">
						<div class="ms-2 fs-5 fw-semibold text-gray-900 text-hover-primary">
							{{ incident.reporter.name }}
						</div>
					</Link>
					<small class="ms-2 fw-semibold text-gray-500 fs-7">{{
						incident.created_at_diff
					}}</small>
				</div>
			</div>
			<div v-else>{{ $t('general.profile_not_found') }}</div>
			<div class="card-toolbar">
				<LocationPicker v-model="incident.coordinates" viewMode />
			</div>
		</div>
		<div class="card-body">
			<div class="d-flex justify-content-between align-items-center">
				<div class="d-flex align-items-center fs-3 text-capitalize">
					<i class="fa-duotone fs-3 text-primary me-3" :class="incidentIcon"></i>
					{{ $t(`general.enums.${incident.type}`) }}
				</div>
				<div v-if="showRide && incident.ride_id" class="d-flex align-items-center fs-3">
					<Link :href="route('transport.rides.view', incident.ride_id)">
						<div class="card hover-elevate-up border-hover-primary">
							<div class="card-body px-3 py-1">
								<i class="fa-duotone fa-road text-primary fs-5 me-2"></i>
								<span class="text-hover-primary text-capitalize fs-5">
									{{ $t('transport.rides.labels.ride_id') }}
									#{{ incident.ride_id }}
								</span>
							</div>
						</div>
					</Link>
				</div>
			</div>
			<div class="d-flex mt-5">
				<p>{{ incident.description }}</p>
			</div>
		</div>
	</div>
</template>
<script>
import LocationPicker from '@/Components/Location/LocationPicker.vue';
import { Link } from '@inertiajs/vue3';

export default {
	name: 'IncidentCard',
	components: {
		LocationPicker,
		Link
	},
	props: {
		incident: {
			type: Object,
			required: true
		},
		showRide: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		incidentIcon() {
			switch (this.incident?.type) {
				case 'mechanical_issue':
					return 'fa-tools';
				case 'accident':
					return 'fa-car-crash';
				case 'health_emergency':
					return 'fa-heartbeat';
				case 'behavioral_issue':
					return 'fa-exclamation-triangle';
				case 'traffic_delay':
					return 'fa-traffic-cone';
				case 'weather_condition':
					return 'fa-cloud-sun-rain';
				case 'road_blockage':
					return 'fa-road';
				case 'missed_station':
					return 'fa-times-circle';
				case 'other':
					return 'fa-question-circle';
				default:
					return 'fa-info-circle';
			}
		},
		baseRoute() {
			switch (this.incident.reporter_type) {
				case 'Passenger':
					return 'transport.passengers.view';
				case 'Assistant':
					return 'fleet.assistants.view';
				case 'Driver':
					return 'fleet.drivers.view';
				case 'Responsible':
					return 'transport.responsibles.view';
				default:
					return;
			}
		}
	}
};
</script>
