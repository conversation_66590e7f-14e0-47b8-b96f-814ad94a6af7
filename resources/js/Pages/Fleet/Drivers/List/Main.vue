<template>
	<BaseLayout>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<template v-slot:toolbar>
			<DriversToolbar />
		</template>

		<Head :title="$t('nav.drivers')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<DriversFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="drivers.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<DriversTable :drivers="drivers.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="colg4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="drivers.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head } from '@inertiajs/vue3';
import DriversToolbar from '@/Pages/Fleet/Drivers/Partials/DriversToolbar.vue';
import DriversTable from '@/Pages/Fleet/Drivers/List/Partials/DriversTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import DriversFilters from '@/Pages/Fleet/Drivers/List/Partials/DriversFilters.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';

export default {
	name: 'Main.vue',
	components: {
		BaseLayout,
		Head,
		DriversToolbar,
		DriversTable,
		Pagination,
		EmptyResults,
		DriversFilters,
		HeaderMenu
	},
	props: {
		drivers: Object
	},
	data() {
		return {
			menu: [
				{
					hasPermission: true,
					active: route().current('fleet.assistants.*'),
					route: route('fleet.assistants.list'),
					icon: 'fa-duotone fa-handshake-angle',
					content: this.$t('fleet.assistant.names')
				}
			]
		};
	}
};
</script>
