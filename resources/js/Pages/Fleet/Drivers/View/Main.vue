<template>
	<DriverLayout :driver="driver">
		<div v-if="!viewMode" class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.image') }}
			</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<ImageInput
					v-model="updateDriverForm.image"
					:error="updateDriverForm.errors.image"
					:allowedTypesText="
						$t('general.allowed_file_types', { types: 'png, jpg, jpeg' })
					"
					@update:modelValue="updateDriver" />
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.first_name') }}
			</label>
			<div class="col-8">
				<input
					v-model="updateDriverForm.first_name"
					class="form-control"
					:placeholder="$t('general.first_name')"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.first_name" class="text-danger fs-6">
					{{ updateDriverForm.errors.first_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.last_name') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updateDriverForm.last_name"
					class="form-control"
					:placeholder="$t('general.last_name')"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.last_name" class="text-danger fs-6">
					{{ updateDriverForm.errors.last_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.phone') }}
			</label>
			<div class="col-8">
				<PhoneInput v-model="updateDriverForm.phone" @phone-validated="validatePhone" />
				<div
					v-if="updateDriverForm.errors.phone || phoneValidationError"
					class="text-danger fs-6">
					{{ updateDriverForm.errors.phone || phoneValidationError }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.email') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updateDriverForm.email"
					class="form-control"
					:placeholder="$t('general.email')"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.email" class="text-danger fs-6">
					{{ updateDriverForm.errors.email }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.gender') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateDriverForm.gender"
					options="UserGender"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.gender" class="text-danger fs-6">
					{{ updateDriverForm.errors.gender }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateDriverForm.status"
					options="DriverStatus"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.status" class="text-danger fs-6">
					{{ updateDriverForm.errors.status }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.region.name') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateDriverForm.region_id"
					options="Region"
					:lazySearch="true"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.region_id" class="text-danger fs-6">
					{{ updateDriverForm.errors.region_id }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.default_vehicle') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateDriverForm.default_vehicle_id"
					options="Vehicle"
					:lazySearch="true"
					:disabled="viewMode" />
				<div v-if="updateDriverForm.errors.default_vehicle_id" class="text-danger fs-6">
					{{ updateDriverForm.errors.default_vehicle_id }}
				</div>
			</div>
		</div>

		<div v-if="!viewMode" class="row">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deleteDriver" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('fleet.drivers.view', driver.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updateDriverForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updateDriver">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</DriverLayout>
</template>

<script>
import DriverLayout from '@/Pages/Fleet/Drivers/View/Partials/DriverLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import PhoneInput from '@/Components/PhoneInput.vue';
import ImageInput from '@/Components/General/ImageInput.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		Link,
		DriverLayout,
		MultiSelect,
		ConfirmationButton,
		PhoneInput,
		ImageInput
	},
	props: {
		driver: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updateDriverForm: useForm({
				id: this.driver.id,
				first_name: this.driver.first_name,
				last_name: this.driver.last_name,
				status: this.driver.status,
				gender: this.driver.gender,
				phone: this.driver.phone,
				email: this.driver.email,
				default_vehicle_id: this.driver.default_vehicle_id,
				region_id: this.driver.region_id,
				image: this.driver.image
			}),
			viewMode: false,
			phoneValidationError: ''
		};
	},
	methods: {
		updateDriver() {
			if (this.updateDriverForm.image && !(this.updateDriverForm.image instanceof File)) {
				this.updateDriverForm.image = null;
			}

			this.updateDriverForm.post(route('fleet.drivers.update', this.driver.id), {
				_method: 'put',
				onError: (errors) => {
					if (errors.phone) {
						this.phoneValidationError = errors.phone[0];
					}
				},
				onSuccess: () => {
					this.updateDriverForm.image = this.driver?.image;
				}
			});
		},
		deleteDriver() {
			router.delete(route('fleet.drivers.update', this.driver.id));
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', {
						attribute: trans('general.phone')
					});
		}
	}
};
</script>
