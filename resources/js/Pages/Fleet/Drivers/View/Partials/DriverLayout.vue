<template>
	<BaseLayout>
		<Head :title="$t('nav.drivers')" />

		<div class="row g-3 h-100">
			<div class="col-md-4 col-lg-3">
				<div class="card p-4 custom-sticky">
					<div class="d-flex flex-center flex-column mb-5">
						<ImageSymbol :imageSrc="driver.image" :width="100" :height="100" />
					</div>

					<div class="d-flex flex-stack fs-4 py-3">
						<div class="fw-bold text-capitalize">
							{{ $t('fleet.driver.details') }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.name') }}
						</div>
						<div class="text-gray-600">
							{{ driver.name }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.phone') }}
						</div>
						<div class="text-gray-600">
							{{ driver.phone }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.status') }}
						</div>
						<div class="badge text-capitalize" :class="badgeClass">
							{{ $t('general.enums.' + driver.status) }}
						</div>
					</div>
				</div>
			</div>

			<div class="col-md-8 col-lg-9">
				<PageNav :driver="driver" class="mb-3" />
				<div class="card h-100">
					<div class="card-body">
						<slot />
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>
<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import PageNav from '@/Pages/Fleet/Drivers/View/Partials/PageNav.vue';
import ImageUploader from '@/Components/ImageUploader.vue';
import { Link, Head } from '@inertiajs/vue3';
import ImageSymbol from '@/Components/General/ImageSymbol.vue';

export default {
	name: 'DriverLayout',
	components: {
		Link,
		ImageUploader,
		BaseLayout,
		PageNav,
		Head,
		ImageSymbol
	},
	props: {
		driver: {
			type: Object,
			required: true
		}
	},
	computed: {
		badgeClass() {
			switch (this.driver.status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
					return 'badge-light-dark';
			}
		}
	}
};
</script>
