<template>
	<div class="card card-flush border-0 mt-3">
		<div class="card-header">
			<div class="card-title">
				<div class="card-label">
					<Link :href="route('transport.rides.view', ride?.id)">
						<div class="d-flex align-items-center text-gray-800 fw-bold fs-5">
							<i class="fa-duotone fa-road text-primary me-1 fs-1"></i>

							<div class="d-flex flex-column text-hover-primary ms-3">
								<div class="d-flex mb-1">#{{ ride.id }}</div>
								<small>{{ ride.created_at_diff }}</small>
							</div>
						</div>
					</Link>
				</div>
			</div>
			<div class="d-flex align-items-center justify-content-between">
				<PassengerList :passengers="passengers" :maxVisible="10" />
				<div :class="badgeClass" class="badge text-capitalize ms-10">
					{{ $t('general.enums.' + ride.status) }}
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="row">
				<div class="col-md-6 border-end">
					<CustomTimeLine :points="points" />
				</div>
				<div class="col-md-6">
					<div class="d-flex align-items-center h-100">
						<div class="row g-0 w-100">
							<div class="col-7 mb-4">
								<div
									class="d-flex flex-column justify-content-between h-100 px-3 pt-3">
									<div class="d-flex justify-content-between fs-6">
										<strong class="text-gray-700 text-capitalize">
											{{ $t('fleet.vehicle.labels.name') }}
										</strong>
										<div class="text-gray-800 fw-semibold">
											{{ vehicle?.name }}
										</div>
									</div>
									<div class="d-flex justify-content-between">
										<strong class="text-gray-700 text-capitalize">
											{{ $t('fleet.vehicle.labels.plate') }}
										</strong>
										<div>
											{{ vehicle?.plate ?? '-' }}
										</div>
									</div>
									<div class="d-flex justify-content-between">
										<strong class="text-gray-700 text-capitalize">
											{{ $t('fleet.vehicle.labels.capacity') }}
										</strong>
										<div>
											{{ vehicle?.capacity }}
										</div>
									</div>
									<div class="d-flex justify-content-between">
										<strong class="text-gray-700 text-capitalize">
											{{ $t('fleet.vehicle.labels.type') }}
										</strong>
										<div>
											{{ $t(`fleet.vehicle.types.${vehicle?.type}`) }}
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<Link :href="route('fleet.vehicles.view', vehicle?.id)">
									<img
										:src="vehicle?.image"
										class="w-100 vehicle-image"
										:alt="vehicle?.name" />
								</Link>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Link } from '@inertiajs/vue3';
import PassengerList from '@/Components/Transport/PassengerList.vue';
import CustomTimeLine from '@/Components/General/CustomTimeLine.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'RideCard',
	components: {
		Link,
		PassengerList,
		CustomTimeLine
	},
	props: {
		ride: {
			type: Object,
			required: true
		}
	},
	computed: {
		points() {
			return [
				{
					label: this.ride.route.origin?.name,
					reference: trans('transport.ride.start_of_ride'),
					time: this.ride.started_at
				},
				{
					label: this.ride.route.destination?.name,
					reference: trans('transport.ride.end_of_ride'),
					time: this.ride.arrived_at
				}
			];
		},
		badgeClass() {
			switch (this.ride?.status) {
				case 'arrived':
					return 'badge-light-success';
				case 'started':
				case 'ongoing':
					return 'badge-light-info';
				case 'canceled':
					return 'badge-light-dark';
			}
		},
		passengers() {
			return this.ride?.attendances?.map((attendance) => attendance.passenger);
		},
		vehicle() {
			return this.ride?.vehicle;
		}
	}
};
</script>
