<template>
	<AssistantLayout :assistant="assistant">
		<div v-if="!viewMode" class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.image') }}
			</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<ImageInput
					v-model="updateAssistantForm.image"
					:error="updateAssistantForm.errors.image"
					:allowedTypesText="
						$t('general.allowed_file_types', { types: 'png, jpg, jpeg' })
					"
					@update:modelValue="updateAssistant" />
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.first_name') }}
			</label>
			<div class="col-8">
				<input
					v-model="updateAssistantForm.first_name"
					class="form-control"
					:placeholder="$t('general.first_name')"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateAssistantForm.errors.first_name" class="text-danger fs-6">
					{{ updateAssistantForm.errors.first_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.last_name') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updateAssistantForm.last_name"
					class="form-control"
					:placeholder="$t('general.last_name')"
					:disabled="viewMode" />
				<div v-if="updateAssistantForm.errors.last_name" class="text-danger fs-6">
					{{ updateAssistantForm.errors.last_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.phone') }}
			</label>
			<div class="col-8">
				<PhoneInput v-model="updateAssistantForm.phone" @phone-validated="validatePhone" />
				<div
					v-if="updateAssistantForm.errors.phone || phoneValidationError"
					class="text-danger fs-6">
					{{ updateAssistantForm.errors.phone || phoneValidationError }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.email') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updateAssistantForm.email"
					class="form-control"
					:placeholder="$t('general.email')"
					:disabled="viewMode" />
				<div v-if="updateAssistantForm.errors.email" class="text-danger fs-6">
					{{ updateAssistantForm.errors.email }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.gender') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateAssistantForm.gender"
					options="UserGender"
					:disabled="viewMode" />
				<div v-if="updateAssistantForm.errors.gender" class="text-danger fs-6">
					{{ updateAssistantForm.errors.gender }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updateAssistantForm.status"
					options="AssistantStatus"
					:disabled="viewMode" />
				<div v-if="updateAssistantForm.errors.status" class="text-danger fs-6">
					{{ updateAssistantForm.errors.status }}
				</div>
			</div>
		</div>

		<div v-if="!viewMode" class="row">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deleteAssistant" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('fleet.assistants.view', assistant.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updateAssistantForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updateAssistant">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</AssistantLayout>
</template>

<script>
import AssistantLayout from '@/Pages/Fleet/Assistants/View/Partials/AssistantLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import PhoneInput from '@/Components/PhoneInput.vue';
import ImageInput from '@/Components/General/ImageInput.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		Link,
		AssistantLayout,
		MultiSelect,
		ConfirmationButton,
		PhoneInput,
		ImageInput
	},
	props: {
		assistant: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updateAssistantForm: useForm({
				id: this.assistant.id,
				first_name: this.assistant.first_name,
				last_name: this.assistant.last_name,
				status: this.assistant.status,
				gender: this.assistant.gender,
				phone: this.assistant.phone,
				email: this.assistant.email,
				default_vehicle_id: this.assistant.default_vehicle_id,
				image: this.assistant.image
			}),
			phoneValidationError: '',
			viewMode: false
		};
	},
	methods: {
		updateAssistant() {
			if (
				this.updateAssistantForm.image &&
				!(this.updateAssistantForm.image instanceof File)
			) {
				this.updateAssistantForm.image = null;
			}
			this.updateAssistantForm.post(route('fleet.assistants.update', this.assistant.id), {
				_method: 'put',
				onError: (errors) => {
					if (errors.phone) {
						this.phoneValidationError = errors.phone[0];
					}
				},
				onSuccess: () => {
					this.updateAssistantForm.image = this.assistant?.image;
				}
			});
		},
		deleteAssistant() {
			router.delete(route('fleet.assistants.update', this.assistant.id));
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', {
						attribute: trans('general.phone')
					});
		}
	}
};
</script>
