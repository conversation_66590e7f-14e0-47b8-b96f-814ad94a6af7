<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<VehiclesToolbar />
		</template>

		<Head :title="$t('nav.vehicles')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<VehiclesFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="vehicles.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<VehiclesTable :vehicles="vehicles.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="colg4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="vehicles.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head } from '@inertiajs/vue3';
import VehiclesToolbar from '@/Pages/Fleet/Vehicles/Partials/VehiclesToolbar.vue';
import VehiclesTable from '@/Pages/Fleet/Vehicles/List/Partials/VehiclesTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import VehiclesFilters from '@/Pages/Fleet/Vehicles/List/Partials/VehiclesFilters.vue';

export default {
	name: 'Main.vue',
	components: {
		BaseLayout,
		Head,
		VehiclesToolbar,
		VehiclesTable,
		Pagination,
		EmptyResults,
		VehiclesFilters
	},
	props: {
		vehicles: Object
	}
};
</script>
