<template>
	<BaseLayout>
		<Head :title="$t('nav.vehicles')" />

		<div class="row g-3 h-100">
			<div class="col-md-4 col-lg-3">
				<div class="card p-4 custom-sticky">
					<div class="d-flex flex-center flex-column mb-5">
						<ImageSymbol :imageSrc="vehicle?.image" :width="100" :height="100" />
					</div>

					<div class="d-flex flex-stack fs-4 py-3">
						<div class="fw-bold text-capitalize">
							{{ $t('fleet.vehicle.details') }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.name') }}
						</div>
						<div class="text-gray-600">
							{{ vehicle?.name }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.make') }}
						</div>
						<div class="text-gray-600">
							{{ vehicle?.make }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-3 text-capitalize">
							{{ $t('general.status') }}
						</div>
						<div class="badge text-capitalize" :class="badgeClass">
							{{ $t('general.enums.' + vehicle?.status) }}
						</div>
					</div>
				</div>
			</div>

			<div class="col-md-8 col-lg-9">
				<PageNav :vehicle="vehicle" class="mb-3" />
				<div class="card h-100">
					<div class="card-body">
						<slot />
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>
<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import PageNav from '@/Pages/Fleet/Vehicles/View/Partials/PageNav.vue';
import ImageUploader from '@/Components/ImageUploader.vue';
import { Link, Head } from '@inertiajs/vue3';
import ImageSymbol from '@/Components/General/ImageSymbol.vue';

export default {
	name: 'VehicleLayout',
	components: {
		Link,
		ImageUploader,
		BaseLayout,
		PageNav,
		Head,
		ImageSymbol
	},
	props: {
		vehicle: {
			type: Object,
			required: true
		}
	},
	computed: {
		badgeClass() {
			switch (this.vehicle?.status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
					return 'badge-light-dark';
				case 'maintenance':
					return 'badge-light-warning';
			}
		}
	}
};
</script>
