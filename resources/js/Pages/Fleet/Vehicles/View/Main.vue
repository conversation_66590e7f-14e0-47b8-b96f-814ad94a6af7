<template>
	<VehicleLayout :vehicle="vehicle">
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.reference') }}
			</label>

			<div class="col-8">
				<input
					v-model="updateVehicleForm.reference"
					:placeholder="$t('general.reference')"
					class="form-control"
					name="fname"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateVehicleForm.errors.reference" class="text-danger fs-6">
					{{ updateVehicleForm.errors.reference }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.name') }}
			</label>

			<div class="col-8">
				<input
					v-model="updateVehicleForm.name"
					:placeholder="$t('general.name')"
					class="form-control"
					name="fname"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateVehicleForm.errors.name" class="text-danger fs-6">
					{{ updateVehicleForm.errors.name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.make') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateVehicleForm.make"
					:placeholder="$t('general.make')"
					class="form-control"
					name="lname"
					:disabled="viewMode" />

				<div v-if="updateVehicleForm.errors.make" class="text-danger fs-6">
					{{ updateVehicleForm.errors.make }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.capacity') }}
			</label>

			<div class="col-8">
				<input
					type="number"
					v-model="updateVehicleForm.capacity"
					class="form-control"
					:placeholder="$t('general.capacity')"
					name="capacity"
					:disabled="viewMode" />

				<div v-if="updateVehicleForm.errors.capacity" class="text-danger fs-6">
					{{ updateVehicleForm.errors.capacity }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.plate') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateVehicleForm.plate"
					:placeholder="$t('general.plate')"
					class="form-control"
					:disabled="viewMode" />

				<div v-if="updateVehicleForm.errors.plate" class="text-danger fs-6">
					{{ updateVehicleForm.errors.plate }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.type') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateVehicleForm.type"
					options="VehicleType"
					:disabled="viewMode" />
				<div v-if="updateVehicleForm.errors.type" class="text-danger fs-6">
					{{ updateVehicleForm.errors.type }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateVehicleForm.status"
					options="VehicleStatus"
					:disabled="viewMode" />
				<div v-if="updateVehicleForm.errors.status" class="text-danger fs-6">
					{{ updateVehicleForm.errors.status }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.region.name') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateVehicleForm.region_id"
					options="Region"
					:disabled="viewMode" />
				<div v-if="updateVehicleForm.errors.region_id" class="text-danger fs-6">
					{{ updateVehicleForm.errors.region_id }}
				</div>
			</div>
		</div>

		<div v-if="!viewMode" class="row">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deleteVehicle" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('fleet.vehicles.view', vehicle?.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updateVehicleForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updateVehicle">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</VehicleLayout>
</template>

<script>
import VehicleLayout from '@/Pages/Fleet/Vehicles/View/Partials/VehicleLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';

export default {
	components: {
		Link,
		VehicleLayout,
		MultiSelect,
		ConfirmationButton
	},
	props: {
		vehicle: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updateVehicleForm: useForm({
				id: this.vehicle?.id,
				reference: this.vehicle?.reference,
				name: this.vehicle?.name,
				make: this.vehicle?.make,
				status: this.vehicle?.status,
				capacity: this.vehicle?.capacity,
				plate: this.vehicle?.plate,
				type: this.vehicle?.type,
				region_id: this.vehicle?.region_id
			}),
			// viewMode: !this.hasPermissions('vehicles.edit')
			viewMode: false
		};
	},
	methods: {
		updateVehicle() {
			this.updateVehicleForm.put(route('fleet.vehicles.update', this.vehicle?.id));
		},
		deleteVehicle() {
			router.delete(route('fleet.vehicles.update', this.vehicle?.id));
		}
	}
};
</script>
