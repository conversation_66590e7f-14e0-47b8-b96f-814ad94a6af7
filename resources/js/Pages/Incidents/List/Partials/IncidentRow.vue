<template>
	<tr class="odd">
		<td>
			<div class="d-flex align-items-center">
				<div class="symbol symbol-40px">
					<div class="symbol-label p-3 bg-light-primary">
						<i class="fa-duotone fa-solid fa-brake-warning fs-2 text-primary"></i>
					</div>
				</div>
			</div>
		</td>

		<td>
			<div class="text-gray-600 text-hover-primary fw-bold fs-5">#{{ incident.id }}</div>
		</td>

		<td v-if="incident?.reporter">
			<ProfileCard :profile="incident.reporter" size="40" :baseRoute="baseRoute" />
		</td>
		<td v-else>
			{{ $t('general.profile_not_found') }}
		</td>

		<td>
			<div class="d-flex align-items-center text-gray-800 text-capitalize">
				<i class="fa-duotone fs-3 text-primary me-3" :class="incidentIcon"></i>
				{{ $t(`general.enums.${incident.type}`) }}
			</div>
		</td>

		<td>
			<div class="text-gray-800">
				{{ createdAt }}
			</div>
		</td>

		<td>
			<IncidentDetailModal :incident="incident" />
		</td>
	</tr>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import StarRating from '@/Components/Review/StarRating.vue';
import ProfileCard from '@/Components/Cards/ProfileCard.vue';
import { format } from 'date-fns';
import IncidentDetailModal from '@/Pages/Incidents/List/Partials/IncidentDetailModal.vue';

export default {
	components: {
		Link,
		StarRating,
		ProfileCard,
		IncidentDetailModal
	},
	props: {
		incident: Object
	},
	computed: {
		createdAt() {
			const date = new Date(this.incident?.created_at);
			return format(date, 'dd/MM/yyyy HH:mm');
		},
		incidentIcon() {
			switch (this.incident?.type) {
				case 'mechanical_issue':
					return 'fa-tools';
				case 'accident':
					return 'fa-car-crash';
				case 'health_emergency':
					return 'fa-heartbeat';
				case 'behavioral_issue':
					return 'fa-exclamation-triangle';
				case 'traffic_delay':
					return 'fa-traffic-cone';
				case 'weather_condition':
					return 'fa-cloud-sun-rain';
				case 'road_blockage':
					return 'fa-road';
				case 'missed_station':
					return 'fa-times-circle';
				case 'other':
					return 'fa-question-circle';
				default:
					return 'fa-info-circle';
			}
		},
		baseRoute() {
			switch (this.incident.reporter_type) {
				case 'Passenger':
					return 'transport.passengers.view';
				case 'Assistant':
					return 'fleet.assistants.view';
				case 'Driver':
					return 'fleet.drivers.view';
				case 'Responsible':
					return 'transport.responsibles.view';
				default:
					return;
			}
		}
	}
};
</script>
