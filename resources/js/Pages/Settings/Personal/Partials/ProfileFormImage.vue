<template>
	<div class="mt-2">
		<div
			class="image-input image-input-empty image-input-outline image-input-placeholder mb-3"
			data-kt-image-input="true">
			<img class="image-input-wrapper w-150px h-150px" :src="previewImagePath" />
			<label
				v-if="!readOnly"
				class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow show"
				data-kt-image-input-action="change">
				<i class="fa-duotone fa-pencil-alt fs-7"></i>
				<input
					type="file"
					name="avatar"
					accept=".png, .jpg, .jpeg"
					@change="previewImage"
					@input="imageForm.image = $event.target.files[0]" />
			</label>
		</div>
		<div v-if="imageForm.errors.image" class="text-danger fs-6">
			{{ imageForm.errors.image }}
		</div>
	</div>
</template>

<script>
import { useForm } from '@inertiajs/vue3';

export default {
	name: 'ProfileFormImage',
	props: {
		readOnly: {
			type: <PERSON>olean,
			default: false
		}
	},
	data() {
		return {
			imageForm: useForm({
				image: null
			}),
			previewImagePath: this.$page.props.user.image
		};
	},
	methods: {
		previewImage(e) {
			this.previewImagePath = URL.createObjectURL(e.target.files[0]);
			this.imageForm.post(route('settings.personal.profile.update.image'), {
				onSuccess: () => location.reload()
			});
		}
	}
};
</script>
