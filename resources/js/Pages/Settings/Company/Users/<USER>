<template>
	<Layout :user="user" :users="users">
		<div class="row my-10 d-flex align-items-center">
			<label class="col-form-label text-right col-lg-6 col-sm-12 fs-5 fw-semibold">
				{{ $t('general.image') }}
			</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<ImageInput
					v-model="updateUserForm.image"
					:error="updateUserForm.errors.image"
					:allowedTypesText="
						$t('general.allowed_file_types', { types: 'png, jpg, jpeg' })
					"
					@update:modelValue="updateUser" />
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.name') }}
			</label>

			<div class="col-8">
				<input
					v-model="updateUserForm.first_name"
					class="form-control"
					name="fname"
					placeholder="First name"
					type="text" />
				<div v-if="updateUserForm.errors.first_name" class="text-danger fs-6">
					{{ updateUserForm.errors.first_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.last_name') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateUserForm.last_name"
					class="form-control"
					placeholder="Last name"
					name="lname" />
				<div v-if="updateUserForm.errors.last_name" class="text-danger fs-6">
					{{ updateUserForm.errors.last_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.email') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateUserForm.email"
					class="form-control"
					:placeholder="$t('general.email')"
					name="email" />
				<div v-if="updateUserForm.errors.email" class="text-danger fs-6">
					{{ updateUserForm.errors.email }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.phone') }}
			</label>

			<div class="col-8">
				<PhoneInput
					v-model="updateUserForm.phone"
					@phone-validated="validatePhone"></PhoneInput>
				<div
					v-if="updateUserForm.errors.phone || phoneValidationError"
					class="text-danger fs-6">
					{{ updateUserForm.errors.phone || phoneValidationError }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>

			<div class="col-8">
				<MultiSelect v-model="updateUserForm.status" options="UserStatus" />
				<div v-if="updateUserForm.errors.status" class="text-danger fs-6">
					{{ updateUserForm.errors.status }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.region.name') }}
			</label>

			<div class="col-8">
				<MultiSelect v-model="updateUserForm.region_id" options="Region" />
				<div v-if="updateUserForm.errors.region_id" class="text-danger fs-6">
					{{ updateUserForm.errors.region_id }}
				</div>
			</div>
		</div>

		<div class="d-flex justify-content-end mt-8">
			<Link
				:href="route('settings.company.users.view', user.id)"
				class="btn btn-sm btn-light mx-1">
				{{ $t('general.cancel') }}
			</Link>
			<button
				:disabled="!updateUserForm.isDirty"
				class="btn btn-sm btn-light-success mx-1"
				@click="updateUser">
				{{ $t('general.save_changes') }}
			</button>
		</div>
	</Layout>
</template>

<script>
import { Link, useForm } from '@inertiajs/vue3';
import Layout from '@/Pages/Settings/Company/Users/<USER>';
import MultiSelect from '@/Components/MultiSelect.vue';
import PhoneInput from '@/Components/PhoneInput.vue';
import ImageInput from '@/Components/General/ImageInput.vue'; // Import ImageInput
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		MultiSelect,
		Layout,
		Link,
		PhoneInput,
		ImageInput // Register ImageInput component
	},
	props: {
		users: {
			required: true
		},
		user: {
			required: true
		}
	},
	data() {
		return {
			updateUserForm: useForm({
				id: this.user.id,
				first_name: this.user?.first_name,
				last_name: this.user?.last_name,
				email: this.user?.email,
				phone: this.user?.phone,
				status: this.user?.status,
				region_id: this.user?.region_id,
				image: this.user?.image // Initialize with user's image
			}),
			phoneValidationError: ''
		};
	},
	methods: {
		updateUser() {
			if (this.updateUserForm.image && !(this.updateUserForm.image instanceof File)) {
				this.updateUserForm.image = null;
			}
			this.updateUserForm.post(route('settings.company.users.update', this.user.id), {
				_method: 'put',
				onSuccess: () => {
					this.updateUserForm.image = this.user?.image;
				}
			});
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', { attribute: trans('general.phone') });
		}
	}
};
</script>
