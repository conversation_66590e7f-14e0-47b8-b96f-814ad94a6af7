<template>
	<BaseLayout>
		<Head :title="$t('nav.settings')" />

		<template v-slot:toolbar>
			<div class="mt-6 app-container container-xxl d-flex flex-stack">
				<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
					<h1
						class="page-heading text-capitalize d-flex text-dark fw-bold fs-2 flex-column justify-content-center my-0">
						{{ $t('company.company_info') }}
					</h1>
				</div>
			</div>
		</template>

		<div class="card">
			<div class="card-body p-7">
				<div class="d-flex align-items-center">
					<div class="symbol symbol-100px border">
						<div class="symbol-label">
							<img
								:src="company.logo"
								alt=""
								class="w-100 h-100 object-fit-contain ratio ratio-1x1 rounded" />
						</div>
					</div>
					<div class="d-flex flex-nowrap ms-10">
						<label
							class="btn btn-outline btn-outline-primary btn-active-light-primary text-capitalize"
							for="logoInput">
							<i class="fa-duotone fa-upload"></i>
							{{ $t('general.upload') }}
						</label>
						<input
							id="logoInput"
							accept=".png, .jpg, .jpeg"
							class="d-none"
							type="file"
							@change="handleLogoChange" />
						<ConfirmationButton
							:action="deleteLogo"
							class="btn btn-outline btn-active-light ms-5 text-capitalize">
							<i class="fa-duotone fa-trash-alt fs-5"></i>
							{{ $t('general.delete') }}
						</ConfirmationButton>
					</div>
				</div>
			</div>
		</div>

		<div class="card card-flush mt-5">
			<div class="card-body">
				<div class="d-flex flex-column mt-5">
					<div class="row g-5">
						<div class="col-md-6">
							<div class="mb-8">
								<label
									class="required form-label text-capitalize fw-semibold fs-5"
									>{{ $t('general.name') }}</label
								>
								<input
									v-model="companyForm.name"
									type="text"
									class="form-control" />
								<small v-if="companyForm.errors.reference" class="text-danger">
									{{ companyForm.errors.name }}
								</small>
							</div>
						</div>
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('company.labels.reference')
								}}</label>
								<input
									v-model="companyForm.reference"
									type="text"
									class="form-control" />
								<small v-if="companyForm.errors.reference" class="text-danger">
									{{ companyForm.errors.reference }}
								</small>
							</div>
						</div>
					</div>
					<div class="row g-5">
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('general.industry')
								}}</label>
								<MultiSelect
									v-model="companyForm.industry"
									:internal-search="false"
									:lazy-search="true"
									:preserve-search="true"
									options="Industry" />
								<small v-if="companyForm.errors.reference" class="text-danger">
									{{ companyForm.errors.industry }}
								</small>
							</div>
						</div>
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('company.labels.language_preferences')
								}}</label>
								<MultiSelect v-model="companyForm.locale" :options="locales" />
								<small v-if="companyForm.errors.locale" class="text-danger">
									{{ companyForm.errors.locale }}
								</small>
							</div>
						</div>
					</div>
					<div class="row g-5">
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('general.emergency_contact')
								}}</label>
								<MultiSelect
									v-model="companyForm.emergency_contact_id"
									:internal-search="false"
									:lazy-search="true"
									:preserve-search="true"
									options="User" />
								<small
									v-if="companyForm.errors.emergency_contact_id"
									class="text-danger">
									{{ companyForm.errors.emergency_contact_id }}
								</small>
							</div>
						</div>
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('general.timezone')
								}}</label>
								<MultiSelect
									v-model="companyForm.timezone"
									:internal-search="false"
									:lazy-search="true"
									:preserve-search="true"
									options="Timezone" />
								<small v-if="companyForm.errors.timezone" class="text-danger">
									{{ companyForm.errors.timezone }}
								</small>
							</div>
						</div>
					</div>
					<div class="row g-5">
						<!-- <div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('general.country')
								}}</label>
								<MultiSelect
									v-model="companyForm.country"
									:internal-search="true"
									:preserve-search="true"
									options="Country" />
								<small v-if="companyForm.errors.country" class="text-danger">
									{{ companyForm.errors.country }}
								</small>
							</div>
						</div> -->

						<div class="col-md-6">
							<div class="fv-row fv-plugins-icon-container">
								<label class="form-label text-capitalize">{{
									$t('geo.location.name')
								}}</label>
								<div class="d-flex align-items-center w-100">
									<LocationInput
										v-model="companyForm.location_id"
										showEditButton />
								</div>
								<div
									v-if="companyForm.errors.location_id"
									class="fv-plugins-message-container invalid-feedback">
									{{ companyForm.errors.location_id }}
								</div>
							</div>
						</div>
					</div>
					<!-- <div class="row g-5">
						<div class="col-md-6">
							<div class="mb-8">
								<label class="form-label text-capitalize fw-semibold fs-5">{{
									$t('company.labels.address')
								}}</label>
								<textarea
									v-model="companyForm.address"
									type="text"
									class="form-control"></textarea>
								<small v-if="companyForm.errors.address" class="text-danger">
									{{ companyForm.errors.address }}
								</small>
							</div>
						</div>
					</div> -->
				</div>
			</div>
			<div class="card-footer">
				<div class="d-flex flex-nowrap justify-content-end">
					<button
						class="btn btn-outline btn-active-light text-capitalize me-5"
						type="button"
						@click="cancel">
						{{ $t('general.cancel') }}
					</button>
					<button
						:disabled="!companyForm.isDirty"
						class="btn btn-primary"
						type="button"
						@click="updateCompanyInfo">
						{{ $t('general.save_changes') }}
					</button>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import { Head, router, useForm } from '@inertiajs/vue3';
import Constants from '@/Components/Constants.vue';
import LocationInput from '@/Components/Location/LocationInput.vue';

export default {
	components: {
		MultiSelect,
		BaseLayout,
		ConfirmationButton,
		Head,
		LocationInput
	},
	data() {
		return {
			companyForm: useForm({
				id: this.company.id,
				name: this.company.name,
				reference: this.company.reference,
				industry: this.company.industry,
				locale: this.company.locale,
				timezone: this.company.timezone,
				emergency_contact_id: this.company.emergency_contact_id,
				logo: null,
				delete_logo: false,
				location_id: this.company.location_id
			}),
			locales: Constants.locales
		};
	},
	props: {
		company: {
			required: true
		}
	},
	methods: {
		handleLogoChange(event) {
			const file = event.target.files[0];
			if (file) {
				this.companyForm.logo = file;
				this.companyForm.delete_logo = false;
				this.updateCompanyInfo();
			}
		},
		deleteLogo() {
			this.companyForm.delete_logo = true;
			this.companyForm.logo = null;
			this.updateCompanyInfo();
		},
		updateCompanyInfo() {
			this.companyForm.post(route('settings.company.update', { preserveScroll: true }));
		},
		cancel() {
			this.companyForm.reset();
			router.reload();
		}
	}
};
</script>
