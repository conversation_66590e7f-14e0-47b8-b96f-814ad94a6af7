<template>
	<Layout :groups="groups" :group="group">
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 fw-semibold text-capitalize">
				{{ $t('general.reference') }}
			</label>

			<div class="col-8">
				<input type="text" v-model="updateGroupForm.reference" class="form-control" />

				<div v-if="updateGroupForm.errors.reference" class="text-danger fs-6">
					{{ updateGroupForm.errors.reference }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 fw-semibold text-capitalize">
				{{ $t('general.name') }}
			</label>

			<div class="col-8">
				<input type="text" v-model="updateGroupForm.name" class="form-control" />

				<div v-if="updateGroupForm.errors.name" class="text-danger fs-6">
					{{ updateGroupForm.errors.name }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 fw-semibold text-capitalize">
				{{ $t('general.description') }}
			</label>

			<div class="col-8">
				<textarea type="text" v-model="updateGroupForm.description" class="form-control" />

				<div v-if="updateGroupForm.errors.description" class="text-danger fs-6">
					{{ updateGroupForm.errors.description }}
				</div>
			</div>
		</div>

		<div class="d-flex justify-content-end mt-8">
			<Link
				:href="route('settings.transport.groups.view', group.id)"
				class="btn btn-sm btn-light mx-1">
				{{ $t('general.cancel') }}
			</Link>
			<button
				:disabled="!updateGroupForm.isDirty"
				class="btn btn-sm btn-light-success mx-1"
				@click="updateGroup">
				{{ $t('general.save_changes') }}
			</button>
		</div>
	</Layout>
</template>

<script>
import { useForm } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';
import Layout from '@/Pages/Settings/Transport/Groups/Layout.vue';
import { Link } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';

export default {
	components: {
		Layout,
		EmptyResults,
		Link,
		MultiSelect
	},
	props: {
		groups: {
			required: true
		},
		group: {
			required: true
		}
	},
	data() {
		return {
			updateGroupForm: useForm({
				id: this.group?.id,
				reference: this.group?.reference,
				name: this.group?.name,
				description: this.group?.description
			})
		};
	},
	methods: {
		updateGroup() {
			this.updateGroupForm.put(route('settings.transport.groups.update', this.group.id));
		}
	}
};
</script>
