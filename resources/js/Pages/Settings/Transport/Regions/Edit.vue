<template>
	<Layout :regions="regions" :region="region">
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 fw-semibold text-capitalize">
				{{ $t('general.name') }}
			</label>

			<div class="col-8">
				<input type="text" v-model="updateRegionForm.name" class="form-control" />

				<div v-if="updateRegionForm.errors.name" class="text-danger fs-6">
					{{ updateRegionForm.errors.name }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 fw-semibold text-capitalize">
				{{ $t('general.emergency_contact') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateRegionForm.emergency_contact_id"
					:internal-search="false"
					:lazy-search="true"
					:preserve-search="true"
					options="User" />

				<div v-if="updateRegionForm.errors.emergency_contact_id" class="text-danger fs-6">
					{{ updateRegionForm.errors.emergency_contact_id }}
				</div>
			</div>
		</div>

		<div class="d-flex justify-content-end mt-8">
			<Link
				:href="route('settings.transport.regions.view', region.id)"
				class="btn btn-sm btn-light mx-1">
				{{ $t('general.cancel') }}
			</Link>
			<button
				:disabled="!updateRegionForm.isDirty"
				class="btn btn-sm btn-light-success mx-1"
				@click="updateRegion">
				{{ $t('general.save_changes') }}
			</button>
		</div>
	</Layout>
</template>

<script>
import { useForm } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';
import Layout from '@/Pages/Settings/Transport/Regions/Layout.vue';
import { Link } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';

export default {
	components: {
		Layout,
		EmptyResults,
		Link,
		MultiSelect
	},
	props: {
		regions: {
			required: true
		},
		region: {
			required: true
		}
	},
	data() {
		return {
			updateRegionForm: useForm({
				id: this.region?.id,
				name: this.region?.name,
				emergency_contact_id: this.region?.emergency_contact_id
			})
		};
	},
	methods: {
		updateRegion() {
			this.updateRegionForm.put(route('settings.transport.regions.update', this.region.id));
		}
	}
};
</script>
