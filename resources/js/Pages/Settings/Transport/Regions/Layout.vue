<template>
	<BaseLayout>
		<Head :title="$t('nav.regions')" />

		<div class="d-flex flex-stack mb-4">
			<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
				<h1
					class="page-heading text-capitalize d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
					{{ $t('transport.settings.pages.regions') }}
				</h1>
			</div>

			<div class="d-flex align-items-center gap-2 gap-lg-3">
				<NewRegionModal />
			</div>
		</div>

		<div class="row min-h-100 g-3">
			<div class="col-md-4">
				<div class="card nav nav-pills full-height h-100">
					<div class="card-body w-100 p-4">
						<ul
							v-if="region"
							class="nav nav-pills nav-pills-custom flex-column border-transparent fs-5 fw-semibold">
							<li v-for="region in regions" class="nav-item m-0 mb-2">
								<Link
									:class="{
										active: route().current(
											'settings.transport.regions.*',
											region.id
										)
									}"
									:href="route('settings.transport.regions.view', region.id)"
									class="nav-link d-flex align-items-center justify-content-between text-gray-700 text-active-primary ms-0 py-3 ps-6 border-0">
									{{ region.name }}

									<span
										class="bullet-custom position-absolute start-0 top-0 w-3px h-100 bg-primary rounded-end"></span>
								</Link>
							</li>
						</ul>

						<!--                            <EmptyResults empty v-if="!region"/>-->
					</div>
				</div>
			</div>

			<div class="col-md-8">
				<div v-if="region" class="card h-100">
					<div
						class="p-4 pb-0 d-flex align-items-start justify-content-between border-bottom">
						<div class="text-capitalize">
							<h3 class="card-label fw-bold text-gray-800 mb-4">
								{{ region.name }}
							</h3>
							<div class="d-flex overflow-auto">
								<ul
									class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold flex-nowrap">
									<li class="nav-item">
										<Link
											:class="{
												active: route().current(
													'settings.transport.regions.view',
													region.id
												)
											}"
											:href="
												route('settings.transport.regions.view', region.id)
											"
											class="nav-link m-0 py-3 px-6 text-active-primary">
											{{ $t('general.info') }}
										</Link>
									</li>
								</ul>
							</div>
						</div>
						<div class="card-toolbar">
							<Link
								:href="route('settings.transport.regions.edit', region.id)"
								class="btn btn-sm btn-light-info mx-2">
								<i class="fa-duotone fa-pen fs-5"></i>
								{{ $t('general.edit') }}
							</Link>
							<ConfirmationButton
								:action="deleteRegion"
								class="btn btn-sm btn-light-danger">
								<i class="fa-duotone fa-trash-alt fs-5"></i>
								{{ $t('general.delete') }}
							</ConfirmationButton>
						</div>
					</div>
					<div class="card-body">
						<slot />
					</div>
				</div>
				<div v-else class="card h-100">
					<EmptyResults
						class="w-50 mx-auto"
						:message="$t('general.messages.no_regions')" />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import NewRegionModal from '@/Pages/Settings/Transport/Regions/Partials/NewRegionModal.vue';

export default {
	components: {
		BaseLayout,
		EmptyResults,
		Link,
		ConfirmationButton,
		Head,
		NewRegionModal
	},
	props: {
		regions: {
			required: true
		},
		region: {
			required: false
		}
	},
	methods: {
		deleteRegion() {
			router.delete(route('settings.transport.regions.delete', this.region.id));
		}
	}
};
</script>
