<template>
	<Layout :plan="plan.data">
		<Head :title="$t('nav.schedules')" />

		<FullCalendar ref="calendar" :options="calendarOptions" />

		<Teleport to="body">
			<VueFinalModal v-model="showCreateNewScheduleFormModel">
				<NewScheduleForm
					v-if="showCreateNewScheduleFormModel"
					@close="showCreateNewScheduleFormModel = false"
					:plan="plan.data" />
			</VueFinalModal>
		</Teleport>

		<Teleport to="body">
			<VueFinalModal v-model="expandedScheduleOpen">
				<EditScheduleForm
					v-if="expandedSchedule"
					@close="expandedScheduleOpen = !!(expandedSchedule = undefined)"
					:schedule="expandedSchedule" />
			</VueFinalModal>
		</Teleport>
	</Layout>
</template>

<script>
import { VueFinalModal } from 'vue-final-modal';
import Layout from './Partials/Layout.vue';
import { Head } from '@inertiajs/vue3';
import FullCalendar from '@fullcalendar/vue3';
import allLocales from '@fullcalendar/core/locales-all';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import rrulePlugin from '@fullcalendar/rrule';
import NewScheduleForm from './Partials/NewScheduleForm.vue';
import EditScheduleForm from './Partials/EditScheduleForm.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'Schedules',
	components: {
		VueFinalModal,
		Head,
		Layout,
		FullCalendar,
		NewScheduleForm,
		EditScheduleForm
	},
	props: {
		plan: {
			type: Object,
			required: true
		},
		schedules: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			showCreateNewScheduleFormModel: false,
			expandedSchedule: undefined,
			expandedScheduleOpen: false
		};
	},
	computed: {
		calendarOptions() {
			return {
				firstDay: 1,
				plugins: [dayGridPlugin, rrulePlugin, interactionPlugin],
				initialView: 'dayGridWeek',
				eventClassNames: 'cursor-pointer',
				eventDisplay: 'block',
				locales: allLocales,
				locale: this.$locale(),
				eventClick: (info) => {
					this.expandedSchedule = this.schedules.data
						.filter((e) => e.id == info.event.id)
						.pop();
					this.expandedScheduleOpen = true;
				},
				headerToolbar: {
					left: 'prev,next dayGridMonth,dayGridWeek',
					center: 'title',
					right: 'createSchedule'
				},
				customButtons: {
					createSchedule: {
						text: trans('plan.schedule.create'),
						click: () => (this.showCreateNewScheduleFormModel = true)
					}
				},
				events: this.formatEvents(this.schedules.data),
				eventContent: function (arg) {
					const startTime = arg.event.extendedProps.startTime;
					const endTime = arg.event.extendedProps.endTime;
					const color = arg.event.extendedProps.color;
					let customHtml = `
				    <div class="fc-event-main-frame text-bg-white d-flex flex-column rounded">
						<div class="fc-event-time fw-semibold py-0 mb-1">
							<i class="fa-duotone fa-clock fs-7 me-1" style="color: ${color}"></i>
							${startTime} - ${endTime}
						</div>
						<div class="fc-event-title-container py-0">
							<div class="fc-event-title fc-sticky py-0">
								<i class="fa-duotone fa-route fs-7 me-1" style="color: ${color}"></i>
								${arg.event.title}
							</div>
						</div>
					</div>
				  `;
					return { html: customHtml };
				},
				eventDidMount: this.styleEventElement
			};
		}
	},
	methods: {
		formatEvents(schedules) {
			return schedules.map((schedule) => {
				const startTime = `${String(schedule.start_time.hours).padStart(2, '0')}:${String(
					schedule.start_time.minutes
				).padStart(2, '0')}`;

				const endTime = `${String(schedule.arrival_time.hours).padStart(2, '0')}:${String(
					schedule.arrival_time.minutes
				).padStart(2, '0')}`;

				let event = {
					title: `${schedule.route?.name}`,
					date: schedule.start_date,
					id: schedule.id,
					color: schedule.color,
					extendedProps: {
						color: schedule.color,
						startTime: startTime,
						endTime: endTime
					}
				};

				if (schedule.frequency) {
					event.rrule = {
						freq: schedule.frequency,
						bymonthday: schedule.frequency === 'monthly' ? schedule.days : undefined,
						byweekday:
							schedule.frequency === 'weekly'
								? schedule.days.map(
										(day) => ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'][day]
									)
								: undefined,
						dtstart: schedule.start_date,
						until: schedule.end_date
					};
				}

				return event;
			});
		},
		styleEventElement({ el, event }) {
			el.classList.add(
				'cursor-pointer',
				'border-hover-primary',
				'hover-elevate-up',
				'shadow-sm',
				'border-left-3',
				'bg-white',
				'py-1'
			);
		}
	}
};
</script>

<style>
.fc .fc-createSchedule-button {
	color: var(--kt-primary);
}
</style>
