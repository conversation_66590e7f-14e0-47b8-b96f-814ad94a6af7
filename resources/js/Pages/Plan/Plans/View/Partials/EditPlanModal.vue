<template>
	<button type="submit" class="btn btn-sm btn-light-primary" @click="showModal = true">
		<i class="fa-duotone fa-pencil"></i>
		{{ $t('general.edit') }}
	</button>

	<VueFinalModal v-model="showModal">
		<div v-if="showModal" class="modal fade show d-block">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h3 class="modal-title text-capitalize">
							{{ $t('plan.edit') }}
						</h3>
						<div
							@click="showModal = false"
							class="btn btn-icon btn-sm btn-active-light-primary ms-2">
							<i class="fa-duotone fa-times fs-1"></i>
						</div>
					</div>

					<div class="modal-body">
						<div class="card-body">
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.name') }}
								</label>
								<div class="col-8">
									<input
										type="text"
										v-model="updatePlanForm.name"
										class="form-control" />

									<div v-if="updatePlanForm.errors.name" class="text-danger fs-6">
										{{ updatePlanForm.errors.name }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.status') }}
								</label>
								<div class="col-8">
									<MultiSelect
										v-model="updatePlanForm.status"
										options="PlanStatus" />

									<div
										v-if="updatePlanForm.errors.status"
										class="text-danger fs-6">
										{{ updatePlanForm.errors.status }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('fleet.driver.name') }}
								</label>
								<div class="col-8">
									<MultiSelect
										v-model="updatePlanForm.driver_id"
										options="Driver" />

									<div
										v-if="updatePlanForm.errors.driver_id"
										class="text-danger fs-6">
										{{ updatePlanForm.errors.driver_id }}
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="modal-footer">
						<button
							type="button"
							class="btn btn-light"
							@click="
								showModal = false;
								updatePlanForm.reset();
							">
							{{ $t('general.cancel') }}
						</button>
						<button
							:disabled="!updatePlanForm.isDirty"
							type="button"
							class="btn btn-primary"
							@click="updatePlan">
							{{ $t('general.save_changes') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</VueFinalModal>
</template>

<script>
import { VueFinalModal } from 'vue-final-modal';
import { useForm } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';

export default {
	name: 'NewRouteModal',
	components: {
		VueFinalModal,
		MultiSelect
	},
	props: {
		plan: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			showModal: false,
			updatePlanForm: useForm({
				name: this.plan?.name,
				status: this.plan?.status,
				driver_id: this.plan?.driver_id
			})
		};
	},
	methods: {
		updatePlan() {
			this.updatePlanForm.put(route('plans.update', this.plan.id), {
				onSuccess: () => {
					this.updatePlanForm.reset();
					this.showModal = false;
				}
			});
		}
	}
};
</script>
