<template>
	<BaseLayout>
		<Head :title="$t('nav.schedules')" />

		<div>
			<div class="rounded bg-white border">
				<div class="p-3">
					<div class="d-flex flex-wrap flex-sm-nowrap">
						<div class="me-5">
							<div class="symbol symbol-100px">
								<div class="symbol-label p-3 bg-light-primary">
									<i
										class="fa-duotone fa-clipboard-list-check text-primary fs-4x"></i>
								</div>
							</div>
						</div>
						<div class="flex-grow-1">
							<div class="text-gray-900 fs-2 fw-bold pb-3">
								{{ plan.name }}
							</div>
							<div class="d-flex flex-wrap flex-stack m-0">
								<div class="d-flex flex-column flex-grow-1 pe-8">
									<div class="d-flex flex-wrap">
										<div class="pe-8">
											<div
												class="fw-semibold fs-6 text-gray-400 text-capitalize">
												{{ $t('general.status') }}
											</div>
											<div class="d-flex align-items-center">
												<div class="fs-2 text-gray-700 fw-bold">
													<div
														:class="
															plan.status === 'active'
																? 'text-success'
																: 'text-warning'
														"
														class="text-capitalize fs-6">
														{{ $t('general.enums.' + plan.status) }}
													</div>
												</div>
											</div>
										</div>

										<div class="pe-6">
											<div
												class="fw-semibold fs-6 text-gray-400 text-capitalize">
												{{ $t('general.date_added') }}
											</div>
											<div class="d-flex align-items-center">
												<div class="fs-5 text-gray-700 fw-bold">
													{{ plan.created_at }}
												</div>
											</div>
										</div>

										<div class="pe-6">
											<div class="d-flex align-items-center">
												<Link
													:class="{
														active: route().current(
															'fleet.drivers.*',
															plan.driver.id
														)
													}"
													:href="
														route('fleet.drivers.view', plan.driver.id)
													"
													class="d-flex align-items-center text-gray-700 text-active-primary m-0">
													<div
														class="cursor-pointer symbol symbol-30px symbol-md-40px p-1"
														@click="toggle">
														<img
															:src="plan.driver.image"
															alt="driver" />
													</div>

													<div class="ms-3 fs-4 text-gray-600">
														<div class="fw-semibold">
															{{ plan.driver.name }}
														</div>
														<span
															:class="badgeClass"
															class="badge text-capitalize">
															{{
																$t(
																	`general.enums.${plan.driver.status}`
																)
															}}
														</span>
													</div>
												</Link>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div>
							<EditPlanModal :plan="plan" />
							<ConfirmationButton
								:action="remove"
								class="btn btn-sm btn-danger ms-2 my-1">
								<i class="fa-duotone fa-trash-alt fs-5"></i>
								{{ $t('general.delete') }}
							</ConfirmationButton>
						</div>
					</div>
				</div>
			</div>

			<div class="card mt-3">
				<div class="card-body">
					<slot />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Link, router, Head } from '@inertiajs/vue3';
import EditPlanModal from '@/Pages/Plan/Plans/View/Partials/EditPlanModal.vue';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';

export default {
	components: {
		Link,
		BaseLayout,
		EditPlanModal,
		ConfirmationButton,
		Head
	},
	data() {
		return {};
	},
	props: {
		plan: {
			type: Object,
			required: true
		}
	},
	computed: {
		badgeClass() {
			switch (this.plan.driver.status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
					return 'badge-light-dark';
			}
		}
	},
	methods: {
		remove() {
			router.delete(route('plans.delete', this.plan.id));
		}
	}
};
</script>
