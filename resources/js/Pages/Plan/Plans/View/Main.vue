<template>
	<Layout :plan="plan.data">
		<div class="row fs-4 my-5">
			<label class="col-lg-4 fw-semibold text-muted text-capitalize">
				{{ $t('general.name') }}
			</label>
			<div class="col-lg-8 fw-bold text-gray-800">
				<input v-model="form.name" class="form-control" />
				<div v-if="form.errors.name" class="text-danger fs-6">
					{{ form.errors.name }}
				</div>
			</div>
		</div>
		<div class="row fs-4 my-5">
			<label class="col-lg-4 fw-semibold text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>
			<div class="col-lg-8 fw-bold text-gray-800">
				<MultiSelect v-model="form.status" options="PlanStatus" />
				<div v-if="form.errors.status" class="text-danger fs-6">
					{{ form.errors.status }}
				</div>
			</div>
		</div>
		<div class="row fs-4 my-5">
			<label class="col-lg-4 fw-semibold text-muted text-capitalize">
				{{ $t('fleet.driver.name') }}
			</label>
			<div class="col-lg-8 fw-bold text-gray-800">
				<MultiSelect v-model="form.driver_id" size="sm" options="Driver" />
				<div v-if="form.errors.driver_id" class="text-danger fs-6">
					{{ form.errors.driver_id }}
				</div>
			</div>
		</div>

		<div class="d-flex justify-content-end">
			<ConfirmationButton :action="remove" class="btn btn-sm btn-danger ms-4">
				<i class="fa-duotone fa-trash-alt fs-5"></i>
				{{ $t('general.delete') }}
			</ConfirmationButton>
			<button
				:disabled="!form.isDirty"
				@click.prevent="save"
				type="submit"
				class="btn btn-sm btn-success ms-2">
				{{ $t('general.save_changes') }}
			</button>
		</div>
	</Layout>
</template>

<script>
import Layout from './Partials/Layout.vue';
import { useForm } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';

export default {
	components: {
		ConfirmationButton,
		MultiSelect,
		Layout
	},
	props: {
		plan: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			form: useForm({
				name: this.plan.data.name,
				status: this.plan.data.status,
				driver_id: this.plan.data.driver_id
			})
		};
	},
	methods: {
		save() {
			this.form.put(route('plans.update', this.plan.data.id));
		},
		remove() {
			this.form.delete(route('plans.delete', this.plan.data.id));
		}
	}
};
</script>
