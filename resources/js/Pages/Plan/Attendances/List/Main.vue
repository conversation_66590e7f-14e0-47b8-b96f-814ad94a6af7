<template>
	<BaseLayout>
		<Head :title="$t('nav.attendances')" />

		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<template v-slot:toolbar>
			<div
				id="kt_app_toolbar_container"
				class="mt-6 app-container container-xxl d-flex flex-stack">
				<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
					<h1
						class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0 text-capitalize">
						{{ $t('plan.attendance.names') }}
					</h1>
				</div>
			</div>
		</template>

		<div class="card full-height">
			<div class="d-flex p-4">
				<AttendancesFilters />
			</div>

			<div class="card-body py-0">
				<div
					v-if="attendances.data.length"
					class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
					<div class="table-responsive h-100">
						<AttendancesTable :attendances="attendances.data" />
					</div>
				</div>

				<div v-else class="row justify-content-center">
					<EmptyResults class="col-md-4 col-sm-6" />
				</div>
			</div>

			<div class="card-footer">
				<div class="d-flex align-items-center justify-content-end justify-content-md-end">
					<Pagination :links="attendances.meta.links"></Pagination>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import AttendancesTable from './Partials/AttendancesTable.vue';
import AttendancesFilters from './Partials/AttendancesFilters.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';

export default {
	components: {
		HeaderMenu,
		BaseLayout,
		Pagination,
		AttendancesTable,
		AttendancesFilters,
		EmptyResults,
		Head,
		Link
	},
	data() {
		return {
			menu: [
				{
					hasPermission: true,
					active: route().current('plans.attendances.*'),
					route: route('plans.attendances.list'),
					icon: 'fa-duotone fa-clipboard-list-check',
					content: 'plan.attendance.names'
				}
			]
		};
	},
	props: {
		attendances: {
			type: Object,
			required: true
		}
	}
};
</script>
