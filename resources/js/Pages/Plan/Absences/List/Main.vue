<template>
	<BaseLayout>
		<Head :title="$t('nav.absences')" />

		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<template v-slot:toolbar>
			<div
				id="kt_app_toolbar_container"
				class="mt-6 app-container container-xxl d-flex flex-stack">
				<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
					<h1
						class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0 text-capitalize">
						{{ $t('plan.absence.names') }}
					</h1>
				</div>
				<NewAbsenceForm />
			</div>
		</template>

		<div class="card full-height">
			<div class="d-flex p-4">
				<AbsencesFilters />
			</div>

			<div class="card-body py-0">
				<div
					v-if="absences.data.length"
					class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
					<div class="table-responsive h-100">
						<AbsencesTable :absences="absences.data" />
					</div>
				</div>

				<div v-else class="row justify-content-center">
					<EmptyResults class="col-md-4 col-sm-6" />
				</div>
			</div>

			<div class="card-footer">
				<div class="d-flex align-items-center justify-content-end justify-content-md-end">
					<Pagination :links="absences.meta.links"></Pagination>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import AbsencesTable from './Partials/AbsencesTable.vue';
import AbsencesFilters from './Partials/AbsencesFilters.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import NewAbsenceForm from './Partials/NewAbsenceForm.vue';

export default {
	components: {
		HeaderMenu,
		BaseLayout,
		Pagination,
		AbsencesTable,
		AbsencesFilters,
		EmptyResults,
		Head,
		Link,
		NewAbsenceForm
	},
	data() {
		return {
			menu: [
				{
					hasPermission: true,
					active: route().current('plans.attendances.*'),
					route: route('plans.attendances.list'),
					icon: 'fa-duotone fa-clipboard-list-check',
					content: 'plan.attendance.names'
				}
			]
		};
	},
	props: {
		absences: {
			type: Object,
			required: true
		}
	}
};
</script>
