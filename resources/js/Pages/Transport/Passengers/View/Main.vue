<template>
	<PassengerLayout :passenger="passenger">
		<div v-if="!viewMode" class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.image') }}
			</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<ImageInput
					v-model="updatePassengerForm.image"
					:error="updatePassengerForm.errors.image"
					:allowedTypesText="
						$t('general.allowed_file_types', { types: 'png, jpg, jpeg' })
					"
					@update:modelValue="updatePassenger" />
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.reference') }}
			</label>
			<div class="col-8">
				<input
					v-model="updatePassengerForm.reference"
					class="form-control"
					:placeholder="$t('general.reference')"
					type="text"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.reference" class="text-danger fs-6">
					{{ updatePassengerForm.errors.reference }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.first_name') }}
			</label>
			<div class="col-8">
				<input
					v-model="updatePassengerForm.first_name"
					class="form-control"
					placeholder="First name"
					type="text"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.first_name" class="text-danger fs-6">
					{{ updatePassengerForm.errors.first_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.last_name') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updatePassengerForm.last_name"
					class="form-control"
					placeholder="Last name"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.last_name" class="text-danger fs-6">
					{{ updatePassengerForm.errors.last_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.birthdate') }}
			</label>
			<div class="col-8">
				<DatePicker v-model="updatePassengerForm.birthdate" :disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.birthdate" class="text-danger fs-6">
					{{ updatePassengerForm.errors.birthdate }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.gender') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updatePassengerForm.gender"
					options="UserGender"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.gender" class="text-danger fs-6">
					{{ updatePassengerForm.errors.gender }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.email') }}
			</label>
			<div class="col-8">
				<input
					type="text"
					v-model="updatePassengerForm.email"
					class="form-control"
					:placeholder="$t('general.email')"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.email" class="text-danger fs-6">
					{{ updatePassengerForm.errors.email }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.phone') }}
			</label>
			<div class="col-8">
				<PhoneInput v-model="updatePassengerForm.phone" @phone-validated="validatePhone" />
				<div
					v-if="updatePassengerForm.errors.phone || phoneValidationError"
					class="text-danger fs-6">
					{{ updatePassengerForm.errors.phone || phoneValidationError }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updatePassengerForm.status"
					options="UserStatus"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.status" class="text-danger fs-6">
					{{ updatePassengerForm.errors.status }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.region.name') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updatePassengerForm.region_id"
					options="Region"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.region_id" class="text-danger fs-6">
					{{ updatePassengerForm.errors.region_id }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.group.name') }}
			</label>
			<div class="col-8">
				<MultiSelect
					v-model="updatePassengerForm.group_id"
					options="Group"
					:disabled="viewMode" />
				<div v-if="updatePassengerForm.errors.group_id" class="text-danger fs-6">
					{{ updatePassengerForm.errors.group_id }}
				</div>
			</div>
		</div>

		<div v-if="!viewMode" class="row">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deletePassenger" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('transport.passengers.view', passenger.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updatePassengerForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updatePassenger">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</PassengerLayout>
</template>

<script>
import PassengerLayout from '@/Pages/Transport/Passengers/View/Partials/PassengerLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import DatePicker from '@/Components/DatePicker.vue';
import PhoneInput from '@/Components/PhoneInput.vue';
import ImageInput from '@/Components/General/ImageInput.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		Link,
		PassengerLayout,
		MultiSelect,
		DatePicker,
		ConfirmationButton,
		PhoneInput,
		ImageInput
	},
	props: {
		passenger: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updatePassengerForm: useForm({
				id: this.passenger.id,
				reference: this.passenger.reference,
				first_name: this.passenger.first_name,
				last_name: this.passenger.last_name,
				email: this.passenger.email,
				phone: this.passenger.phone,
				status: this.passenger.status,
				gender: this.passenger.gender,
				birthdate: this.passenger.birthdate,
				region_id: this.passenger.region_id,
				group_id: this.passenger.group_id,
				image: this.passenger.image
			}),
			phoneValidationError: '',
			viewMode: false
		};
	},
	methods: {
		updatePassenger() {
			if (
				this.updatePassengerForm.image &&
				!(this.updatePassengerForm.image instanceof File)
			) {
				this.updatePassengerForm.image = null;
			}
			this.updatePassengerForm.post(route('transport.passengers.update', this.passenger.id), {
				_method: 'put',
				onError: (errors) => {
					if (errors.phone) {
						this.phoneValidationError = errors.phone[0];
					}
				},
				onSuccess: () => {
					this.updatePassengerForm.image = this.passenger?.image;
				}
			});
		},
		deletePassenger() {
			router.delete(route('transport.passengers.update', this.passenger.id));
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', {
						attribute: trans('general.phone')
					});
		}
	}
};
</script>
