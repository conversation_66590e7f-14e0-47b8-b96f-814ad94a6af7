<template>
	<BaseLayout>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<template v-slot:toolbar>
			<PassengersToolbar />
		</template>

		<Head :title="$t('nav.passengers')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<PassengersFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="passengers.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<PassengersTable :passengers="passengers.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="col-4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="passengers.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import PassengersToolbar from '@/Pages/Transport/Passengers/Partials/PassengersToolbar.vue';
import PassengersTable from '@/Pages/Transport/Passengers/List/Partials/PassengersTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import PassengersFilters from '@/Pages/Transport/Passengers/List/Partials/PassengersFilters.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'Main.vue',
	data() {
		return {
			menu: [
				{
					hasPermission: true,
					active: route().current('transport.responsibles.*'),
					route: route('transport.responsibles.list'),
					icon: 'fa-duotone fa-hands-holding-child',
					content: trans('transport.responsible.names')
				}
			]
		};
	},
	props: {
		passengers: Object
	},
	components: {
		BaseLayout,
		Head,
		router,
		PassengersToolbar,
		PassengersTable,
		Pagination,
		EmptyResults,
		PassengersFilters,
		HeaderMenu
	}
};
</script>
