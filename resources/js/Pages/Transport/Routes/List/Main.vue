<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<RoutesToolbar />
		</template>

		<Head :title="$t('nav.routes')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6">
						<RoutesFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="routes.data.length > 0"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<RoutesTable :routes="routes.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="colg4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="routes.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import RoutesToolbar from '@/Pages/Transport/Routes/Partials/RoutesToolbar.vue';
import RoutesTable from '@/Pages/Transport/Routes/List/Partials/RoutesTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import RoutesFilters from '@/Pages/Transport/Routes/List/Partials/RoutesFilters.vue';

export default {
	name: 'Main.vue',
	props: {
		routes: Object
	},
	components: {
		BaseLayout,
		Head,
		router,
		RoutesToolbar,
		RoutesTable,
		Pagination,
		EmptyResults,
		RoutesFilters
	}
};
</script>
