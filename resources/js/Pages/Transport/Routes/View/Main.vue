<template>
	<RouteLayout :singleRoute="singleRoute">
		<div class="d-flex flex-column gap-5 gap-md-7">
			<div class="fv-row fv-plugins-icon-container">
				<label class="form-label text-capitalize">
					{{ $t('general.reference') }}
				</label>
				<input v-model="updateRouteForm.reference" class="form-control" />
				<div
					v-if="updateRouteForm.errors.reference"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.reference }}
				</div>
			</div>
			<div class="fv-row fv-plugins-icon-container">
				<label class="required form-label text-capitalize">
					{{ $t('general.name') }}
				</label>
				<input
					v-model="updateRouteForm.name"
					class="form-control"
					name="location_name"
					placeholder="Route name" />
				<div
					v-if="updateRouteForm.errors.name"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.name }}
				</div>
			</div>
			<div class="fv-row fv-plugins-icon-container">
				<label class="required form-label text-capitalize">
					{{ $t('general.status') }}
				</label>
				<MultiSelect
					v-model="updateRouteForm.status"
					:placeholder="$t('general.status')"
					options="RouteStatus" />
				<div
					v-if="updateRouteForm.errors.status"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.status }}
				</div>
			</div>
			<div class="form-check form-check-custom form-check-solid">
				<input
					v-model="updateRouteForm.static"
					class="form-check-input"
					type="checkbox"
					id="is_primary" />
				<label class="form-check-label" for="is_primary">
					{{ $t('transport.route.labels.static') }}
				</label>
				<div
					v-if="updateRouteForm.errors.primary"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.primary }}
				</div>
			</div>
			<div class="fv-row fv-plugins-icon-container">
				<label class="form-label text-capitalize">{{
					$t('transport.route.origin.name')
				}}</label>
				<div class="d-flex align-items-center w-100">
					<LocationInput v-model="updateRouteForm.origin_id" showEditButton />
				</div>
				<div
					v-if="updateRouteForm.errors.origin_id"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.origin_id }}
				</div>
			</div>
			<div class="fv-row fv-plugins-icon-container">
				<label class="form-label text-capitalize">{{
					$t('transport.route.destination.name')
				}}</label>
				<div class="d-flex align-items-center w-100">
					<LocationInput v-model="updateRouteForm.destination_id" showEditButton />
				</div>
				<div
					v-if="updateRouteForm.errors.destination_id"
					class="fv-plugins-message-container invalid-feedback">
					{{ updateRouteForm.errors.destination_id }}
				</div>
			</div>
		</div>

		<div v-if="isFormDirty" class="alert alert-warning mt-10 d-flex">
			<i class="fa-duotone fa-triangle-exclamation text-warning fs-1 me-5"></i>
			<span>{{ $t('general.unsaved_changes') }}</span>
		</div>
		<div v-if="!viewMode" class="d-flex justify-content-end mt-8">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deleteRoute" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('transport.routes.view', singleRoute.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updateRouteForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updateRoute">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</RouteLayout>
</template>

<script>
import RouteLayout from '@/Pages/Transport/Routes/View/Partials/RouteLayout.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';
import LocationInput from '@/Components/Location/LocationInput.vue';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';

export default {
	components: {
		Link,
		RouteLayout,
		MultiSelect,
		LocationInput,
		ConfirmationButton
	},
	props: {
		singleRoute: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updateRouteForm: useForm({
				id: this.singleRoute?.id,
				reference: this.singleRoute?.reference,
				name: this.singleRoute?.name,
				static: this.singleRoute?.static,
				status: this.singleRoute?.status,
				origin_id: this.singleRoute?.origin_id,
				destination_id: this.singleRoute?.destination_id
			}),
			viewMode: false
		};
	},
	computed: {
		isFormDirty() {
			return this.updateRouteForm.isDirty;
		}
	},
	methods: {
		updateRoute() {
			this.updateRouteForm.put(route('transport.routes.update', this.singleRoute.id));
		},
		deleteRoute() {
			router.delete(route('transport.routes.delete', this.singleRoute.id));
		}
	}
};
</script>
