<template>
	<button
		:class="
			station?.id
				? 'btn btn-sm btn-light btn-active-light-info'
				: 'btn w-100 btn-light-primary'
		"
		@click="showModal = true">
		<i v-if="!station?.id" class="fas fa-plus fs-4"></i>
		{{ station?.id ? $t('general.edit') : $t('general.add') }}
	</button>

	<VueFinalModal v-model="showModal">
		<div class="modal fade show d-block" v-if="showModal">
			<div class="modal-dialog modal-dialog-scrollable modal-xl">
				<div class="modal-content">
					<div class="modal-header">
						<h3 class="modal-title text-capitalize">
							{{ $t('transport.station.add') }}
						</h3>
						<div
							@click="closeModal"
							class="btn btn-icon btn-sm btn-active-light-primary ms-2">
							<i class="fa-duotone fa-times fs-1"></i>
						</div>
					</div>
					<div class="modal-body px-0">
						<div class="row min-h-400px">
							<div class="col-md-6 border-end">
								<div class="card card-flush border-0">
									<div class="card-header">
										<div class="card-title text-capitalize">
											<h3>{{ $t('transport.passenger.details') }}</h3>
										</div>
									</div>
									<div class="card-body pt-0">
										<label class="fs-4 fw-semibold text-capitalize mb-2">{{
											$t('transport.passenger.names')
										}}</label>
										<MultiSelect
											class="mb-3"
											:close-on-select="false"
											:multiple="true"
											v-model="selectedPassengers"
											options="Passenger"
											:taggable="false"
											noReduce
											:lazySearch="true"
											:exceptIds="existingPassengerIds">
											<template #selection="{ values, search, isOpen }">
												<span
													class="multiselect__single"
													v-if="values.length"
													v-show="!isOpen">
													{{
														$tChoice(
															'transport.passenger.selected',
															values.length
														)
													}}
												</span>
											</template>
										</MultiSelect>
										<template
											v-if="selectedPassengers.length > 0"
											v-for="passenger in selectedPassengers"
											:key="passenger.id">
											<PassengerItem
												@deletePassenger="removePassenger"
												@locationSelected="selectLocation"
												@update:seat="updatePassengerSeat"
												:passenger="getPassengerWithSeat(passenger)"
												:hasLocation="
													!!selectedPassengersLocations.find(
														(loc) => loc.passengerId === passenger.id
													)
												" />
										</template>
										<EmptyResults
											illustration="secondary"
											:message="
												$t('general.messages.please_select_passengers')
											"
											v-else />
										<div
											v-if="addStationForm.errors.location_id"
											class="fv-plugins-message-container invalid-feedback">
											{{ addStationForm.errors.location_id }}
										</div>
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="card card-flush border-0">
									<div class="card-header">
										<div class="card-title text-capitalize">
											<h3>{{ $t('transport.station.details') }}</h3>
										</div>
									</div>

									<div class="card-body pt-0">
										<div class="d-flex flex-column gap-5 gap-md-7">
											<div class="fv-row fv-plugins-icon-container">
												<label
													class="fs-4 fw-semibold text-capitalize mb-2"
													>{{ $t('general.name') }}</label
												>
												<input
													v-model="addStationForm.name"
													class="form-control"
													name="station_name"
													:placeholder="$t('general.name')" />
												<div
													v-if="addStationForm.errors.name"
													class="fv-plugins-message-container invalid-feedback">
													{{ addStationForm.errors.name }}
												</div>
											</div>
											<div class="fv-row fv-plugins-icon-container">
												<label
													class="required fs-4 fw-semibold text-capitalize mb-2"
													>{{ $t('geo.location.name') }}</label
												>
												<div class="d-flex align-items-center w-100">
													<LocationInput
														v-model="addStationForm.location_id"
														showEditButton />
												</div>

												<div
													v-if="addStationForm.errors.location_id"
													class="fv-plugins-message-container invalid-feedback">
													{{ addStationForm.errors.location_id }}
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="modal-footer">
						<button
							type="button"
							class="btn btn-light text-capitalize"
							@click="closeModal">
							{{ $t('general.cancel') }}
						</button>
						<button
							v-if="!station?.id"
							type="button"
							class="btn btn-primary text-capitalize"
							@click="addStations">
							{{ $t('transport.station.add') }}
						</button>
						<button
							v-else
							type="button"
							class="btn btn-primary text-capitalize"
							@click="updateStations">
							{{ $t('general.save') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</VueFinalModal>
</template>

<script>
import { VueFinalModal } from 'vue-final-modal';
import { useForm, router } from '@inertiajs/vue3';
import MultiSelect from '@/Components/MultiSelect.vue';
import PassengerItem from '@/Pages/Transport/Routes/View/Stations/Partials/PassengerItem.vue';
import LocationInput from '@/Components/Location/LocationInput.vue';
import EmptyResults from '@/Components/EmptyResults.vue';

export default {
	components: {
		VueFinalModal,
		MultiSelect,
		PassengerItem,
		LocationInput,
		EmptyResults
	},
	props: {
		singleRoute: {
			type: Object
		},
		station: {
			type: Object
		},
		locations: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			showModal: false,
			addStationForm: useForm({
				name: this.station?.name,
				location_id: this.station?.location?.id,
				passengers:
					this.station?.passengers?.map((passenger) => ({
						id: passenger.id,
						seat: passenger.pivot?.seat || ''
					})) || []
			}),
			selectedPassengers:
				this.station?.passengers?.map((passenger) => ({
					...passenger,
					seat: passenger.pivot?.seat || ''
				})) || []
		};
	},
	watch: {
		selectedPassengers: {
			handler(newVal, oldVal) {
				const newIds = newVal.map((passenger) => passenger.id);
				const currentIds = this.addStationForm.passengers.map((passenger) => passenger.id);

				newVal.forEach((passenger) => {
					if (!currentIds.includes(passenger.id)) {
						this.addStationForm.passengers.push({
							id: passenger.id,
							seat: passenger.seat || ''
						});
					}
				});

				this.addStationForm.passengers = this.addStationForm.passengers.filter(
					(passenger) => newIds.includes(passenger.id)
				);
			},
			deep: true
		}
	},
	methods: {
		closeModal() {
			this.resetModal();
			this.showModal = false;
		},
		resetModal() {
			this.selectedPassengers = [];
			this.addStationForm.reset();
		},
		addStations() {
			this.addStationForm.post(route('transport.routes.stations.save', this.singleRoute.id), {
				preserveScroll: true,
				onSuccess: () => {
					this.closeModal();
				}
			});
		},
		updateStations() {
			const formData = {
				...this.addStationForm.data(),
				passenger_ids: this.addStationForm.passengers.map((passenger) => passenger.id)
			};

			this.addStationForm
				.transform(() => formData)
				.put(
					route('transport.routes.stations.update', {
						route: this.singleRoute.id,
						station: this.station.id
					}),
					{
						preserveState: false,
						onSuccess: () => {
							this.closeModal();
						}
					}
				);
		},
		removePassenger(passengerId) {
			this.selectedPassengers = this.selectedPassengers.filter(
				(passenger) => passenger.id !== passengerId
			);
		},
		selectLocation(passengerId) {
			const passengerLocationObj = this.selectedPassengersLocations.find(
				(el) => el.passengerId === passengerId
			);
			if (passengerLocationObj?.location) {
				this.addStationForm.location_id = passengerLocationObj.location.id;
			}
		},
		getPassengerWithSeat(passenger) {
			const formPassenger = this.addStationForm.passengers.find(
				(passenger) => passenger.id === passenger.id
			);
			return {
				...passenger,
				seat: formPassenger?.seat || ''
			};
		},
		updatePassengerSeat({ passengerId, seat }) {
			const index = this.addStationForm.passengers.findIndex(
				(passenger) => passenger.id === passengerId
			);
			if (index > -1) {
				this.addStationForm.passengers[index].seat = seat;
			}
		}
	},
	computed: {
		selectedPassengersIds() {
			return this.selectedPassengers.map((passenger) => passenger.id);
		},
		selectedPassengersLocations() {
			return this.locations
				.filter(
					(loc) =>
						this.selectedPassengersIds.includes(loc.model_id) &&
						loc.model_type === 'Passenger'
				)
				.map((loc) => ({
					passengerId: loc.model_id,
					location: loc
				}));
		},
		existingPassengerIds() {
			return this.singleRoute.stations.flatMap((station) =>
				station.passengers?.map((passenger) => passenger.id)
			);
		}
	}
};
</script>
