<template>
	<BaseLayout>
		<Head :title="$t('nav.routes')" />

		<div class="row g-3 h-100">
			<div class="col-md-4 col-lg-3">
				<div class="custom-sticky">
					<div class="card p-4">
						<div class="d-flex flex-center flex-column mb-5">
							<div class="symbol symbol-100px">
								<div class="symbol-label p-3 bg-light-primary">
									<i class="fa-duotone fa-route text-primary fs-4x"></i>
								</div>
							</div>
						</div>

						<div class="d-flex flex-stack fs-4 py-3">
							<div class="fw-bold text-capitalize">
								{{ $t('transport.route.details') }}
							</div>
						</div>

						<div class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								{{ $t('general.name') }}
							</div>
							<div class="text-gray-600">
								{{ singleRoute.name }}
							</div>
						</div>

						<div v-if="singleRoute.static" class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								<div class="badge text-capitalize badge-secondary">
									{{ $t('general.enums.static') }}
								</div>
							</div>
						</div>

						<div class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								{{ $t('transport.route.labels.starting_location') }}
							</div>
							<div class="text-gray-600">
								{{ singleRoute.origin?.name ?? '-' }}
							</div>
						</div>

						<div class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								{{ $t('transport.route.labels.destination') }}
							</div>
							<div class="text-gray-600">
								{{ singleRoute.destination?.name ?? '-' }}
							</div>
						</div>

						<div v-if="singleRoute.distance" class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								{{ $t('transport.route.labels.distance') }}
							</div>
							<div class="text-gray-600">
								{{ formatDistance(singleRoute.distance) }}
							</div>
						</div>

						<div v-if="singleRoute.duration" class="pb-2 fs-6">
							<div class="fw-bold mt-5 text-capitalize">
								{{ $t('transport.route.labels.duration') }}
							</div>
							<div class="text-gray-600">
								{{ formatDuration(singleRoute.duration) }}
							</div>
						</div>
					</div>
					<div
						@click="optimizeRoute"
						class="btn btn-light-success w-100 my-5 border border-2 border-success text-capitalize">
						<i class="fa-duotone fa-solid fa-arrow-up-square-triangle"></i>
						{{ $t('transport.route.optimize') }}
					</div>
				</div>
			</div>

			<div class="col-md-8 col-lg-9">
				<PageNav :single-route="singleRoute" class="mb-3" />
				<div v-if="!fullMode" class="card h-100">
					<div class="card-body">
						<slot />
					</div>
				</div>
				<div v-else class="h-100 w-100 rounded border border-2">
					<slot />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import PageNav from '@/Pages/Transport/Routes/View/Partials/PageNav.vue';
import ImageUploader from '@/Components/ImageUploader.vue';
import { Link, Head, router } from '@inertiajs/vue3';

export default {
	name: 'RouteLayout',
	components: {
		Link,
		ImageUploader,
		BaseLayout,
		PageNav,
		Head
	},
	props: {
		singleRoute: {
			type: Object,
			required: true
		},
		fullMode: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		optimizeRoute() {
			router.put(route('transport.routes.optimize', this.singleRoute?.id));
		},
		formatDistance(distance) {
			if (distance >= 1000) {
				return (distance / 1000).toFixed(2) + ' ' + this.$t('general.units.kilometers');
			}

			return distance + ' ' + this.$t('general.units.meters');
		},
		formatDuration(seconds) {
			if (seconds === 0) {
				return '0 ' + this.$t('general.units.seconds');
			}

			const hours = Math.floor(seconds / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			const remainingSeconds = seconds % 60;

			let duration = '';

			if (hours > 0) {
				duration += hours + ' ' + this.$t('general.units.hours') + ' ';
			}

			if (minutes > 0) {
				duration += minutes + ' ' + this.$t('general.units.minutes') + ' ';
			}

			if (remainingSeconds > 0) {
				duration += remainingSeconds + ' ' + this.$t('general.units.seconds');
			}

			return duration.trim();
		}
	}
};
</script>
