<template>
	<button type="submit" class="btn btn-sm btn-primary text-capitalize" @click="showModal = true">
		{{ $t('transport.responsible.new') }}
	</button>

	<VueFinalModal v-model="showModal">
		<div v-if="showModal" class="modal fade show d-block">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h3 class="modal-title text-capitalize">
							{{ $t('transport.responsible.new') }}
						</h3>
						<div
							@click="showModal = false"
							class="btn btn-icon btn-sm btn-active-light-primary ms-2">
							<i class="fa-duotone fa-times fs-1"></i>
						</div>
					</div>

					<div class="modal-body">
						<div class="card-body">
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.reference') }}
								</label>
								<div class="col-8">
									<input
										v-model="newResponsibleForm.reference"
										class="form-control"
										type="text" />
									<div
										v-if="newResponsibleForm.errors.reference"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.reference }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 required fs-4 fw-semibold text-capitalize">
									{{ $t('general.first_name') }}
								</label>
								<div class="col-8">
									<input
										v-model="newResponsibleForm.first_name"
										class="form-control"
										type="text" />
									<div
										v-if="newResponsibleForm.errors.first_name"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.first_name }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 required fs-4 fw-semibold text-capitalize">
									{{ $t('general.last_name') }}
								</label>
								<div class="col-8">
									<input
										v-model="newResponsibleForm.last_name"
										class="form-control"
										type="text" />
									<div
										v-if="newResponsibleForm.errors.last_name"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.last_name }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 required fs-4 fw-semibold text-capitalize">
									{{ $t('general.phone') }}
								</label>
								<div class="col-8">
									<PhoneInput
										v-model="newResponsibleForm.phone"
										@phone-validated="validatePhone"></PhoneInput>
									<div
										v-if="
											newResponsibleForm.errors.phone || phoneValidationError
										"
										class="text-danger fs-6">
										{{
											newResponsibleForm.errors.phone || phoneValidationError
										}}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.email') }}
								</label>
								<div class="col-8">
									<input
										v-model="newResponsibleForm.email"
										class="form-control"
										type="email" />
									<div
										v-if="newResponsibleForm.errors.email"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.email }}
									</div>
								</div>
							</div>
							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.status') }}
								</label>
								<div class="col-8">
									<MultiSelect
										v-model="newResponsibleForm.status"
										options="UserStatus" />
									<div
										v-if="newResponsibleForm.errors.status"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.status }}
									</div>
								</div>
							</div>

							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.gender') }}
								</label>
								<div class="col-8">
									<MultiSelect
										v-model="newResponsibleForm.gender"
										options="UserGender" />
									<div
										v-if="newResponsibleForm.errors.gender"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.gender }}
									</div>
								</div>
							</div>

							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('general.birthdate') }}
								</label>
								<div class="col-8">
									<DatePicker v-model="newResponsibleForm.birthdate" />

									<div
										v-if="newResponsibleForm.errors.birthdate"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.birthdate }}
									</div>
								</div>
							</div>

							<div class="row my-6 d-flex align-items-center">
								<label class="col-4 fs-4 fw-semibold text-capitalize">
									{{ $t('transport.region.name') }}
								</label>
								<div class="col-8">
									<MultiSelect
										v-model="newResponsibleForm.region_id"
										options="Region" />
									<div
										v-if="newResponsibleForm.errors.region_id"
										class="text-danger fs-6">
										{{ newResponsibleForm.errors.region_id }}
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="modal-footer">
						<button
							type="button"
							class="btn btn-light"
							@click="
								showModal = false;
								newResponsibleForm.reset();
							">
							{{ $t('general.cancel') }}
						</button>
						<button type="button" class="btn btn-primary" @click="saveResponsible">
							{{ $t('general.save') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</VueFinalModal>
</template>

<script>
import { VueFinalModal } from 'vue-final-modal';
import { useForm } from '@inertiajs/vue3';
import PhoneInput from '@/Components/PhoneInput.vue';
import { trans } from 'laravel-vue-i18n';
import MultiSelect from '@/Components/MultiSelect.vue';
import DatePicker from '@/Components/DatePicker.vue';

export default {
	name: 'NewResponsibleModal',
	components: {
		VueFinalModal,
		PhoneInput,
		MultiSelect,
		DatePicker
	},
	data() {
		return {
			showModal: false,
			newResponsibleForm: useForm({
				reference: null,
				first_name: null,
				last_name: null,
				email: null,
				phone: null,
				status: null,
				gender: null,
				birthdate: null,
				region_id: null
			}),
			phoneValidationError: ''
		};
	},
	methods: {
		saveResponsible() {
			this.newResponsibleForm.post(route('transport.responsibles.save'), {
				preserveScroll: true,
				onSuccess: () => {
					this.newResponsibleForm.reset();
					this.showModal = false;
				}
			});
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', { attribute: trans('general.phone') });
		}
	}
};
</script>
