<template>
	<ResponsibleLayout :responsible="responsible">
		<div v-if="!viewMode" class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.image') }}
			</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<ImageInput
					v-model="updateResponsibleForm.image"
					:error="updateResponsibleForm.errors.image"
					:allowedTypesText="
						$t('general.allowed_file_types', { types: 'png, jpg, jpeg' })
					"
					@update:modelValue="updateResponsible" />
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.reference') }}
			</label>

			<div class="col-8">
				<input
					v-model="updateResponsibleForm.reference"
					class="form-control"
					name="fname"
					:placeholder="$t('general.reference')"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateResponsibleForm.errors.reference" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.reference }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.first_name') }}
			</label>

			<div class="col-8">
				<input
					v-model="updateResponsibleForm.first_name"
					class="form-control"
					name="fname"
					placeholder="First name"
					type="text"
					:disabled="viewMode" />
				<div v-if="updateResponsibleForm.errors.first_name" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.first_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.last_name') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateResponsibleForm.last_name"
					class="form-control"
					placeholder="Last name"
					name="lname"
					:disabled="viewMode" />

				<div v-if="updateResponsibleForm.errors.last_name" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.last_name }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.birthdate') }}
			</label>

			<div class="col-8">
				<DatePicker v-model="updateResponsibleForm.birthdate" :disabled="viewMode" />

				<div v-if="updateResponsibleForm.errors.birthdate" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.birthdate }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.email') }}
			</label>

			<div class="col-8">
				<input
					type="text"
					v-model="updateResponsibleForm.email"
					class="form-control"
					:placeholder="$t('general.email')"
					name="email"
					:disabled="viewMode" />

				<div v-if="updateResponsibleForm.errors.email" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.email }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.phone') }}
			</label>

			<div class="col-8">
				<PhoneInput
					v-model="updateResponsibleForm.phone"
					@phone-validated="validatePhone" />

				<div
					v-if="updateResponsibleForm.errors.phone || phoneValidationError"
					class="text-danger fs-6">
					{{ updateResponsibleForm.errors.phone || phoneValidationError }}
				</div>
			</div>
		</div>

		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.status') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateResponsibleForm.status"
					options="UserStatus"
					:disabled="viewMode" />
				<div v-if="updateResponsibleForm.errors.status" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.status }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('general.gender') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateResponsibleForm.gender"
					options="UserGender"
					:disabled="viewMode" />
				<div v-if="updateResponsibleForm.errors.gender" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.gender }}
				</div>
			</div>
		</div>
		<div class="row my-10 d-flex align-items-center">
			<label class="col-4 fs-4 text-muted text-capitalize">
				{{ $t('transport.region.name') }}
			</label>

			<div class="col-8">
				<MultiSelect
					v-model="updateResponsibleForm.region_id"
					options="Region"
					:disabled="viewMode" />
				<div v-if="updateResponsibleForm.errors.region_id" class="text-danger fs-6">
					{{ updateResponsibleForm.errors.region_id }}
				</div>
			</div>
		</div>

		<div v-if="!viewMode" class="row">
			<div class="col d-flex justify-content-start mt-8">
				<ConfirmationButton :action="deleteResponsible" class="btn btn-sm btn-light-danger">
					<i class="fa-duotone fa-trash-alt fs-5"></i>
					{{ $t('general.delete') }}
				</ConfirmationButton>
			</div>
			<div class="col d-flex justify-content-end mt-8">
				<Link
					:href="route('transport.responsibles.view', responsible.id)"
					class="btn btn-sm btn-light mx-1">
					{{ $t('general.cancel') }}
				</Link>
				<button
					:disabled="!updateResponsibleForm.isDirty"
					class="btn btn-sm btn-light-success mx-1"
					@click="updateResponsible">
					{{ $t('general.save_changes') }}
				</button>
			</div>
		</div>
	</ResponsibleLayout>
</template>

<script>
import ResponsibleLayout from '@/Pages/Transport/Responsibles/View/Partials/ResponsibleLayout.vue';
import MultiSelect from '@/Components/MultiSelect.vue';
import { Link, useForm, router } from '@inertiajs/vue3';
import ConfirmationButton from '@/Components/ConfirmationButton.vue';
import DatePicker from '@/Components/DatePicker.vue';
import PhoneInput from '@/Components/PhoneInput.vue';
import { trans } from 'laravel-vue-i18n';
import ImageInput from '@/Components/General/ImageInput.vue';

export default {
	components: {
		Link,
		ResponsibleLayout,
		MultiSelect,
		DatePicker,
		ConfirmationButton,
		PhoneInput,
		ImageInput
	},
	props: {
		responsible: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			updateResponsibleForm: useForm({
				id: this.responsible.id,
				reference: this.responsible.reference,
				first_name: this.responsible.first_name,
				last_name: this.responsible.last_name,
				email: this.responsible.email,
				phone: this.responsible.phone,
				status: this.responsible.status,
				gender: this.responsible.gender,
				birthdate: this.responsible.birthdate,
				region_id: this.responsible.region_id,
				image: this.responsible.image
			}),
			viewMode: false,
			phoneValidationError: ''
		};
	},
	methods: {
		updateResponsible() {
			if (
				this.updateResponsibleForm.image &&
				!(this.updateResponsibleForm.image instanceof File)
			) {
				this.updateResponsibleForm.image = null;
			}
			this.updateResponsibleForm.post(
				route('transport.responsibles.update', this.responsible.id),
				{
					_method: 'put',
					onError: (errors) => {
						if (errors.phone) {
							this.phoneValidationError = errors.phone[0];
						}
					},
					onSuccess: () => {
						this.updateResponsibleForm.image = this.responsible?.image;
					}
				}
			);
		},
		deleteResponsible() {
			router.delete(route('transport.responsibles.update', this.responsible.id));
		},
		validatePhone(isValid) {
			this.phoneValidationError = isValid
				? ''
				: trans('validation.not_regex', { attribute: trans('general.phone') });
		}
	}
};
</script>
