<template>
	<BaseLayout>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<template v-slot:toolbar>
			<ResponsiblesToolbar />
		</template>

		<Head :title="$t('nav.responsibles')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<ResponsiblesFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="responsibles.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<ResponsiblesTable :responsibles="responsibles.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults
								:message="$t('general.messages.no_responsibles')"
								class="colg4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="responsibles.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import ResponsiblesToolbar from '@/Pages/Transport/Responsibles/Partials/ResponsiblesToolbar.vue';
import ResponsiblesTable from '@/Pages/Transport/Responsibles/List/Partials/ResponsiblesTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import ResponsiblesFilters from '@/Pages/Transport/Responsibles/List/Partials/ResponsiblesFilters.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'Main.vue',
	components: {
		BaseLayout,
		Head,
		router,
		ResponsiblesToolbar,
		ResponsiblesTable,
		Pagination,
		EmptyResults,
		ResponsiblesFilters,
		HeaderMenu
	},
	props: {
		responsibles: Object
	},
	data() {
		return {
			menu: [
				{
					hasPermission: true,
					active: route().current('transport.responsibles.*'),
					route: route('transport.responsibles.list'),
					icon: 'fa-duotone fa-hands-holding-child',
					content: trans('transport.responsible.names')
				}
			]
		};
	}
};
</script>
