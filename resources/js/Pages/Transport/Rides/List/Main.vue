<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<RidesToolbar />
		</template>

		<Head :title="$t('nav.rides')" />

		<div class="d-flex flex-fill h-100">
			<div class="row gx-3 w-100">
				<div class="col-5">
					<RidesList v-model="selectedRide" :rides="rides" />
				</div>
				<div class="col-7">
					<MapView :ride="selectedRide" />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head } from '@inertiajs/vue3';
import RidesToolbar from '@/Pages/Transport/Rides/Partials/RidesToolbar.vue';
import RidesList from '@/Pages/Transport/Rides/List/Partials/RidesList.vue';
import MapView from '@/Pages/Transport/Rides/List/Partials/MapView.vue';

export default {
	name: 'Main.vue',
	components: {
		BaseLayout,
		Head,
		RidesToolbar,
		RidesList,
		MapView
	},
	props: {
		rides: Object
	},
	data() {
		return {
			selectedRide: this.rides?.data[0] ?? {}
		};
	}
};
</script>
