<template>
	<div class="card card-flush card-stretch custom-height p-0">
		<div class="card-header">
			<div class="d-flex w-100 py-5">
				<RidesFilters />
			</div>
		</div>
		<div class="card-body p-4 scroll">
			<template
				v-if="rides.data.length > 0"
				v-for="(ride, index) in rides.data"
				:key="`ride-${index}`">
				<div @click="updateSelectedRide(ride)">
					<div
						class="card border-hover-primary cursor-pointer"
						:class="{
							'mb-3': index !== rides?.length - 1,
							'border-dashed border-primary': ride.id === selectedRide?.id
						}">
						<div class="card-body p-3">
							<div class="d-flex justify-content-between align-items-center">
								<div class="text-gray-600 fw-bold fs-5">
									<i class="fa-duotone fa-road text-primary me-1"></i>
									#{{ ride.id }}
								</div>
								<div :class="badgeClass(ride.status)" class="badge text-capitalize">
									{{ $t('general.enums.' + ride.status) }}
								</div>
							</div>
							<div class="d-flex justify-content-start align-items-center mt-1">
								<div class="col">
									<div
										class="d-flex justify-content-start align-items-center mt-1">
										<span class="text-gray-800 fw-bold fs-5 text-capitalize">
											{{ ride.vehicle?.name }}
										</span>
									</div>
									<div
										class="d-flex justify-content-start align-items-center mt-1">
										<span class="text-primary fw-bold fs-6 text-uppercase">
											{{ ride.vehicle?.plate ?? '-' }}
										</span>
									</div>
								</div>
								<div class="col">
									<div
										v-if="ride?.route?.name"
										class="d-flex justify-content-end align-items-center">
										<i class="fa-duotone fa-route text-primary fs-4 me-3"></i>
										<span class="fs-5 fw-semibold">
											{{ ride?.route?.name }}
										</span>
									</div>
								</div>
							</div>
							<div class="d-flex flex-nowrap">
								<div
									class="d-flex flex-wrap justify-content-start align-items-center gap-3">
									<div
										class="text-gray-600 fs-7 fw-semibold text-capitalize mt-2">
										<i class="fa-duotone fa-user-tie me-1"></i>
										{{ ride.driver?.name ?? '-' }}
									</div>
									<div
										class="text-gray-600 fs-7 fw-semibold text-capitalize mt-2">
										<i class="fa-duotone fa-phone me-1"></i>
										{{ ride.driver.phone ?? '-' }}
									</div>
									<div
										class="text-gray-600 fs-7 fw-semibold text-capitalize mt-2">
										<i class="fa-duotone fa-clock me-1"></i>
										{{ formattedTime(ride.started_at) }}
									</div>
								</div>
								<div class="d-flex flex-grow-1 justify-content-end align-items-end">
									<div class="text-gray-600 fs-7 fw-semibold text-capitalize">
										<Link
											:href="route('transport.rides.view', ride?.id)"
											class="btn btn-sm btn-secondary ms-auto py-1">
											{{ $t('general.view') }}
										</Link>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</template>
			<EmptyResults v-else :message="$t('general.messages.no_rides')" />
		</div>
		<div class="card-footer">
			<Pagination :links="rides.links"></Pagination>
		</div>
	</div>
</template>

<script>
import EmptyResults from '@/Components/EmptyResults.vue';
import { Link } from '@inertiajs/vue3';
import RidesFilters from './RidesFilters.vue';
import Pagination from '@/Components/Pagination.vue';

export default {
	name: 'RidesList',
	components: {
		EmptyResults,
		Link,
		Pagination,
		RidesFilters
	},
	props: {
		rides: {
			type: Object,
			required: true
		},
		modelValue: {
			type: Object,
			required: true
		}
	},
	emits: ['update:modelValue'],
	computed: {
		selectedRide() {
			return this.modelValue;
		}
	},
	methods: {
		formattedTime(time) {
			const date = new Date(time);
			const day = date.getDay();
			const hours = date.getHours();
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		},
		updateSelectedRide(ride) {
			this.$emit('update:modelValue', ride);
		},
		badgeClass(status) {
			switch (status) {
				case 'arrived':
					return 'badge-light-success';
				case 'started':
				case 'ongoing':
					return 'badge-light-info';
				case 'canceled':
					return 'badge-light-dark';
			}
		}
	}
};
</script>

<style scoped>
.custom-height {
	height: calc(100vh - 145px);
}
</style>
