<template>
	<tr class="odd">
		<td>
			<div class="d-flex align-items-center">
				<div class="symbol symbol-40px">
					<Link :href="route('transport.rides.view', ride.id)">
						<div class="symbol-label p-3 bg-light-primary">
							<i class="fa-duotone fa-road fs-3 text-primary"></i>
						</div>
					</Link>
				</div>
			</div>
		</td>

		<td>
			<Link
				:href="route('transport.rides.view', ride.id)"
				class="text-gray-800 fs-5 fw-bold text-hover-primary">
				#{{ ride.id }}
			</Link>
		</td>

		<td>
			<span :class="badgeClass" class="badge text-capitalize">
				{{ $t('general.enums.' + ride.status) }}
			</span>
		</td>

		<td v-if="ride.driver">
			<Link
				:class="{
					active: route().current('fleet.drivers.*', ride.driver?.id)
				}"
				:href="route('fleet.drivers.view', ride.driver?.id)"
				class="d-flex align-items-center text-gray-700 text-active-primary m-0">
				<div class="cursor-pointer symbol symbol-30px symbol-md-40px" @click="toggle">
					<img :src="ride.driver?.image" alt="driver" />
				</div>

				<div class="ms-3">
					{{ ride.driver?.name ?? '-' }}
				</div>
			</Link>
		</td>
		<td v-else>
			{{ $t('general.profile_not_found') }}
		</td>

		<td>
			<VehicleCard :vehicle="ride.vehicle" />
		</td>

		<td>
			<div class="text-gray-800">
				{{ ride.started_at }}
				<!-- Assuming 'started_at' is a formatted datetime -->
			</div>
		</td>

		<td>
			<Link
				:href="route('transport.rides.view', ride.id)"
				class="btn text-lg btn-icon btn-bg-light btn-active-color-primary w-30px h-30px">
				<i class="fa-duotone fa-angle-right"></i>
			</Link>
		</td>
	</tr>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import VehicleCard from '@/Components/Cards/VehicleCard.vue';

export default {
	components: {
		Link,
		VehicleCard
	},
	props: {
		ride: Object,
		status: String
	},
	computed: {
		badgeClass() {
			switch (this.ride.status) {
				case 'arrived':
					return 'badge-light-success';
				case 'started':
				case 'ongoing':
					return 'badge-light-info';
				case 'canceled':
					return 'badge-light-dark';
			}
		}
	}
};
</script>
