<template>
	<div class="modal fade show d-block">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header border-0">
					<div class="modal-title text-capitalize">
						<div class="fs-3 text-capitalize fw-semibold">
							<i class="fa-duotone fa-road text-primary me-1 fs-3"></i>
							{{ $t('transport.ride.details') }}
						</div>
					</div>
					<div
						class="btn btn-icon btn-sm btn-active-light-primary ms-2"
						@click="$emit('close')">
						<i class="fa-duotone fa-times fs-1"></i>
					</div>
				</div>

				<div class="modal-body py-0">
					<div class="row">
						<div class="col-6 d-flex">
							<VehicleCard :vehicle="ride.vehicle" :size="70" />
						</div>
						<div class="col-6 d-flex align-items-center justify-content-between">
							<ProfileCard
								:profile="ride?.driver"
								:showStatus="true"
								baseRoute="fleet.drivers.view" />
							<Tooltip :content="ride.driver?.phone ?? '-'">
								<div
									class="bg-success d-flex justify-content-center align-items-center w-20px h-20px rounded-circle cursor-pointer">
									<i class="fa-light fa-phone-volume text-white fs-8"></i>
								</div>
							</Tooltip>
						</div>
					</div>

					<div class="row g-0 mt-10">
						<div class="col-6">
							<div class="row fs-4">
								<label class="col-lg-6 fw-semibold text-muted text-capitalize">
									{{ $t('general.status') }}
								</label>
								<div class="col-lg-6">
									<div class="badge text-capitalize fs-6" :class="badgeClass">
										{{ $t('general.enums.' + ride.status) }}
									</div>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="row fs-4">
								<label class="col-lg-6 fw-semibold text-muted text-capitalize">
									{{ $t('general.mode') }}
								</label>
								<div class="col-lg-6 text-gray-800">
									<span
										class="badge text-capitalize fs-6"
										:class="modeBadgeClass">
										<i
											class="fas me-2"
											:class="
												ride.mode === 'pickup'
													? 'fa-arrow-up text-info'
													: 'fa-arrow-down text-gray-700'
											"></i>
										{{ $t('general.enums.' + ride.mode) }}
									</span>
								</div>
							</div>
						</div>
					</div>

					<div class="row mt-10 g-0">
						<div class="col-6">
							<div class="row fs-4">
								<label class="col-lg-6 fw-semibold text-muted text-capitalize">
									{{ $t('general.started_at') }}
								</label>
								<div class="col-lg-6 fw-bold text-gray-800">
									{{ formattedTime(ride.started_at) }}
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="row fs-4">
								<label class="col-lg-6 fw-semibold text-muted text-capitalize">
									{{ $t('general.arrived_at') }}
								</label>
								<div class="col-lg-6 fw-bold text-gray-800">
									{{ formattedTime(ride.arrived_at) }}
								</div>
							</div>
						</div>
					</div>

					<div class="row fs-4 mt-10">
						<label class="col-lg-4 fw-semibold text-muted text-capitalize">
							{{ $t('transport.route.name') }}
						</label>
						<div class="col-lg-8 fw-bold text-gray-800">
							<Link :href="route('transport.routes.view', ride?.route?.id)">
								{{ ride.route.name }}
							</Link>
						</div>
					</div>

					<div class="row fs-4 mt-10">
						<label class="col-lg-4 fw-semibold text-muted text-capitalize">
							{{ $t('general.note') }}
						</label>
						<div class="col-lg-8 fw-bold text-gray-800">
							{{ ride.note }}
						</div>
					</div>
				</div>
				<div class="modal-footer border-0">
					<Link
						class="btn btn-secondary text-capitalize"
						as="button"
						:href="route('transport.rides.view', ride)">
						{{ $t('general.view') }}
					</Link>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import DatePicker from '@/Components/DatePicker.vue';
import Tabs from '@/Components/Tabs.vue';
import { Link } from '@inertiajs/vue3';
import Datepicker from '@vuepic/vue-datepicker';
import VehicleCard from '@/Components/Cards/VehicleCard.vue';
import ProfileCard from '@/Components/Cards/ProfileCard.vue';
import Tooltip from '@/Components/Tooltip.vue';

export default {
	components: {
		Datepicker,
		Link,
		Tabs,
		DatePicker,
		VehicleCard,
		ProfileCard,
		Tooltip
	},
	props: {
		ride: {
			type: Object,
			required: true
		}
	},
	computed: {
		badgeClass() {
			switch (this.ride.status) {
				case 'arrived':
					return 'badge-light-success';
				case 'started':
				case 'ongoing':
					return 'badge-light-info';
				case 'canceled':
					return 'badge-light-dark';
			}
		},
		modeBadgeClass() {
			switch (this.ride.mode) {
				case 'pickup':
					return 'badge-light-info';
				case 'drop_off':
					return 'badge-secondary';
				default:
					return 'badge-light-success';
			}
		}
	},
	methods: {
		formattedTime(time) {
			const date = new Date(time);
			const day = date.getDay();
			const hours = date.getHours();
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		}
	}
};
</script>
