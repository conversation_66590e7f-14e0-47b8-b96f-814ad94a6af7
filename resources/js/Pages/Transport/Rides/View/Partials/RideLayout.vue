<template>
	<BaseLayout>
		<Head :title="$t('nav.rides')" />

		<div class="row g-3 h-100">
			<div class="col-md-4 col-lg-3">
				<div class="card p-4 custom-sticky">
					<div class="d-flex flex-center flex-column mb-5">
						<div class="symbol symbol-100px">
							<div class="symbol-label p-3 bg-light-primary">
								<i class="fa-duotone fa-road text-primary fs-4x"></i>
							</div>
						</div>
					</div>

					<div class="d-flex flex-stack fs-4 py-3">
						<div class="fw-bold text-capitalize">
							{{ $t('transport.ride.details') }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('transport.rides.labels.ride_id') }}
							<span class="text-gray-800 fs-5 fw-bold"> #{{ ride.id }} </span>
						</div>
					</div>

					<div v-if="ride.route?.id" class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('transport.route.name') }}
						</div>
						<Link
							:href="route('transport.routes.view', ride.route?.id)"
							class="d-flex align-items-center m-0">
							<div class="">
								{{ ride.route?.name }}
							</div>
						</Link>
					</div>

					<div v-if="ride.route?.id" class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('transport.rides.labels.distance') }}
						</div>
						<div class="text-gray-700">
							{{ formatedDistance }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize mb-1">
							{{ $t('fleet.driver.name') }}
						</div>
						<div v-if="ride.driver" class="">
							<Link
								:href="route('fleet.drivers.view', ride.driver?.id)"
								class="d-flex align-items-center text-gray-700 text-active-primary m-0">
								<div
									class="cursor-pointer symbol symbol-30px symbol-md-40px p-1"
									@click="toggle">
									<img :src="ride.driver?.image" alt="driver" />
								</div>

								<div class="ms-3 fs-4 text-gray-600">
									<div class="fw-semibold">
										{{ ride.driver?.name ?? '-' }}
									</div>
									<span :class="badgeClass" class="badge text-capitalize">
										{{ $t(`general.enums.${ride.driver?.status}`) }}
									</span>
								</div>
							</Link>
						</div>
						<div v-else>{{ $t('general.profile_not_found') }}</div>
					</div>

					<div v-if="ride?.assistant" class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize mb-1">
							{{ $t('fleet.assistant.name') }}
						</div>
						<div class="">
							<Link
								:class="{
									active: route().current(
										'fleet.assistant.*',
										ride?.assistant?.id
									)
								}"
								:href="route('fleet.assistants.view', ride?.assistant?.id)"
								class="d-flex align-items-center text-gray-700 text-active-primary m-0">
								<div
									class="cursor-pointer symbol symbol-30px symbol-md-40px p-1"
									@click="toggle">
									<img :src="ride?.assistant?.image" alt="assistant" />
								</div>

								<div class="ms-3 fs-4 text-gray-600">
									<div class="fw-semibold">
										{{ ride?.assistant?.name }}
									</div>
									<span :class="badgeClass" class="badge text-capitalize">
										{{ $t(`general.enums.${ride?.assistant?.status}`) }}
									</span>
								</div>
							</Link>
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize mb-1">
							{{ $t('fleet.vehicle.name') }}
						</div>
						<VehicleCard :vehicle="ride.vehicle" />
					</div>
				</div>
			</div>

			<div class="col-md-8 col-lg-9">
				<PageNav :ride="ride" class="mb-3" />

				<div v-if="!fullMode" class="card h-100">
					<div class="card-body">
						<slot />
					</div>
				</div>
				<div v-else class="h-100 w-100 rounded border border-2">
					<slot />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import PageNav from '@/Pages/Transport/Rides/View/Partials/PageNav.vue';
import { Link, Head } from '@inertiajs/vue3';
import VehicleCard from '@/Components/Cards/VehicleCard.vue';

export default {
	name: 'RideLayout',
	components: {
		Link,
		BaseLayout,
		PageNav,
		VehicleCard,
		Head
	},
	props: {
		ride: {
			type: Object,
			required: true
		},
		fullMode: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		badgeClass() {
			switch (this.ride.driver?.status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
				default:
					return 'badge-light-dark';
			}
		},
		formatedDistance() {
			if (!this.ride?.distance) return '-';
			return (
				`${(this.ride?.distance / 1000).toFixed(1)} ` + this.$t('transport.route.labels.km')
			);
		}
	}
};
</script>
