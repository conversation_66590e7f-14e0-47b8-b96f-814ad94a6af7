<template>
	<div class="chart card card-flush h-100">
		<div class="card-header">
			<div class="card-title">
				<div class="card-label fs-3">
					{{ $t(title) }}
					<Tooltip v-if="description" :content="$t(description)" />
				</div>
			</div>
			<div class="card-toolbar" v-if="showButtons || showSeriesSelector">
				<div class="d-flex gap-2">
					<!-- Series Selector Dropdown -->
					<div
						v-if="showSeriesSelector && seriesNames.length > 1"
						class="dropdown position-relative">
						<button
							class="btn btn-sm btn-outline-secondary dropdown-toggle text-capitalize"
							type="button"
							@click="showDropdown = !showDropdown">
							{{ $t(serieName) }} ({{ visibleSeriesCount }}/{{ seriesNames?.length }})
						</button>
						<ul
							v-if="showDropdown"
							v-click-away="handleClickAway"
							class="dropdown-menu show position-absolute mt-2 top-100 end-0"
							style="z-index: 1000">
							<li v-for="seriesName in seriesNames" :key="seriesName">
								<div
									class="form-check form-check-custom form-check-primary form-check-sm">
									<label
										class="dropdown-item d-flex align-items-center"
										style="cursor: pointer">
										<input
											type="checkbox"
											class="form-check-input me-2"
											:checked="visibleSeries[seriesName]"
											@change="toggleSeries(seriesName)" />
										{{ getSeriesLabel(seriesName) }}
									</label>
								</div>
							</li>
						</ul>
					</div>

					<!-- Group By Buttons -->
					<div v-if="showButtons" class="btn-group btn-group-sm border" role="group">
						<button
							v-for="option in buttonOptions"
							:key="option.value"
							type="button"
							:class="[
								'btn',
								'btn-outline-secondary',
								{ 'active text-active-primary': groupBy === option.value }
							]"
							@click="setGroupBy(option.value)">
							{{ $t(option.label) }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="card-body">
			<div
				v-if="failed"
				class="text-danger h-100 d-flex align-items-center justify-content-center">
				<div @click="load" style="cursor: pointer">
					<div class="my-3 d-flex align-items-center justify-content-center">
						<i class="fa-duotone fs-1 fa-rotate-right text-danger"></i>
					</div>
					<div class="my-3">{{ $t('general.try-again') }}</div>
				</div>
			</div>

			<div v-else-if="loading" class="p-5">
				<div v-for="i in value.length || 15" class="pulse my-2 p-2 w-100"></div>
			</div>

			<div v-else-if="value.length == 0" class="row justify-content-center h-100 w-100">
				<EmptyResults class="col-lg-4 col-sm-6" />
			</div>

			<VChart v-else :option="options" autoresize></VChart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import { TooltipComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import EmptyResults from '@/Components/EmptyResults.vue';
import Tooltip from '@/Components/Tooltip.vue';

use([CanvasRenderer, LineChart, TooltipComponent, DataZoomComponent, GridComponent]);

export default {
	components: {
		VChart,
		EmptyResults,
		Tooltip
	},
	data() {
		return {
			value: [],
			failed: true,
			loading: true,
			groupBy: this.filters?.group_by ?? 'daily',
			visibleSeries: {},
			showDropdown: false,
			buttonOptions: [
				{ label: 'general.options.daily', value: 'daily' },
				{ label: 'general.options.weekly', value: 'weekly' },
				{ label: 'general.options.monthly', value: 'monthly' }
			],
			colors: [
				'#5B2C87',
				'#7C3AAD',
				'#9D47D4',
				'#6366F1',
				'#4F46E5',
				'#4338CA',
				'#3730A3',
				'#312E81',
				'#2563EB',
				'#1D4ED8'
			]
		};
	},
	props: {
		title: {
			type: String,
			required: true
		},
		url: {
			type: String,
			required: true
		},
		filters: {
			type: Object,
			required: false
		},
		symbol: {
			type: String,
			required: true
		},
		color: {
			type: String,
			required: true
		},
		description: {
			type: String,
			required: false
		},
		showButtons: {
			type: Boolean,
			default: true
		},
		showSeriesSelector: {
			type: Boolean,
			default: true
		},
		serieName: {
			type: String,
			required: false
		}
	},
	watch: {
		filters: {
			handler(newValue) {
				if (newValue.group_by !== this.groupBy) {
					this.groupBy = newValue.group_by;
				}
				this.load();
			},
			deep: true,
			immediate: true
		},
		value: {
			handler() {
				this.initializeVisibleSeries();
			},
			immediate: true
		}
	},
	computed: {
		seriesNames() {
			return Object.keys(this.value[0] || {}).filter((key) => key.startsWith('serie'));
		},
		visibleSeriesCount() {
			return Object.values(this.visibleSeries).filter(Boolean).length;
		},
		options() {
			return {
				grid: {
					bottom: '3%',
					containLabel: true,
					left: '3%',
					right: '4%',
					top: '4%'
				},
				tooltip: {
					trigger: 'axis',
					formatter: (params) => {
						const tooltipItems = params
							.map((param) => {
								return `${param.marker} ${param.seriesName} : ${param.value}`;
							})
							.join('<br/>');
						return `${params[0].name}<br/>${tooltipItems}`;
					},
					axisPointer: {
						type: 'line'
					}
				},
				xAxis: {
					type: 'category',
					data: this.value.map((e) => e.name),
					axisLine: {
						show: false
					},
					axisTick: {
						show: false
					},
					dataZoom: [
						{
							type: 'inside'
						}
					],
					z: 10
				},
				yAxis: {
					type: 'value',
					axisLine: {
						show: false
					},
					axisTick: {
						show: false
					}
				},
				series: this.seriesNames
					.filter((seriesName) => this.visibleSeries[seriesName])
					.map((seriesName, index) => ({
						name: this.getSeriesLabel(seriesName),
						type: 'line',
						smooth: true,
						symbol: 'none',
						itemStyle: {
							color: this.colorWithOpacity(
								this.colors[index % this.colors.length],
								0.9
							)
						},
						data: this.value.map((e) => e[seriesName].value.toFixed(2)),
						z: 10
					}))
			};
		}
	},
	methods: {
		shorten(number) {
			const suffixes = ['', 'K', 'M', 'B', 'T'];
			const scale = 1000;
			let index = 0;

			while (number >= scale && index < suffixes.length - 1) {
				number /= scale;
				index++;
			}

			return number.toFixed(1) + suffixes[index];
		},
		load() {
			this.loading = true;
			this.failed = false;
			const updatedFilters = { ...this.filters, group_by: this.groupBy };

			window.axios
				.get(this.url, {
					params: {
						filters: updatedFilters
					}
				})
				.then(({ data }) => (this.value = data.report))
				.catch(() => (this.failed = true))
				.finally(() => (this.loading = false));
		},
		colorWithOpacity(color, opacity) {
			const rgba = `rgba(${parseInt(color.slice(1, 3), 16)}, ${parseInt(
				color.slice(3, 5),
				16
			)}, ${parseInt(color.slice(5, 7), 16)}, ${opacity})`;
			return rgba;
		},
		setGroupBy(groupBy) {
			this.groupBy = groupBy;
			this.load();
		},
		initializeVisibleSeries() {
			if (this.value.length > 0) {
				const newVisibleSeries = {};
				this.seriesNames.forEach((seriesName) => {
					if (!(seriesName in this.visibleSeries)) {
						newVisibleSeries[seriesName] = true;
					} else {
						newVisibleSeries[seriesName] = this.visibleSeries[seriesName];
					}
				});
				this.visibleSeries = newVisibleSeries;
			}
		},
		toggleSeries(seriesName) {
			this.visibleSeries = {
				...this.visibleSeries,
				[seriesName]: !this.visibleSeries[seriesName]
			};
		},
		getSeriesLabel(seriesName) {
			return this.value.length > 0 ? this.value[0][seriesName].label : seriesName;
		},
		handleClickOutside(event) {
			if (!this.$el.contains(event.target)) {
				this.showDropdown = false;
			}
		},
		handleClickAway() {
			this.showDropdown = false;
		}
	},
	mounted() {
		this.load();
		document.addEventListener('click', this.handleClickOutside);
	},
	beforeUnmount() {
		document.removeEventListener('click', this.handleClickOutside);
	}
};
</script>

<style scoped>
.chart {
	height: 20rem;
}

@keyframes pulse {
	60% {
		opacity: 0.5;
	}
}

.pulse {
	animation: pulse 1.5s infinite alternate;
	border-radius: 1.5rem;
	background-color: #e5e5e5aa !important;
}

.pulse.no-animation {
	animation: none;
}

.dropdown-menu {
	max-height: 300px;
	overflow-y: auto;
}
</style>
