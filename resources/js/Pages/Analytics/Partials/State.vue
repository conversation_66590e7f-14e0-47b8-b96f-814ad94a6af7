<template>
	<div class="card card-flush card-stretch" :class="{ '': animate }">
		<div
			v-if="failed"
			class="card-body text-danger d-flex align-items-center justify-content-center">
			<div @click="load" style="cursor: pointer">
				<div class="my-3 d-flex align-items-center justify-content-center">
					<i class="fa-duotone fs-1 fa-rotate-right text-danger"></i>
				</div>
				<div class="my-3">{{ $t('general.try-again') }}</div>
			</div>
		</div>

		<div v-else-if="loading" class="card-body">
			<div class="pulse p-2 no-animation" style="width: 2rem; height: 2rem"></div>
			<div class="d-flex align-items-center my-3">
				<div class="pulse p-3 w-50"></div>
			</div>
			<div style="animation-delay: 300ms" class="pulse p-2 w-75"></div>
		</div>

		<div v-else class="card-body">
			<template v-if="withChart">
				<div class="d-flex">
					<i :class="[icon]" class="fa-duotone text-primary fs-2 me-2"></i>
					<div class="fw-semibold text-gray-600 fs-5">{{ $t(title) }}</div>
				</div>
				<div class="row g-1 h-100">
					<div class="col w-100">
						<div class="d-flex flex-wrap align-items-center my-auto mt-5">
							<div
								v-for="(item, index) in value"
								:key="item.label"
								class="d-flex flex-column me-10 mb-3">
								<div class="d-flex align-items-center">
									<span
										:style="{ color: colors[index] }"
										class="fas fa-circle fs-8"></span>
									<span class="ms-2">{{ item.label }}</span>
								</div>
								<div class="text-gray-800 fw-semibold fs-6">
									{{ item.value }}
								</div>
							</div>
						</div>
					</div>
					<div class="col mw-100px">
						<div class="w-100 h-100px mx-auto mb-3">
							<VChart :option="chartOptions" autoresize></VChart>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="d-flex">
					<div class="fw-semibold text-gray-600">{{ $t(title) }}</div>
					<i :class="[icon]" class="fa-duotone text-primary fs-1 ms-auto"></i>
				</div>
				<div class="d-flex align-items-center my-3">
					<div class="me-auto">
						<div class="text-gray-900 fw-bold fs-1">
							{{ formattedValue }}
							<span class="fs-4 fw-semibold text-gray-600">{{ $t(symbol) }}</span>
						</div>
					</div>
					<div v-if="percentageChange !== null">
						<span class="badge text-gray-800" :class="percentageChangeClass">
							<i :class="percentageChangeIcon"></i>
							{{ percentageChangeLabel }}
						</span>
					</div>
				</div>
			</template>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import { TooltipComponent, GraphicComponent } from 'echarts/components';

use([CanvasRenderer, PieChart, TooltipComponent, GraphicComponent]);

export default {
	components: {
		VChart
	},
	props: {
		title: {
			type: String,
			required: true
		},
		symbol: {
			type: String,
			required: true
		},
		url: {
			type: String,
			required: true
		},
		filters: {
			type: Object,
			required: false
		},
		icon: {
			type: String,
			required: true
		},
		withChart: {
			type: Boolean,
			required: true,
			default: false
		},
		animate: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			value: [],
			loading: false,
			failed: false,
			colors: [
				'#6210CC',
				'#130326',
				'#DDC6FA',
				'#a1d0fc',
				'#4dc9ff',
				'#b3e5fc',
				'#ff8a80',
				'#ff5252',
				'#ff1744',
				'#d50000'
			]
		};
	},
	computed: {
		percentageChange() {
			return this.value?.percentage_change !== undefined
				? this.value.percentage_change
				: null;
		},
		percentageChangeLabel() {
			if (this.percentageChange !== null) {
				const isWholeNumber = Number.isInteger(this.percentageChange);
				const formattedChange = isWholeNumber
					? this.percentageChange.toFixed(0)
					: this.percentageChange.toFixed(2);
				return `${formattedChange}%`;
			}
			return '';
		},
		percentageChangeClass() {
			return this.percentageChange > 0
				? 'badge-light-success'
				: this.percentageChange < 0
					? 'badge-light-danger'
					: 'badge-secondary';
		},
		percentageChangeIcon() {
			return this.percentageChange > 0
				? 'fa-duotone fa-arrow-trend-up text-success me-2'
				: this.percentageChange < 0
					? 'fa-duotone fa-arrow-trend-down text-danger me-2'
					: '';
		},
		formattedValue() {
			return this.isArray ? this.shorten(this.totalValue) : (this.value?.value ?? this.value);
		},
		totalValue() {
			return this.isArray ? this.value.reduce((acc, item) => acc + item.value, 0) : 0;
		},
		isArray() {
			return Array.isArray(this.value) && !this.percentageChange;
		},
		chartOptions() {
			if (this.isArray && this.withChart) {
				return {
					tooltip: {
						trigger: 'item',
						formatter: `{b} : {c} ${this.symbol} ({d}%)`
					},
					color: this.colors,
					series: [
						{
							name: 'Status',
							type: 'pie',
							radius: ['80%', '90%'],
							itemStyle: {
								borderRadius: 5,
								borderWidth: 2,
								borderColor: '#fff'
							},
							label: { show: false },
							data: this.value.map((item) => ({
								name: item.label,
								value: item.value
							}))
						}
					],
					graphic: {
						elements: [
							{
								type: 'text',
								left: 'center',
								top: 'center',
								style: {
									text: `${this.totalValue}${
										this.symbol ? ' ' + this.symbol : ''
									}`,
									textAlign: 'center',
									fill: '#333',
									fontSize: 20
								}
							}
						]
					}
				};
			}
			return {};
		}
	},
	watch: {
		filters: {
			handler() {
				this.load();
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		shorten(number) {
			const suffixes = ['', 'K', 'M', 'B', 'T'];
			const scale = 1000;
			let index = 0;
			while (number >= scale && index < suffixes.length - 1) {
				number /= scale;
				index++;
			}
			return number.toFixed(1).replace(/\.0$/, '') + suffixes[index];
		},
		load() {
			this.loading = true;
			this.failed = false;
			window.axios
				.get(this.url, { params: { filters: this.filters } })
				.then(({ data }) => {
					this.value = data.report || [];
					this.failed = false;
				})
				.catch(() => {
					this.failed = true;
				})
				.finally(() => {
					this.loading = false;
				});
		}
	},
	mounted() {
		this.load();
	}
};
</script>

<style scoped>
@keyframes pulse {
	0% {
		transform: scaleX(0.85);
		opacity: 0.9;
	}

	60% {
		opacity: 0.8;
	}
}

.pulse {
	animation: pulse 1.5s infinite alternate;
	border-radius: 1rem;
	transform-origin: left;
	background-color: #e5e5e5aa !important;
}

.pulse.no-animation {
	animation: none;
}
</style>
