<template>
	<div class="chart card card-flush">
		<div class="card-header">
			<div class="card-title">
				<div class="card-label fs-3">
					{{ $t(title) }}
					<Tooltip v-if="description" :content="$t(description)" />
				</div>
			</div>
			<div class="card-toolbar"></div>
		</div>

		<div class="card-body pt-0">
			<div
				v-if="failed"
				class="text-danger h-100 d-flex align-items-center justify-content-center">
				<div @click="load" style="cursor: pointer">
					<div class="my-3 d-flex align-items-center justify-content-center">
						<i class="fa-duotone fs-1 fa-rotate-right text-danger"></i>
					</div>
					<div class="my-3">
						{{ $t('general.try-again') }}
					</div>
				</div>
			</div>

			<div v-else-if="emptyData" class="row justify-content-center h-100 w-100">
				<EmptyResults class="col-lg-4 col-sm-6" />
			</div>

			<VChart v-else :option="options" autoresize></VChart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import { TooltipComponent } from 'echarts/components';
import EmptyResults from '@/Components/EmptyResults.vue';
import Tooltip from '@/Components/Tooltip.vue';

use([CanvasRenderer, PieChart, TooltipComponent]);

export default {
	components: {
		VChart,
		EmptyResults,
		Tooltip
	},
	data() {
		return {
			value: [],
			failed: true,
			emptyData: false,
			colors: [
				'#5B2C87',
				'#7C3AAD',
				'#9D47D4',
				'#6366F1',
				'#4F46E5',
				'#4338CA',
				'#3730A3',
				'#312E81',
				'#2563EB',
				'#1D4ED8'
			]
		};
	},
	props: {
		title: {
			type: String,
			required: true
		},
		url: {
			type: String,
			required: true
		},
		description: {
			type: String,
			required: false
		},
		filters: {
			type: Object,
			required: false
		},
		symbol: {
			type: String,
			required: true
		},
		doughnut: {
			type: Boolean
		}
	},
	computed: {
		options() {
			return {
				tooltip: {
					trigger: 'item',
					formatter: `{b} : {c} ${this.symbol} ({d}%)`
				},
				color: this.colors,
				series: [
					{
						name: 'Area Mode',
						type: 'pie',
						radius: this.doughnut ? ['50%', '70%'] : '70%',
						itemStyle: {
							borderRadius: 5,
							borderWidth: 1,
							borderColor: '#fff'
						},
						data: this.value.map((e) => ({
							name: e.name,
							value: e.value
						}))
					}
				]
			};
		}
	},
	watch: {
		filters(newValue) {
			this.value = [];
			this.emptyData = false;
			this.load();
		}
	},
	methods: {
		load() {
			this.failed = false;
			window.axios
				.get(this.url, {
					params: {
						filters: this.filters
					}
				})
				.then(({ data }) => {
					this.value = data.report;
					this.emptyData = data.report.length == 0;
				})
				.catch(() => (this.failed = true));
		},
		shuffleArray(array) {
			const shuffledArray = array.slice();
			for (let i = shuffledArray.length - 1; i > 0; i--) {
				const j = Math.floor(Math.random() * (i + 1));
				[shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]];
			}
			return shuffledArray;
		}
	},
	mounted() {
		this.load();
	}
};
</script>
<style scoped>
.chart {
	height: 30rem;
}
</style>
