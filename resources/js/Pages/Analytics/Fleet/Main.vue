<template>
	<BaseLayout>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<Head :title="$t('nav.analytics')" />

		<template v-slot:toolbar>
			<AnalyticsToolbar type="fleet" @update-filters="updateFilters" />
		</template>

		<div class="row g-5">
			<div
				v-for="(chart, index) in statesWithoutCharts"
				:key="index"
				class="col-sm-6 col-md-3">
				<State
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:icon="chart.icon"
					:withChart="chart.withChart"
					:description="chart.description" />
			</div>
		</div>

		<div class="row g-5 mb-5">
			<template v-for="(chunk, chunkIndex) in chunkedStates" :key="'chunk-' + chunkIndex">
				<div
					v-for="(chart, index) in chunk"
					:key="index"
					:class="[
						'col-sm-6',
						chunk.length < 3 && 'col-md-6',
						chunk.length === 3 && 'col-md-4'
					]">
					<State
						:title="chart.title"
						:symbol="chart.symbol"
						:url="chart.url"
						:filters="filters"
						:icon="chart.icon"
						:withChart="chart.withChart"
						:description="chart.description" />
				</div>
			</template>
		</div>

		<div class="row g-5 mb-5">
			<div v-for="(chart, index) in combinedCharts" :key="index" class="col-md-6">
				<component
					:is="chart.type === 'pie' ? 'Pie' : 'Bar'"
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:doughnut="chart.doughnut"
					:color="chart.color"
					:description="chart.description"
					:orientation="chart.orientation" />
			</div>
			<div v-for="(chart, index) in lineCharts" :key="index" class="col-sm-6">
				<Line
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:color="chart.color"
					:description="chart.description"
					:showButtons="chart.showButtons"
					:serieName="chart.serieName"
					:orientation="chart.orientation" />
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import State from '@/Pages/Analytics/Partials/State.vue';
import Bar from '@/Pages/Analytics/Partials/Bar.vue';
import Table from '@/Pages/Analytics/Partials/Table.vue';
import Pie from '@/Pages/Analytics/Partials/Pie.vue';
import Line from '@/Pages/Analytics/Partials/Line.vue';
import AnalyticsToolbar from '@/Pages/Analytics/Partials/AnalyticsToolbar.vue';

export default {
	components: {
		BaseLayout,
		HeaderMenu,
		Head,
		Link,
		State,
		Bar,
		Table,
		Pie,
		Line,
		AnalyticsToolbar
	},
	data() {
		return {
			filters: {
				from: undefined,
				to: undefined,
				group_by: 'daily'
			},
			charts: [
				{
					title: this.$t('analytics.charts.total_distance_traveled.title'),
					type: 'state',
					url: route('analytics.report.fleet', {
						key: 'total-distance-traveled'
					}),
					symbol: 'transport.route.labels.km',
					icon: 'fa-infinity',
					withChart: false,
					description: this.$t('analytics.charts.total_distance_traveled.description')
				},
				{
					title: this.$t('analytics.charts.total_driver_count.title'),
					type: 'state',
					url: route('analytics.report.fleet', {
						key: 'total-driver-count'
					}),
					symbol: '',
					icon: 'fa-shuttle-van',
					withChart: false,
					description: this.$t('analytics.charts.total_driver_count.description')
				},
				{
					title: this.$t('analytics.charts.total_assistant_count.title'),
					type: 'state',
					url: route('analytics.report.fleet', {
						key: 'total-assistant-count'
					}),
					symbol: '',
					icon: 'fa-handshake-angle',
					withChart: false,
					description: this.$t('analytics.charts.total_assistant_count.description')
				},
				{
					title: this.$t('analytics.charts.total_vehicle_count.title'),
					type: 'state',
					url: route('analytics.report.fleet', {
						key: 'total-vehicle-count'
					}),
					symbol: '',
					icon: 'fa-user-tie',
					withChart: false,
					description: this.$t('analytics.charts.total_vehicle_count.description')
				},
				{
					title: 'analytics.charts.average_driver_speed.title',
					type: 'line',
					url: route('analytics.report.fleet', {
						key: 'average-driver-speed'
					}),
					symbol: 'km/h',
					color: '#5B2C87',
					description: 'analytics.charts.average_driver_speed.description',
					showButtons: true,
					serieName: 'fleet.driver.names'
				},
				{
					title: this.$t('analytics.charts.average_vehicle_usage.title'),
					type: 'bar',
					url: route('analytics.report.fleet', {
						key: 'average-vehicle-usage'
					}),
					symbol: '%',
					color: '#5B2C87',
					description: this.$t('analytics.charts.average_vehicle_usage.description'),
					orientation: 'horizontal'
				},
				{
					title: this.$t('analytics.charts.ride_counts_by_vehicle.title'),
					type: 'pie',
					url: route('analytics.report.fleet', {
						key: 'ride-counts-by-vehicle'
					}),
					symbol: '',
					doughnut: true,
					description: this.$t('analytics.charts.ride_counts_by_vehicle.description')
				},
				{
					title: this.$t('analytics.charts.ride_counts_by_driver.title'),
					type: 'pie',
					url: route('analytics.report.fleet', {
						key: 'ride-counts-by-driver'
					}),
					symbol: '',
					doughnut: false,
					description: this.$t('analytics.charts.ride_counts_by_driver.description')
				}
			],
			menu: [
				{
					hasPermission: true,
					active: route().current('analytics.view'),
					route: route('analytics.view'),
					icon: 'fa-duotone fa-route',
					content: this.$t('analytics.menu.transport_analytics')
				},
				{
					hasPermission: true,
					active: route().current('analytics.fleet.view'),
					route: route('analytics.fleet.view'),
					icon: 'fa-truck',
					content: this.$t('analytics.menu.fleet_analytics')
				}
			]
		};
	},
	computed: {
		statesWithCharts() {
			return this.charts.filter((e) => e.type === 'state' && e.withChart);
		},
		statesWithoutCharts() {
			return this.charts.filter((e) => e.type === 'state' && !e.withChart);
		},
		combinedCharts() {
			const pieCharts = this.charts.filter((e) => e.type === 'pie');
			const barCharts = this.charts.filter((e) => e.type === 'bar');
			const combined = [];

			const maxLength = Math.max(pieCharts.length, barCharts.length);
			for (let i = 0; i < maxLength; i++) {
				if (i % 2 === 0) {
					if (i < barCharts.length) combined.push(barCharts[i]);
					if (i < pieCharts.length) combined.push(pieCharts[i]);
				} else {
					if (i < pieCharts.length) combined.push(pieCharts[i]);
					if (i < barCharts.length) combined.push(barCharts[i]);
				}
			}

			return combined;
		},
		lineCharts() {
			return this.charts.filter((chart) => chart.type === 'line');
		},
		chunkedStates() {
			const chunkSize = 3;
			const chunks = [];
			for (let i = 0; i < this.statesWithCharts.length; i += chunkSize) {
				chunks.push(this.statesWithCharts.slice(i, i + chunkSize));
			}
			return chunks;
		}
	},
	methods: {
		updateFilters(filters) {
			this.filters = filters;
		}
	}
};
</script>
