<template>
	<BaseLayout>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<Head :title="$t('nav.analytics')" />

		<template v-slot:toolbar>
			<AnalyticsToolbar type="transport" @update-filters="updateFilters" />
		</template>

		<template v-if="statesWithoutCharts?.length">
			<div class="row g-5">
				<div
					v-for="(chart, index) in statesWithoutCharts"
					:key="index"
					class="col-sm-6 col-md-3">
					<State
						:title="chart.title"
						:symbol="chart.symbol"
						:url="chart.url"
						:filters="filters"
						:icon="chart.icon"
						:color="chart.icon"
						:withChart="chart.withChart"
						:description="chart.description" />
				</div>
			</div>
		</template>

		<template v-if="statesWithCharts?.length">
			<div class="row g-5">
				<div
					v-for="(chart, index) in statesWithCharts"
					:key="index"
					class="col-sm-6 col-md-4">
					<State
						:title="chart.title"
						:symbol="chart.symbol"
						:url="chart.url"
						:filters="filters"
						:icon="chart.icon"
						:withChart="chart.withChart"
						:description="chart.description" />
				</div>
			</div>
		</template>

		<div class="row g-5 mb-5">
			<div v-for="(chart, index) in lineCharts" :key="index" class="col-sm-6">
				<Line
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:color="chart.color"
					:description="chart.description"
					:showButtons="chart.showButtons"
					:serieName="chart.serieName"
					:orientation="chart.orientation" />
			</div>
			<div v-for="(chart, index) in radarCharts" :key="index" class="col-sm-6">
				<Radar
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:description="chart.description"
					:showDataSelector="chart.showButtons"
					:serieName="chart.serieName" />
			</div>
		</div>

		<div class="row g-5">
			<div v-for="(chart, index) in maps" :key="index" class="col-sm-6">
				<AssetsMap :title="chart.title" :url="chart.url" :filters="filters" />
			</div>
			<div v-for="(chart, index) in combinedCharts" :key="index" class="col-md-6">
				<component
					:is="chart.type === 'pie' ? 'Pie' : 'Bar'"
					:title="chart.title"
					:symbol="chart.symbol"
					:url="chart.url"
					:filters="filters"
					:doughnut="chart.doughnut"
					:color="chart.color"
					:description="chart.description"
					:orientation="chart.orientation" />
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import State from '@/Pages/Analytics/Partials/State.vue';
import Bar from '@/Pages/Analytics/Partials/Bar.vue';
import Line from '@/Pages/Analytics/Partials/Line.vue';
import Table from '@/Pages/Analytics/Partials/Table.vue';
import Pie from '@/Pages/Analytics/Partials/Pie.vue';
import Radar from '@/Pages/Analytics/Partials/Radar.vue';
import AssetsMap from '@/Pages/Analytics/Partials/AssetsMap.vue';
import AnalyticsToolbar from '@/Pages/Analytics/Partials/AnalyticsToolbar.vue';

export default {
	components: {
		BaseLayout,
		HeaderMenu,
		Head,
		Link,
		State,
		Bar,
		Line,
		Table,
		Pie,
		Radar,
		AssetsMap,
		AnalyticsToolbar
	},
	data() {
		return {
			filters: {
				from: undefined,
				to: undefined,
				group_by: 'daily'
			},
			charts: [
				{
					title: 'analytics.charts.total_number_of_rides.title',
					type: 'state',
					url: route('analytics.report.transport', {
						key: 'total-number-of-rides'
					}),
					symbol: '',
					icon: 'fa-road',
					withChart: false,
					description: 'analytics.charts.total_number_of_rides.description'
				},
				{
					title: 'analytics.charts.ride_duration.title',
					type: 'line',
					url: route('analytics.report.transport', {
						key: 'ride-duration-metrics'
					}),
					symbol: 'min',
					color: '#5B2C87',
					description: 'analytics.charts.ride_duration.description',
					showButtons: true,
					serieName: 'transport.route.names'
				},
				{
					title: 'analytics.charts.ride_distance.title',
					type: 'bar',
					url: route('analytics.report.transport', {
						key: 'ride-distance-metrics'
					}),
					symbol: 'km',
					color: '#5B2C87',
					description: 'analytics.charts.ride_distance.description',
					orientation: 'vertical'
				},
				{
					title: 'analytics.charts.total_number_of_routes.title',
					type: 'state',
					url: route('analytics.report.transport', {
						key: 'total-number-of-routes'
					}),
					symbol: '',
					icon: 'fa-route',
					withChart: false,
					description: 'analytics.charts.total_number_of_routes.description'
				},
				{
					title: 'analytics.charts.ridership_per_route.title',
					type: 'pie',
					url: route('analytics.report.transport', {
						key: 'ridership-per-route'
					}),
					symbol: '',
					doughnut: false,
					description: 'analytics.charts.ridership_per_route.description'
				},
				{
					title: 'analytics.charts.ride_status_count.title',
					type: 'radar',
					url: route('analytics.report.transport', {
						key: 'ride-status-count'
					}),
					symbol: '',
					description: 'analytics.charts.ride_status_count.description',
					serieName: 'transport.route.names'
				},
				{
					title: 'analytics.charts.total_number_of_stations.title',
					type: 'state',
					url: route('analytics.report.transport', {
						key: 'total-number-of-stations'
					}),
					symbol: '',
					icon: 'fa-map-marker-alt',
					withChart: false,
					description: 'analytics.charts.total_number_of_stations.description'
				},
				{
					title: 'analytics.charts.total_number_of_passengers.title',
					type: 'state',
					url: route('analytics.report.transport', {
						key: 'total-number-of-passengers'
					}),
					symbol: '',
					icon: 'fa-person-seat',
					withChart: false,
					description: 'analytics.charts.total_number_of_passengers.description'
				}
			],
			menu: [
				{
					hasPermission: true,
					active: route().current('analytics.view'),
					route: route('analytics.view'),
					icon: 'fa-duotone fa-route',
					content: 'analytics.menu.transport_analytics'
				},
				{
					hasPermission: true,
					active: route().current('analytics.fleet.view'),
					route: route('analytics.fleet.view'),
					icon: 'fa-truck',
					content: 'analytics.menu.fleet_analytics'
				}
			]
		};
	},
	computed: {
		statesWithCharts() {
			return this.charts.filter((e) => e.type === 'state' && e.withChart);
		},
		statesWithoutCharts() {
			return this.charts.filter((e) => e.type === 'state' && !e.withChart);
		},
		lineCharts() {
			return this.charts.filter((e) => e.type === 'line');
		},
		radarCharts() {
			return this.charts.filter((e) => e.type === 'radar');
		},
		maps() {
			return this.charts.filter((e) => e.type === 'map');
		},
		combinedCharts() {
			const pieCharts = this.charts.filter((e) => e.type === 'pie');
			const barCharts = this.charts.filter((e) => e.type === 'bar');
			const combined = [];

			const maxLength = Math.max(pieCharts.length, barCharts.length);
			for (let i = 0; i < maxLength; i++) {
				if (i % 2 === 0) {
					if (i < barCharts.length) combined.push(barCharts[i]);
					if (i < pieCharts.length) combined.push(pieCharts[i]);
				} else {
					if (i < pieCharts.length) combined.push(pieCharts[i]);
					if (i < barCharts.length) combined.push(barCharts[i]);
				}
			}

			return combined;
		}
	},
	methods: {
		updateFilters(filters) {
			this.filters = filters;
		}
	}
};
</script>
