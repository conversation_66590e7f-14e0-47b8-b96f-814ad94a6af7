<template>
	<div class="card border-0 p-0">
		<div class="card-body px-0 py-2">
			<div class="row g-3">
				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-circle-info fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('general.status') }}
							</div>
							<div class="badge text-capitalize" :class="badgeClass">
								{{ $t('general.enums.' + ride.status) }}
							</div>
						</div>
					</div>
				</div>

				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-arrows-up-down fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('general.mode') }}
							</div>
							<div class="badge text-capitalize" :class="modeBadgeClass">
								<i class="fas me-1" :class="modeIconClass"></i>
								{{ $t('general.enums.' + ride.mode) }}
							</div>
						</div>
					</div>
				</div>

				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-route fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('transport.rides.labels.distance') }}
							</div>
							<div class="fw-semibold">{{ formatedDistance }}</div>
						</div>
					</div>
				</div>

				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-clock fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('transport.rides.labels.duration') }}
							</div>
							<div class="fw-semibold">
								{{ ride.duration }} {{ $t('transport.rides.labels.minutes') }}
							</div>
						</div>
					</div>
				</div>

				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-hourglass-start fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('transport.rides.labels.start_time') }}
							</div>
							<div class="fw-semibold">{{ formattedTime }}</div>
						</div>
					</div>
				</div>

				<div class="col-6">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-gauge-high fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('transport.rides.labels.average_speed') }}
							</div>
							<div class="fw-semibold">
								{{ ride.average_speed }} {{ $t('transport.rides.labels.kmh') }}
							</div>
						</div>
					</div>
				</div>

				<div class="col-12">
					<div class="d-flex align-items-start">
						<i class="fa-duotone fa-note-sticky fa-fw fs-1 me-3 text-primary"></i>
						<div class="w-100">
							<div class="text-muted small text-capitalize">
								{{ $t('general.note') }}
							</div>
							<div class="fw-semibold">{{ ride.note }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'OverviewTab',
	props: {
		ride: {
			type: Object,
			required: true
		}
	},
	computed: {
		badgeClass() {
			const classes = {
				arrived: 'badge-light-success',
				started: 'badge-light-info',
				ongoing: 'badge-light-info',
				canceled: 'badge-light-dark'
			};
			return classes[this.ride.status] || 'badge-light-secondary';
		},
		modeBadgeClass() {
			const classes = {
				pickup: 'badge-light-info',
				drop_off: 'badge-secondary'
			};
			return classes[this.ride.mode] || 'badge-light-success';
		},
		modeIconClass() {
			return this.ride.mode === 'pickup'
				? 'fa-arrow-up text-info'
				: 'fa-arrow-down text-gray-700';
		},
		formattedTime() {
			const date = new Date(this.ride.started_at);
			return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
		},
		formatedDistance() {
			if (!this.ride?.distance) return '-';
			return (
				`${(this.ride?.distance / 1000).toFixed(1)} ` + trans('transport.route.labels.km')
			);
		}
	}
};
</script>
