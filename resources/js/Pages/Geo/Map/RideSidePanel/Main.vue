<template>
	<div class="d-flex flex-column gap-2 p-4 side-bar">
		<div class="card shadow-sm">
			<div class="card-body">
				<DriverSection :driver="ride?.driver" v-on:close="$emit('close')" />
			</div>
		</div>
		<VehicleSection :vehicle="ride.vehicle" />
		<div class="card shadow-sm">
			<div class="card-body">
				<TabSection :ride="ride" />
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import DriverSection from '@/Pages/Geo/Map/RideSidePanel/Partials/DriverSection.vue';
import VehicleSection from '@/Pages/Geo/Map/RideSidePanel/Partials/VehicleSection.vue';
import TabSection from '@/Pages/Geo/Map/RideSidePanel/Partials/TabSection.vue';

export default {
	components: {
		Link,
		DriverSection,
		VehicleSection,
		TabSection
	},
	props: {
		ride: {
			type: Object
		}
	},
	emits: ['close'],
	methods: {
		closeSidePanel() {
			this.$emit('close');
		}
	}
};
</script>
<!-- 
<style scoped>
.side-bar::-webkit-scrollbar {
	display: none;
}

.side-bar {
	-ms-overflow-style: none;
	scrollbar-width: none;
}
</style> -->
