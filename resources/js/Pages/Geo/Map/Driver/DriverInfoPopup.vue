<template>
	<div id="info-window-card" class="card p-2 pb-0 min-w-250px">
		<div class="card-body p-1 pb-0">
			<div class="d-flex flex-wrap flex-sm">
				<div class="flex-grow-1">
					<div class="d-flex justify-content-between align-items-center flex-wrap">
						<div class="d-flex flex-column">
							<ProfileCard
								:profile="driver"
								:showStatus="true"
								baseRoute="fleet.drivers.view" />
						</div>
						<div class="d-flex flex-column ms-3">
							<Tooltip :content="driver?.phone">
								<div
									class="bg-success d-flex justify-content-center align-items-center w-20px h-20px rounded-circle cursor-pointer">
									<i class="fa-light fa-phone-volume text-white fs-8"></i>
								</div>
							</Tooltip>
						</div>
					</div>
					<div class="mt-5">
						<div class="d-flex">
							<span class="badge badge-secondary badge-sm me-3">
								<i class="fas fa-users text-gray-700 me-2"></i>
								<span class="fs-8">{{ driver?.ride.passengers_count }}</span>
							</span>
							<span class="badge badge-secondary badge-sm">
								<i class="fa-duotone fa-location-dot text-gray-700 me-2"></i>
								<span class="fs-8">{{ driver?.ride.stops_count }}</span>
							</span>
							<div class="badge text-capitalize fs-6 ms-auto" :class="badgeClass">
								{{ $t('general.enums.' + driver?.ride.status) }}
							</div>
						</div>
						<div class="mt-3 text-gray-800 fw-semibold">
							<CustomTimeLine :points="points" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import ProfileCard from '@/Components/Cards/ProfileCard.vue';
import Tooltip from '@/Components/Tooltip.vue';
import CustomTimeLine from '@/Components/General/CustomTimeLine.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		Link,
		ProfileCard,
		Tooltip,
		CustomTimeLine
	},
	props: {
		driver: {
			type: Object
		}
	},
	computed: {
		points() {
			return [
				{
					label: this.driver?.ride.route.origin?.name,
					reference: trans('transport.ride.start_of_ride'),
					time: this.driver?.ride.started_at
				},
				{
					label: this.driver?.ride.route.destination?.name,
					reference: trans('transport.ride.end_of_ride'),
					time: this.driver?.ride.arrived_at
				}
			];
		},
		badgeClass() {
			switch (this.driver?.ride.status) {
				case 'arrived':
					return 'badge-light-success';
				case 'ongoing':
					return 'badge-light-info';
				case 'canceled':
					return 'badge-light-dark';
			}
		}
	}
};
</script>
