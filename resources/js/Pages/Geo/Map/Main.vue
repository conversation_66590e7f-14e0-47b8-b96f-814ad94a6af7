<template>
	<BaseLayout :content-width-fluid="false" background-color="rgb(255, 255, 255)" fullMode>
		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<Head :title="$t('nav.live_map')" />

		<div class="d-flex flex-column flex-fill h-100">
			<div class="position-relative h-100">
				<!-- Map Container -->
				<div class="h-100">
					<DynamicMap
						v-if="!isLoading"
						:drivers="
							selectedRide
								? activeDrivers.filter(
										(driver) => driver.id == selectedRide?.driver.id
									)
								: activeDrivers
						"
						:ride="selectedRide"
						class="w-100 h-100"
						mode="live-map"
						:rounded="false"
						:shouldFitBound="isIntialDataLoad"
						@show-ride="showDriverInfo" />

					<!-- Empty State Overlay -->
					<div
						v-if="!isLoading && activeDrivers.length === 0"
						class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-flex justify-content-center align-items-center">
						<div class="bg-white p-4 rounded shadow text-center">
							<i class="fa-duotone fa-map-marker-slash fs-1 text-muted mb-3"></i>
							<h5>{{ $t('fleet.driver.no_drivers_found') }}</h5>
							<p class="text-muted">
								{{ $t('fleet.driver.no_drivers_match_filters') }}
							</p>
							<button @click="clearAllFilters" class="btn btn-sm btn-primary">
								{{ $t('general.clear_filters') }}
							</button>
						</div>
					</div>
				</div>

				<!-- Loading Overlay -->
				<div v-if="isLoading" class="position-absolute top-50 start-50 translate-middle">
					<LoadingSpinner />
				</div>

				<!-- Rides Carousel -->
				<div
					v-if="!selectedRide && activeDrivers.length > 0"
					class="position-absolute top-0 start-0 mt-15 overflow-auto custom-height scroll">
					<div class="d-flex flex-column gap-2 py-4 px-4">
						<RideMapCard
							v-for="driver in activeDrivers"
							:key="driver.id"
							:ride="driver.ride"
							@click="showDriverInfo(driver)" />
					</div>
				</div>

				<!-- Search Bar Overlay -->
				<div
					v-if="!selectedRide"
					class="position-absolute top-0 start-0 m-4"
					style="width: 300px">
					<MapFilters
						:available-routes="availableRoutes"
						:pending-filters="pendingFilters"
						@update:pending-filters="updatePendingFilters"
						@apply-filters="applyFilters" />
				</div>

				<!-- Side Panel Overlay -->
				<div
					v-if="selectedRide"
					class="position-absolute top-0 start-0 h-100"
					style="width: 22vw; min-width: 300px">
					<RideSidePanel :ride="selectedRide" @close="hideDriverInfo" />
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import RideSidePanel from '@/Pages/Geo/Map/RideSidePanel/Main.vue';
import DynamicMap from '@/Components/Location/DynamicMap.vue';
import RideMapCard from './Partials/RideMapCard.vue';
import MapFilters from './Partials/MapFilters.vue';
import LoadingSpinner from '@/Components/LoadingSpinner.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'Main',
	components: {
		BaseLayout,
		Head,
		HeaderMenu,
		DynamicMap,
		RideSidePanel,
		RideMapCard,
		MapFilters,
		LoadingSpinner
	},
	data() {
		return {
			isLoading: true,
			selectedRide: null,
			isIntialDataLoad: true,
			menu: [
				{
					hasPermission: true,
					active: route().current('geo.live-map'),
					route: route('geo.live-map'),
					icon: 'fa-duotone fa-location-crosshairs',
					content: trans('nav.live_map')
				},
				{
					hasPermission: true,
					active: route().current('geo.passengers-map'),
					route: route('geo.passengers-map'),
					icon: 'fa-duotone fa-person-seat-reclined',
					content: trans('transport.passenger.names')
				},
				{
					hasPermission: true,
					active: route().current('geo.routes-map'),
					route: route('geo.routes-map'),
					icon: 'fa-duotone fa-route',
					content: trans('transport.route.names')
				}
			],
			authUser: usePage().props?.user,
			drivers: [],
			activeFilters: {
				status: null,
				route_id: null,
				search: ''
			},
			pendingFilters: {
				status: null,
				route_id: null,
				search: ''
			},
			echoChannel: null
		};
	},
	computed: {
		availableRoutes() {
			return [...new Set(this.activeDrivers.map((driver) => driver.ride?.route))].filter(
				Boolean
			);
		},
		activeDrivers() {
			return this.drivers.filter((driver) => {
				if (!driver?.coordinates?.lat || !driver?.coordinates?.lng) return false;

				const matchesSearch =
					!this.activeFilters.search ||
					driver.name.toLowerCase().includes(this.activeFilters.search.toLowerCase());
				const matchesStatus =
					!this.activeFilters.status || driver.ride?.status === this.activeFilters.status;
				const matchesRoute =
					!this.activeFilters.route_id ||
					driver.ride?.route_id === this.activeFilters.route_id;

				return matchesSearch && matchesStatus && matchesRoute;
			});
		}
	},
	mounted() {
		this.loadDrivers();
		this.echoChannel = this.setupBroadcasting();
	},
	beforeUnmount() {
		if (this.echoChannel) {
			this.echoChannel.stopListening('.driver-location.update');
			this.echoChannel.unsubscribe();
		}
	},
	methods: {
		updatePendingFilters(newFilters) {
			this.pendingFilters = newFilters;
		},
		applyFilters() {
			this.activeFilters = { ...this.pendingFilters };
		},
		clearAllFilters() {
			const emptyFilters = {
				status: null,
				route_id: null,
				search: ''
			};
			this.pendingFilters = { ...emptyFilters };
			this.activeFilters = { ...emptyFilters };
		},
		async loadDrivers() {
			this.isLoading = true;
			try {
				const response = await axios.get(route('geo.locations.live.drivers.list'));
				this.drivers = response.data.data;
			} catch (error) {
				console.error('Failed to load drivers:', error);
			} finally {
				this.isLoading = false;
				setTimeout(() => {
					this.isIntialDataLoad = false;
				}, 500);
			}
		},
		async showDriverInfo(driver) {
			try {
				const response = await axios.get(
					route('geo.locations.live.drivers.ride.get', { driver: driver.id })
				);
				this.selectedRide = response.data.data;
			} catch (error) {
				console.error('Failed to load driver ride:', error);
			}
		},
		hideDriverInfo() {
			this.selectedRide = null;
		},
		setupBroadcasting() {
			let echoPrivateChannel = window.Echo.private('company.' + this.authUser?.company?.id);

			echoPrivateChannel.listen('.driver-location.update', this.handleDriverLocationUpdate);

			return echoPrivateChannel;
		},
		handleDriverLocationUpdate(event) {
			const driverIndex = this.drivers.findIndex((driver) => driver.id === event.driver_id);

			if (driverIndex !== -1) {
				const driver = this.drivers[driverIndex];
				driver.coordinates = driver.coordinates || { lat: null, lng: null };
				driver.coordinates.lat = event.lat;
				driver.coordinates.lng = event.lng;
			} else {
				this.fetchDriverDetails(event.driver_id);
			}
		},
		async fetchDriverDetails(driverId) {
			try {
				const response = await axios.get(
					route('geo.locations.live.drivers.get', { driver: driverId })
				);
				this.drivers.push(response.data.data);
			} catch (error) {
				console.error('Failed to fetch driver details:', error);
			}
		}
	}
};
</script>

<style scoped>
.custom-height {
	max-height: calc(100vh - 117px);
}

/* Hide scrollbar while maintaining functionality */
.overflow-auto {
	/* For Chrome, Safari, and Opera */
	&::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For IE and Edge */
	-ms-overflow-style: none;
}
</style>
