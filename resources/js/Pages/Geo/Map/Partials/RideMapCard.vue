<template>
	<div
		class="card flex-shrink-0 bg-white bg-opacity-90 shadow-xm border-hover-primary border-2 hover-elevate-up"
		style="width: 210px; cursor: pointer">
		<div class="card-body p-2">
			<div class="d-flex flex-column gap-2">
				<div class="d-flex align-items-center gap-2">
					<span class="text-muted">{{ formatTime(ride?.started_at) }}</span>
					<i class="fa-duotone fa-circle-dot text-success"></i>
					<span>{{ ride?.route.origin?.name }}</span>
				</div>
				<div class="d-flex align-items-center gap-2">
					<span class="text-muted">{{
						ride?.arrived_at ? formatTime(ride?.arrived_at) : '-'
					}}</span>
					<i class="fa-duotone fa-circle-dot text-primary"></i>
					<span>{{ ride?.route.destination?.name }}</span>
				</div>
			</div>

			<div class="d-flex align-items-center gap-3 mt-3">
				<div class="d-flex align-items-center gap-1">
					<i class="fa-duotone fa-bus text-muted"></i>
					<span>{{ ride?.vehicle?.capacity }}</span>
				</div>
				<div class="d-flex align-items-center gap-1">
					<i class="fa-duotone fa-users text-muted"></i>
					<span>{{ ride?.passengers_count }}</span>
				</div>
				<span class="badge badge-light-primary text-capitalize ms-auto">
					{{ $t(`general.enums.${ride?.status}`) }}</span
				>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'RideCard',
	props: {
		ride: {
			type: Object,
			required: true
		}
	},
	methods: {
		formatTime(time) {
			return new Date(time).toLocaleTimeString('en-US', {
				hour: 'numeric',
				minute: '2-digit',
				hour12: false
			});
		}
	}
};
</script>
