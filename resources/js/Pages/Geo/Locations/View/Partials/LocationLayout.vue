<template>
	<BaseLayout>
		<Head :title="$t('nav.locations')" />

		<div class="row g-3 h-100">
			<div class="col-md-4 col-lg-3">
				<div class="card p-4 custom-sticky">
					<div class="d-flex flex-center flex-column mb-5">
						<div class="symbol symbol-150px">
							<div class="symbol-label">
								<img
									:src="singleLocation?.image"
									class="h-100 rounded"
									style="object-fit: contain" />
							</div>
						</div>
					</div>

					<div class="d-flex flex-stack fs-4 py-3">
						<div class="fw-bold">
							{{ $t('geo.location.details') }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('general.name') }}
						</div>
						<div class="text-gray-600">
							{{ singleLocation?.name ?? '-' }}
						</div>
					</div>

					<div v-if="singleLocation?.primary" class="pb-2 fs-6">
						<span class="badge badge-light-success fs-7 mt-5">
							{{ $t('geo.location.labels.primary') }}
						</span>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('geo.location.labels.postal_code') }}
						</div>
						<div class="text-gray-600">
							{{ singleLocation?.postal_code ?? '-' }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('geo.location.labels.city') }}
						</div>
						<div class="text-gray-600">
							{{ singleLocation?.city ?? '-' }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('geo.location.labels.state') }}
						</div>
						<div class="text-gray-600">
							{{ singleLocation?.state ?? '-' }}
						</div>
					</div>

					<div class="pb-2 fs-6">
						<div class="fw-bold mt-5 text-capitalize">
							{{ $t('geo.location.labels.country') }}
						</div>
						<div class="text-gray-600">
							{{ singleLocation?.country ?? '-' }}
						</div>
					</div>
				</div>
			</div>

			<div class="col-md-8 col-lg-9">
				<PageNav :single-location="singleLocation" class="mb-3" />
				<div class="card h-100">
					<div class="card-body">
						<slot />
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>
<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import PageNav from '@/Pages/Geo/Locations/View/Partials/PageNav.vue';
import ImageUploader from '@/Components/ImageUploader.vue';
import { Link, Head } from '@inertiajs/vue3';

export default {
	name: 'LocationLayout',
	components: {
		Link,
		ImageUploader,
		BaseLayout,
		PageNav,
		Head
	},
	props: {
		singleLocation: {
			type: Object,
			required: true
		}
	}
};
</script>
