<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<LocationsToolbar />
		</template>

		<Head :title="$t('nav.locations')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<LocationsFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="locations.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<LocationsTable :locations="locations.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="colg4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="locations.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import LocationsToolbar from '@/Pages/Geo/Locations/Partials/LocationsToolbar.vue';
import LocationsTable from '@/Pages/Geo/Locations/List/Partials/LocationsTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import LocationsFilters from '@/Pages/Geo/Locations/List/Partials/LocationsFilters.vue';

export default {
	name: 'Main.vue',
	data() {
		return {
			filters: this.getQueryParams()
		};
	},
	methods: {
		getQueryParams() {
			const urlParams = new URLSearchParams(window.location.search);

			return {
				search: urlParams?.get('search') || ''
			};
		},
		async updatePage() {
			await this.$nextTick();

			let cleanFilters = Object.assign({}, this.filters);
			for (const field in cleanFilters) {
				if (!cleanFilters[field]) {
					delete cleanFilters[field];
				}
			}

			router.get(route('geo.locations.list'), cleanFilters, {
				preserveState: true
			});
		},
		clearFilters() {
			this.filters = {
				search: ''
			};
			this.updatePage();
		}
	},
	components: {
		BaseLayout,
		Head,
		router,
		LocationsToolbar,
		LocationsTable,
		Pagination,
		EmptyResults,
		LocationsFilters
	},
	props: {
		locations: Object
	}
};
</script>
