<template>
	<tr class="odd">
		<td>
			<div class="d-flex align-items-center">
				<Link
					:href="
						route(
							review.reviewer_type === 'passenger'
								? 'transport.passengers.view'
								: 'transport.responsibles.view',
							review.reviewer_id
						)
					">
					<div class="cursor-pointer symbol symbol-30px symbol-md-40px">
						<img :src="review.reviewer.image" alt="review" />
					</div>
				</Link>
			</div>
		</td>

		<td>
			<Link
				:href="
					route(
						review.reviewer_type === 'passenger'
							? 'transport.passengers.view'
							: 'transport.responsibles.view',
						review.reviewer_id
					)
				"
				class="text-gray-800 text-hover-primary">
				{{ review.reviewer.name }}
			</Link>
		</td>

		<td>
			<Link :href="route('transport.rides.view', review.ride_id)" class="text-gray-800">
				<div class="text-gray-600 text-hover-primary fw-bold fs-5">
					<i class="fa-duotone fa-road text-primary me-1"></i>
					#{{ review.ride_id }}
				</div>
			</Link>
		</td>

		<td v-if="review?.ride?.driver">
			<ProfileCard :profile="review.ride?.driver" size="40" baseRoute="fleet.drivers.view" />
		</td>
		<td v-else>
			{{ $t('general.profile_not_found') }}
		</td>

		<td>
			<StarRating :rating="review.rating" />
		</td>

		<td>
			<div class="text-gray-800">
				{{ formatDate(review.created_at) }}
			</div>
		</td>

		<td>
			<ReviewDetailModal :review="review" />
		</td>
	</tr>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import StarRating from '@/Components/Review/StarRating.vue';
import ProfileCard from '@/Components/Cards/ProfileCard.vue';
import { format } from 'date-fns';
import ReviewDetailModal from './ReviewDetailModal.vue';

export default {
	components: {
		Link,
		StarRating,
		ProfileCard,
		ReviewDetailModal
	},
	props: {
		review: Object
	},
	methods: {
		formatDate(dateString) {
			const date = new Date(dateString);
			return format(date, 'dd/MM/yyyy HH:mm ');
		}
	}
};
</script>
