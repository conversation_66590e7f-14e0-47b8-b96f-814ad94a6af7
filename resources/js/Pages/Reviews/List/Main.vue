<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<ReviewsToolbar />
		</template>

		<Head :title="$t('nav.reviews')" />

		<div class="row">
			<div class="col-lg-12 order-lg-1 order-2">
				<div class="card full-height">
					<div class="d-flex p-6 pb-0">
						<ReviewsFilters />
					</div>
					<div class="card-body p-4">
						<div
							v-if="reviews.data.length"
							class="dataTables_wrapper dt-bootstrap4 no-footer h-100">
							<div class="table-responsive h-100 p-5">
								<ReviewsTable :reviews="reviews.data" />
							</div>
						</div>

						<div v-else class="row justify-content-center w-100">
							<EmptyResults class="col-4 col-sm-6" />
						</div>
					</div>

					<div class="card-footer">
						<div
							class="d-flex align-items-center justify-content-center justify-content-md-center">
							<Pagination :links="reviews.links"></Pagination>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import ReviewsToolbar from '@/Pages/Reviews/List/Partials/ReviewsToolbar.vue';
import ReviewsTable from '@/Pages/Reviews/List/Partials/ReviewsTable.vue';
import Pagination from '@/Components/Pagination.vue';
import EmptyResults from '@/Components/EmptyResults.vue';
import ReviewsFilters from '@/Pages/Reviews/List/Partials/ReviewsFilters.vue';

export default {
	name: 'Main.vue',
	components: {
		BaseLayout,
		Head,
		router,
		ReviewsToolbar,
		ReviewsTable,
		Pagination,
		EmptyResults,
		ReviewsFilters
	},
	props: {
		reviews: Object
	}
};
</script>
