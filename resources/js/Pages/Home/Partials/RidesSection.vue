<template>
	<div class="card card-flush px-1 h-100">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center text-capitalize">
					<i class="fa-duotone fa-road me-4 fs-1 text-primary"></i>
					{{ $t('transport.ride.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('transport.rides.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i
				></Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<OngoingTab :rides="ongoingRides" class="" />
			<!-- <Tab :tabs="tabs" :activeTab="activeTab">
				<template #ongoing>
				</template>
				<template #coming>
					<ComingTab :rides="comingRides" />
				</template>
			</Tab> -->
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import Tab from '@/Components/General/Tabs/Tab.vue';
import OngoingTab from './OngoingTab.vue';
import ComingTab from './ComingTab.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'RidesSection',
	components: {
		Link,
		Tab,
		OngoingTab,
		ComingTab
	},
	props: {
		rides: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			tabs: [
				{ id: 'ongoing', label: trans('transport.ride.ongoing') },
				{ id: 'coming', label: trans('transport.ride.next_5_days') }
			],
			activeTab: 'ongoing'
		};
	},
	computed: {
		ongoingRides() {
			return this.rides.ongoing_rides;
		},
		comingRides() {
			return this.rides.next_rides;
		}
	}
};
</script>
