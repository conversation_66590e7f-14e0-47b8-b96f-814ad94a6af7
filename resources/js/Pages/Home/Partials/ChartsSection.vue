<template>
	<div class="row g-3">
		<div v-for="(chart, index) in charts" :key="index" class="col-md-6">
			<component
				:is="chart.type === 'pie' ? 'Pie' : 'Line'"
				:title="chart.title"
				:symbol="chart.symbol"
				:url="chart.url"
				:filters="filters"
				:doughnut="chart.doughnut"
				:color="chart.color"
				:description="chart.description"
				:orientation="chart.orientation"
				:showButtons="chart.showButtons"
				:serieName="chart.serieName" />
		</div>
	</div>
</template>

<script>
import Pie from '@/Pages/Analytics/Partials/Pie.vue';
import Line from '@/Pages/Analytics/Partials/Line.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'ChartsSection',
	components: {
		Pie,
		Line
	},
	data() {
		return {
			filters: {}
		};
	},
	computed: {
		charts() {
			return [
				{
					title: trans('analytics.charts.ride_status_count.title'),
					type: 'pie',
					url: route('analytics.report.transport', {
						key: 'ride-status-count'
					}),
					symbol: '',
					doughnut: false,
					description: trans('analytics.charts.ride_status_count.description')
				},
				{
					title: trans('analytics.charts.ride_duration.title'),
					type: 'line',
					url: route('analytics.report.transport', {
						key: 'ride-duration-metrics'
					}),
					symbol: 'min',
					color: '#5B2C87',
					description: trans('analytics.charts.ride_duration.description'),
					showButtons: true,
					serieName: 'transport.route.names'
				}
			];
		}
	}
};
</script>
