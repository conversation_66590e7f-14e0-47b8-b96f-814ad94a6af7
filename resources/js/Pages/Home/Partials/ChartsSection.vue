<template>
	<div class="card card-flush mb-4 h-100">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center">
					<i class="fa-duotone fa-chart-line me-4 fs-1 text-primary"></i>
					{{ $t('analytics.charts.title') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link :href="route('analytics.view')" class="btn btn-primary btn-sm">
					{{ $t('analytics.view_analytics') }}
					<i class="fa-duotone fa-arrow-right ms-1"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3">
			<div class="row g-3">
				<div v-for="(chart, index) in charts" :key="index" class="col-md-6">
					<component
						v-if="chart.type !== 'radar'"
						:is="chart.type === 'pie' ? 'Pie' : 'Line'"
						:title="chart.title"
						:symbol="chart.symbol"
						:url="chart.url"
						:filters="monthToDateFilters"
						:doughnut="chart.doughnut"
						:color="chart.color"
						:description="chart.description"
						:orientation="chart.orientation"
						:showButtons="chart.showButtons"
						:serieName="chart.serieName" />
					<Radar
						v-else
						:title="chart.title"
						:symbol="chart.symbol"
						:url="chart.url"
						:filters="monthToDateFilters"
						:description="chart.description"
						:showDataSelector="chart.showButtons"
						:serieName="chart.serieName" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import Pie from '@/Pages/Analytics/Partials/Pie.vue';
import Radar from '@/Pages/Analytics/Partials/Radar.vue';
import Line from '@/Pages/Analytics/Partials/Line.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'ChartsSection',
	components: {
		Link,
		Pie,
		Line,
		Radar
	},
	computed: {
		monthToDateFilters() {
			const now = new Date();
			const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
			return {
				from: startOfMonth.toISOString().split('T')[0],
				to: now.toISOString().split('T')[0],
				group_by: 'daily'
			};
		},
		charts() {
			return [
				{
					title:
						trans('analytics.charts.ride_status_count.title') +
						' - ' +
						trans('analytics.this_month'),
					type: 'radar',
					url: route('analytics.report.transport', {
						key: 'ride-status-count'
					}),
					symbol: '',
					description: trans('analytics.charts.ride_status_count.description'),
					serieName: 'transport.route.names'
				},
				{
					title:
						trans('analytics.charts.ride_duration.title') +
						' - ' +
						trans('analytics.this_month'),
					type: 'line',
					url: route('analytics.report.transport', {
						key: 'ride-duration-metrics'
					}),
					symbol: 'min',
					color: '#5B2C87',
					description: trans('analytics.charts.ride_duration.description'),
					showButtons: true,
					serieName: 'transport.route.names'
				}
			];
		}
	}
};
</script>
