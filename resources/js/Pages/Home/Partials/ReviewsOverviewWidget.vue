<template>
	<div class="card card-flush">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center">
					<i class="fa-duotone fa-star me-4 fs-1 text-warning"></i>
					{{ $t('review.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('reviews.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<template v-if="reviews.length > 0">
				<div class="row mb-4">
					<div class="col-6 text-center">
						<div class="fs-2x fw-bold text-warning">{{ averageRating }}</div>
						<StarRating :rating="averageRating" />
						<div class="text-gray-600 fs-7 mt-1">
							{{ $tChoice('review.rating.count', reviews.length) }}
						</div>
					</div>
					<div class="col-6">
						<div
							v-for="rating in ratingCounts"
							:key="rating.score"
							class="d-flex align-items-center mb-1">
							<div class="fw-semibold me-2 text-gray-600" style="min-width: 15px">
								{{ rating.score }}
							</div>
							<div class="progress h-6px flex-grow-1 me-2">
								<div
									class="progress-bar bg-warning"
									:style="{ width: `${rating.percentage}%` }"></div>
							</div>
							<div class="text-gray-500 fs-8" style="min-width: 20px">
								{{ rating.count }}
							</div>
						</div>
					</div>
				</div>

				<div class="separator my-3"></div>

				<div class="scroll h-150px">
					<template v-for="(review, index) in recentReviews" :key="review.id">
						<div v-if="index > 0" class="separator my-2"></div>
						<div class="d-flex align-items-start py-2">
							<div class="symbol symbol-30px me-3">
								<img
									v-if="review.reviewer?.image"
									:src="review.reviewer.image"
									:alt="review.reviewer.name"
									class="img-fluid" />
								<div v-else class="symbol-label bg-light-warning">
									<i class="fa-duotone fa-user fs-5 text-warning"></i>
								</div>
							</div>
							<div class="flex-grow-1">
								<div class="d-flex justify-content-between align-items-start">
									<div>
										<div class="text-gray-800 fw-semibold fs-7">
											{{
												review.reviewer?.name ||
												$t('general.profile_not_found')
											}}
										</div>
										<StarRating :rating="review.rating" size="sm" />
									</div>
									<div class="text-gray-500 fs-8">
										{{ review.created_at_diff }}
									</div>
								</div>
								<div
									v-if="review.comment"
									class="text-gray-600 fs-8 mt-1"
									style="line-height: 1.2">
									{{ truncateText(review.comment, 60) }}
								</div>
							</div>
						</div>
					</template>
				</div>
			</template>

			<div v-else class="text-center py-4">
				<i class="fa-duotone fa-star fs-2x text-gray-400 mb-2"></i>
				<div class="text-gray-600 fs-6">{{ $t('review.no_reviews') }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import StarRating from '@/Components/Review/StarRating.vue';

export default {
	name: 'ReviewsOverviewWidget',
	components: {
		Link,
		StarRating
	},
	props: {
		reviews: {
			type: Array,
			required: true
		}
	},
	computed: {
		averageRating() {
			if (this.reviews.length === 0) return '0.0';
			const total = this.reviews.reduce((sum, review) => sum + parseFloat(review.rating), 0);
			return (total / this.reviews.length).toFixed(1);
		},
		ratingCounts() {
			const counts = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
			this.reviews.forEach((review) => {
				const wholePart = Math.floor(parseFloat(review.rating));
				if (counts[wholePart] !== undefined) {
					counts[wholePart]++;
				}
			});

			const totalRatings = this.reviews.length;
			return Object.keys(counts)
				.map((score) => ({
					score: parseInt(score),
					count: counts[score],
					percentage: totalRatings ? ((counts[score] / totalRatings) * 100).toFixed(0) : 0
				}))
				.sort((a, b) => b.score - a.score);
		},
		recentReviews() {
			return this.reviews.slice(0, 3);
		}
	},
	methods: {
		truncateText(text, maxLength) {
			if (!text) return '';
			return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
		}
	}
};
</script>
