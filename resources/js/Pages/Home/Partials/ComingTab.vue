<template>
	<div class="card h-350px scroll p-0 border-0">
		<div class="card-body h-100 p-0">
			<template v-if="rides.length > 0" v-for="(ride, index) in rides" :key="`ride-${index}`">
				<Link :href="route('transport.rides.view', ride.id)">
					<div
						class="card h-125px border-hover-primary cursor-pointer"
						:class="{ 'mb-3': index !== rides?.length - 1 }">
						<div class="card-body p-3">
							<div class="d-flex justify-content-between align-items-center">
								<div class="text-gray-600 fw-bold fs-5">#{{ ride.id }}</div>
								<div class="badge badge-light-primary text-capitalize">
									{{ $t(`general.enums.${ride.status}`) }}
								</div>
							</div>
							<div class="d-flex justify-content-start align-items-center mt-1">
								<span class="text-gray-800 fw-bold fs-5 text-capitalize">
									{{ ride.vehicle?.name }}
								</span>
							</div>
							<div class="d-flex justify-content-start align-items-center mt-1">
								<span class="text-primary fw-bold fs-6 text-uppercase">
									{{ ride.vehicle?.plate ?? '-' }}
								</span>
							</div>
							<div
								class="d-flex flex-wrap justify-content-between align-items-center mt-4">
								<div class="text-gray-600 fs-7 fw-semibold text-capitalize">
									<i class="fa-duotone fa-user-tie me-1"></i>
									{{ ride.driver?.name ?? '-' }}
								</div>
								<div class="text-gray-600 fs-7 fw-semibold text-capitalize ms-2">
									<i class="fa-duotone fa-phone me-1"></i>
									{{ ride.driver?.phone ?? '-' }}
								</div>
								<div class="text-gray-600 fs-7 fw-semibold text-capitalize ms-2">
									<i class="fa-duotone fa-clock me-1"></i>
									{{ formattedTime(ride.schedule.start_date) }}
								</div>
							</div>
						</div>
					</div>
				</Link>
			</template>
			<EmptyResults v-else :message="$t('general.messages.no_upcoming_rides')" />
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';

export default {
	name: 'ComingTab',
	components: {
		Link,
		EmptyResults
	},
	props: {
		rides: {
			type: Object,
			required: true
		}
	},
	methods: {
		formattedTime(time) {
			const date = new Date(time);
			const day = date.getDay();
			const hours = date.getHours();
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		}
	}
};
</script>
