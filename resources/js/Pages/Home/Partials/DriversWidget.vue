<template>
	<div class="card card-flush">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center">
					<i class="fa-duotone fa-steering-wheel me-4 fs-1 text-primary"></i>
					{{ $t('fleet.driver.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('fleet.drivers.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<template v-if="drivers.length > 0">
				<div class="scroll h-250px">
					<template v-for="(driver, index) in drivers" :key="driver.id">
						<div v-if="index > 0" class="separator my-2"></div>
						<ProfileCard
							:profile="driver"
							baseRoute="fleet.drivers.view"
							size="40"
							showStatus
							class="py-2" />
					</template>
				</div>
			</template>
			
			<div v-else class="text-center py-4">
				<i class="fa-duotone fa-steering-wheel fs-2x text-gray-400 mb-2"></i>
				<div class="text-gray-600 fs-6">{{ $t('fleet.driver.no_drivers') }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import ProfileCard from '@/Components/Cards/ProfileCard.vue';

export default {
	name: 'DriversWidget',
	components: {
		Link,
		ProfileCard
	},
	props: {
		drivers: {
			type: Array,
			required: true
		}
	}
};
</script>
