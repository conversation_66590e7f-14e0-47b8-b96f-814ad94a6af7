<script>
import { Head } from '@inertiajs/vue3';
import BaseLayout from '../../Layouts/MainLayout/BaseLayout.vue';
import HomeToolbar from '@/Pages/Home/Partials/HomeToolbar.vue';
import StatisticsSection from '@/Pages/Home/Partials/StatisticsSection.vue';
import ChartsSection from '@/Pages/Home/Partials/ChartsSection.vue';
import RidesSection from '@/Pages/Home/Partials/RidesSection.vue';
import TodayIncidentsWidget from '@/Pages/Home/Partials/TodayIncidentsWidget.vue';
import TodayAbsencesWidget from '@/Pages/Home/Partials/TodayAbsencesWidget.vue';
import DriversWidget from '@/Pages/Home/Partials/DriversWidget.vue';
import ReviewsOverviewWidget from '@/Pages/Home/Partials/ReviewsOverviewWidget.vue';

export default {
	components: {
		HomeToolbar,
		BaseLayout,
		Head,
		StatisticsSection,
		ChartsSection,
		RidesSection,
		TodayIncidentsWidget,
		TodayAbsencesWidget,
		DriversWidget,
		ReviewsOverviewWidget
	},
	props: {
		rides: {
			type: Object,
			required: true
		},
		todayIncidents: {
			type: Array,
			required: true
		},
		todayAbsences: {
			type: Array,
			required: true
		},
		topDrivers: {
			type: Array,
			required: true
		},
		recentReviews: {
			type: Array,
			required: true
		},
		dashboardStats: {
			type: Object,
			required: true
		}
	}
};
</script>

<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<HomeToolbar />
		</template>

		<Head :title="$t('nav.dashboard')"></Head>

		<!-- Top Statistics Section -->
		<StatisticsSection :stats="dashboardStats" />

		<!-- Charts and Rides Section -->
		<div class="row g-3 mb-4">
			<div class="col-lg-8">
				<ChartsSection />
			</div>
			<div class="col-lg-4">
				<RidesSection :rides="rides" />
			</div>
		</div>

		<!-- Dashboard Widgets Grid -->
		<div class="row g-3">
			<!-- High Priority Widgets -->
			<div class="col-lg-6">
				<TodayIncidentsWidget
					:incidents="todayIncidents"
					:count="dashboardStats.incidents_today_count" />
			</div>
			<div class="col-lg-6">
				<ReviewsOverviewWidget :reviews="recentReviews" />
			</div>

			<!-- Medium Priority Widgets -->
			<div class="col-lg-6">
				<DriversWidget :drivers="topDrivers" />
			</div>
			<div class="col-lg-6">
				<TodayAbsencesWidget
					:absences="todayAbsences"
					:count="dashboardStats.absences_today_count" />
			</div>
		</div>
	</BaseLayout>
</template>
