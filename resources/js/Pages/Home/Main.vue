<script>
import { Head } from '@inertiajs/vue3';
import BaseLayout from '../../Layouts/MainLayout/BaseLayout.vue';
import HomeToolbar from '@/Pages/Home/Partials/HomeToolbar.vue';
import StatisticsSection from '@/Pages/Home/Partials/StatisticsSection.vue';
import ChartsSection from '@/Pages/Home/Partials/ChartsSection.vue';
import RidesSection from '@/Pages/Home/Partials/RidesSection.vue';
import TodayIncidentsWidget from '@/Pages/Home/Partials/TodayIncidentsWidget.vue';
import TodayAbsencesWidget from '@/Pages/Home/Partials/TodayAbsencesWidget.vue';
import DriversWidget from '@/Pages/Home/Partials/DriversWidget.vue';
import VehiclesWidget from '@/Pages/Home/Partials/VehiclesWidget.vue';
import AssistantsWidget from '@/Pages/Home/Partials/AssistantsWidget.vue';
import ReviewsOverviewWidget from '@/Pages/Home/Partials/ReviewsOverviewWidget.vue';

export default {
	components: {
		HomeToolbar,
		BaseLayout,
		Head,
		StatisticsSection,
		ChartsSection,
		RidesSection,
		TodayIncidentsWidget,
		TodayAbsencesWidget,
		DriversWidget,
		VehiclesWidget,
		AssistantsWidget,
		ReviewsOverviewWidget
	},
	props: {
		rides: {
			type: Object,
			required: true
		},
		todayIncidents: {
			type: Array,
			required: true
		},
		todayAbsences: {
			type: Array,
			required: true
		},
		topDrivers: {
			type: Array,
			required: true
		},
		topVehicles: {
			type: Array,
			required: true
		},
		topAssistants: {
			type: Array,
			required: true
		},
		recentReviews: {
			type: Array,
			required: true
		},
		dashboardStats: {
			type: Object,
			required: true
		}
	}
};
</script>

<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<HomeToolbar />
		</template>

		<Head :title="$t('nav.dashboard')"></Head>

		<!-- 1. Top Statistics Section -->
		<StatisticsSection :stats="dashboardStats" />

		<!-- 2. High Priority Row -->
		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-6">
				<TodayIncidentsWidget
					:incidents="todayIncidents"
					:count="dashboardStats.incidents_today_count" />
			</div>
			<div class="col-12 col-lg-6">
				<ReviewsOverviewWidget :reviews="recentReviews" />
			</div>
		</div>

		<!-- 3. Medium Priority Row -->
		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-6">
				<RidesSection :rides="rides" />
			</div>
			<div class="col-12 col-lg-6">
				<DriversWidget :drivers="topDrivers" />
			</div>
		</div>

		<!-- 4. Medium Priority Row 2 -->
		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-6">
				<VehiclesWidget :vehicles="topVehicles" />
			</div>
			<div class="col-12 col-lg-6">
				<AssistantsWidget :assistants="topAssistants" />
			</div>
		</div>

		<!-- 5. Lower Priority - Charts -->
		<ChartsSection />
	</BaseLayout>
</template>
