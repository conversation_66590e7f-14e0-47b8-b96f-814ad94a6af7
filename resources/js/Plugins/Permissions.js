import { usePage } from '@inertiajs/vue3';

export default {
	install: (app) => {
		app.config.globalProperties.$can = function (key, employeeId) {
			return true;

			if (employeeId) {
				key = key + '.' + employeeId;
			}

			if (this.$isAdmin()) {
				return true;
			}

			return Object.values(usePage().props.auth?.permissions).includes(key);
		};

		app.config.globalProperties.$isAdmin = function () {
			return usePage().props.auth?.permissions === 'admin';
		};
	}
};
