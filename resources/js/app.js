import './bootstrap';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m';
import Toast from 'vue-toastification';
import ToasterMixin from '@/Mixins/Toaster';
import ConfirmAlertMixin from '@/Mixins/ConfirmAlert';
import Auth from '@/Plugins/Auth';
import Helpers from '@/Plugins/Helpers';
import Permissions from '@/Plugins/Permissions';
import VueClickAway from 'vue3-click-away';
import PermissionsHandler from '@/Mixins/PermissionsHandler';
import { i18nVue } from 'laravel-vue-i18n';
import * as Sentry from '@sentry/vue';
import mixpanel from 'mixpanel-browser';

const appName = window.document.getElementsByTagName('title')[0]?.innerText || 'Yosr';

createInertiaApp({
	title: (title) => `${title} | ${appName}`,
	resolve: (name) =>
		resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
	setup({ el, App, props, plugin }) {
		const app = createApp({ render: () => h(App, props) });

		if (import.meta.env.VITE_APP_ENV !== 'local') {
			Sentry.init({
				app,
				dsn: import.meta.env.VITE_SENTRY_DSN,
				integrations: []
			});
		}

		if (import.meta.env.VITE_APP_ENV === 'production') {
			mixpanel.init(import.meta.env.VITE_MIXPANEL_TOKEN, {});
		}

		return app
			.use(Toast, {
				toastClassName: 'custom-toast-class',
				bodyClassName: 'custom-body-class',
				timeout: 3000
			})
			.mixin(ToasterMixin)
			.mixin(ConfirmAlertMixin)
			.mixin(PermissionsHandler)
			.use(plugin)
			.use(ZiggyVue, Ziggy)
			.use(Auth)
			.use(Permissions)
			.use(Helpers)
			.use(VueClickAway)
			.use(i18nVue, {
				resolve: async (lang) => {
					const langs = import.meta.glob('../../lang/*.json');
					return await langs[`../../lang/${lang}.json`]();
				}
			})
			.mount(el);
	}
});
