<template>
	<div class="overflow-hidden border-gray-300 app-sidebar-menu flex-column-fluid border-end">
		<div class="my-5 app-sidebar-wrapper hover-scroll-overlay-y">
			<div
				class="text-gray-800 menu menu-column menu-sub-indention fw-semibold h-100 fs-4 justify-content-center">
				<template v-for="(item, j) in mainMenu" :key="j">
					<template v-if="item.hasPermissions">
						<template v-if="!item.section">
							<MenuAccordion v-if="item.subMenu?.length" :item="item" />

							<div v-else class="menu-item">
								<Link
									:class="{
										'border-start border-3 border-primary text-primary fw-semibold':
											item.active
									}"
									:href="item.route"
									class="px-4 py-2 mx-2 menu-link text-hover-primary"
									@click="handleNormalLinkClick">
									<div class="menu-icon me-4">
										<i
											:class="
												item.fontIcon +
												(item.active ? ' text-primary' : ' text-gray-700')
											"
											class="fs-3"></i>
									</div>
									<div class="w-100 fs-5 d-flex text-capitalize">
										{{ $t(item.heading) }}
										<span
											v-if="item.coming"
											class="badge badge-sm badge-light-primary ms-3">
											{{ $t('general.soon') }}
										</span>
										<span
											v-if="item.notifications"
											class="badge badge-circle badge-light-danger ms-auto">
											{{ item.notifications }}
										</span>
									</div>
									<span v-if="item.subMenu" class="menu-arrow"></span>
								</Link>
							</div>
						</template>

						<div v-else class="menu-item">
							<div class="menu-content">
								<div
									class="menu-heading text-uppercase fs-7"
									style="letter-spacing: 1px">
									{{ $t(item.heading) }}
								</div>
							</div>
						</div>
					</template>
				</template>
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import MenuAccordion from '@/Layouts/MainLayout/sidebar/Partials/MenuAccordion.vue';

export default {
	components: {
		Link,
		MenuAccordion
	},
	data() {
		return {
			menu: [
				{
					heading: this.$t('nav.dashboard'),
					route: route('home'),
					fontIcon: 'fa-duotone fa-house-chimney',
					active: route().current('home'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.calendar'),
					route: route('transport.rides.calendar'),
					fontIcon: 'fa-duotone fa-calendar-range',
					active: route().current('transport.rides.calendar'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.maps'),
					route: route('geo.live-map'),
					fontIcon: 'fa-duotone fa-map',
					active:
						route().current('geo.live-map') || route().current('geo.passengers-map'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.analytics'),
					route: route('analytics.view'),
					fontIcon: 'fa-duotone fa-chart-line',
					active: route().current('analytics.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.plans'),
					route: route('plans.list'),
					fontIcon: 'fa-duotone fa-ballot-check',
					active:
						route().current('plans.*') &&
						!route().current('plans.attendances.*') &&
						!route().current('plans.absences.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.absences'),
					route: route('plans.absences.list'),
					fontIcon: 'fa-duotone fa-calendar-check',
					active: route().current('plans.absences.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.drivers'),
					route: route('fleet.drivers.list'),
					fontIcon: 'fa-duotone fa-user-tie',
					active:
						route().current('fleet.drivers.*') || route().current('fleet.assistants.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.vehicles'),
					route: route('fleet.vehicles.list'),
					fontIcon: 'fa-duotone fa-shuttle-van',
					active: route().current('fleet.vehicles.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.reviews'),
					route: route('reviews.list'),
					fontIcon: 'fa-duotone fa-star-sharp-half-stroke',
					active: route().current('reviews.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.incidents'),
					route: route('incidents.list'),
					fontIcon: 'fa-duotone fa-brake-warning',
					active: route().current('incidents.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.routes'),
					route: route('transport.routes.list'),
					fontIcon: 'fa-duotone fa-route',
					active: route().current('transport.routes.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.rides'),
					route: route('transport.rides.list'),
					fontIcon: 'fa-duotone fa-road',
					active:
						route().current('transport.rides.*') &&
						!route().current('transport.rides.calendar'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.passengers'),
					route: route('transport.passengers.list'),
					fontIcon: 'fa-duotone fa-person-seat-reclined',
					active: route().current('transport.passengers.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.locations'),
					route: route('geo.locations.list'),
					fontIcon: 'fa-duotone fa-map-location-dot',
					active: route().current('geo.locations.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.imports'),
					route: route('imports.view'),
					fontIcon: 'fa-duotone fa-upload',
					active: route().current('imports.*'),
					hasPermissions: true
				},
				{
					heading: this.$t('nav.settings'),
					route: route('settings.list.pages'),
					fontIcon: 'fa-duotone fa-sliders',
					active: route().current('settings.*'),
					// hasPermissions: this.hasPermissions('settings.edit')
					hasPermissions: true
				}
			]
		};
	},
	computed: {
		mainMenu() {
			return this.menu.filter((item) => item.show !== false);
		}
	},
	methods: {
		handleNormalLinkClick() {
			localStorage.removeItem('activeSection');
		},
		showWarehouseTab() {
			return this.hasAnyPermission([
				'depots.view',
				'stocks.view',
				'lots.view',
				'transfers.view',
				'inventories.view'
			]);
		},
		showSalesTab() {
			return this.hasAnyPermission([
				'customers.view',
				'orders.view',
				'shipments.view',
				'discounts.view',
				'price_lists.view',
				'invoices.view',
				'credit_notes.view',
				'payments.view',
				'deposits.view',
				'targets.view',
				'dispatches.view',
				'sessions.view',
				'sample_requests.view'
			]);
		},
		showCatalogTab() {
			return this.hasAnyPermission(['products.view', 'categories.view']);
		}
	}
};
</script>

<style>
.app-sidebar-wrapper {
	max-height: calc(100vh - 120px);
	overflow-y: auto;
}

.app-sidebar-wrapper::-webkit-scrollbar {
	display: none;
}

.app-sidebar-wrapper::-webkit-scrollbar-thumb {
	background-color: var(--kt-app-sidebar-light-scrollbar-color);
}

.app-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
	background-color: var(--kt-app-sidebar-light-scrollbar-color-hover);
}
</style>
