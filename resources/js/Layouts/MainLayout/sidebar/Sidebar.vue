<template>
	<div
		class="app-sidebar flex-column h-100 justify-content-start"
		:class="{ 'drawer drawer-start drawer-on': showSidebar }">
		<div
			class="app-sidebar-logo d-flex justify-content-around border-bottom-0 border-end border-gray-300">
			<SidebarLogo class="p-6 p-md-16" url="/assets/images/logos/logo.svg"></SidebarLogo>

			<div
				v-if="showSidebar"
				@click="$emit('hideSidebar')"
				class="hide-sidebar btn btn-icon btn-active-color-primary w-35px h-35px ms-auto my-auto">
				<span class="svg-icon svg-icon-1">
					<i class="fa-duotone fa-list fs-1"></i>
				</span>
			</div>
		</div>
		<SidebarMenu />
		<CompanySelector />
	</div>
</template>

<script>
import SidebarLogo from '@/Layouts/MainLayout/sidebar/SidebarLogo.vue';
import SidebarMenu from '@/Layouts/MainLayout/sidebar/SidebarMenu.vue';
import { usePage } from '@inertiajs/vue3';
import CompanySelector from '@/Components/General/CompanySelector.vue';

export default {
	components: {
		SidebarLogo,
		SidebarMenu,
		CompanySelector
	},
	props: {
		showSidebar: {
			type: Boolean
		}
	},
	emits: ['hideSidebar'],
	data() {
		return {
			company: usePage().props?.company
		};
	}
};
</script>

<style scoped>
@media (min-width: 992px) {
	.hide-sidebar {
		display: none !important;
	}
}
</style>
