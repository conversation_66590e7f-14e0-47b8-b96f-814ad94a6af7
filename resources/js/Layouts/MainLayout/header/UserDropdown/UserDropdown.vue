<template>
	<div v-click-away="handleClickAway">
		<div class="cursor-pointer symbol symbol-30px symbol-md-40px" @click="toggle">
			<img :src="authUser?.image || 'https://api.dicebear.com/6.x/bottts/png'" alt="user" />
		</div>
		<div
			v-if="showMenu"
			class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px show showMenu">
			<div class="menu-item px-5">
				<UserOverview :employee="authUser" />
			</div>

			<div class="separator my-2"></div>

			<div class="menu-item px-5">
				<LanguageDropdown :locale="authUser.locale" />
			</div>

			<div class="menu-item px-5">
				<Link
					:href="route('settings.personal.view')"
					class="menu-link px-5 text-capitalize"
					type="button">
					{{ $t('auth.user.personal_settings') }}
				</Link>
			</div>

			<div class="menu-item px-5" v-if="authUser.type === 'super_admin'">
				<a class="menu-link px-5 text-capitalize" href="/admin">{{
					$t('general.enums.admin')
				}}</a>
			</div>

			<div class="menu-item px-5">
				<Link
					:href="route('logout')"
					method="post"
					class="menu-link px-5 text-capitalize border-0 bg-white bg-hover-secondary"
					type="button"
					as="span">
					{{ $t('auth.sign_out') }}
				</Link>
			</div>
		</div>
	</div>
</template>

<script>
import { Link, usePage } from '@inertiajs/vue3';
import LanguageDropdown from '@/Layouts/MainLayout/header/UserDropdown/LanguageDropdown.vue';
import UserOverview from '@/Layouts/MainLayout/header/UserDropdown/UserOverview.vue';
import { directive } from 'vue3-click-away';

export default {
	name: 'DropdownMenu',
	components: {
		Link,
		LanguageDropdown,
		UserOverview
	},
	directives: {
		ClickAway: directive
	},
	data() {
		return {
			showMenu: false,
			authUser: usePage().props?.user
		};
	},
	methods: {
		toggle() {
			this.showMenu = !this.showMenu;
		},
		handleClickAway() {
			this.showMenu = false;
		}
	}
};
</script>
