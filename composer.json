{"name": "laravel/laravel", "type": "project", "description": "Yosr dashboard project.", "keywords": ["dashboard", "yosr"], "license": "MIT", "require": {"php": "^8.4", "ably/laravel-broadcaster": "^1.0", "bensampo/laravel-enum": "^6.3", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.8", "inertiajs/inertia-laravel": "^2.0", "itsgoingd/clockwork": "^5.2", "knuckleswtf/scribe": "^5.1", "laravel/framework": "^12.0", "laravel/octane": "^2.9", "laravel/sanctum": "^4.0", "laravel/slack-notification-channel": "*", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "mailersend/laravel-driver": "^2.7", "mixpanel/mixpanel-php": "^2.11", "predis/predis": "^2.0", "sentry/sentry-laravel": "^4.10", "spatie/laravel-medialibrary": "^11.4", "stechstudio/filament-impersonate": "^3.9", "symfony/var-exporter": "^6.3", "tightenco/ziggy": "^1.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.9.1", "icanhazstring/composer-unused": "^0.9.2", "laravel-lang/common": "^6.3", "laravel/pint": "^1.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "pelmered/fake-car": "^2.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "prefer-stable": true}