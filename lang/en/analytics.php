<?php

declare(strict_types=1);

return [
    'statistics' => 'Statistics',
    'start_on_time' => 'Start on time',
    'end_on_time' => 'End on time',
    'rides' => [
        'title' => 'Rides',
        'description' => 'Count of all rides over a period',
    ],
    'drivers' => [
        'title' => 'Drivers',
        'description' => 'Count of all rides over a period',
    ],
    'vehicles' => [
        'title' => 'Vehicles',
        'description' => 'Count of all rides over a period',
    ],
    'passengers' => [
        'title' => 'Passengers',
        'description' => 'Count of all passengers over a period',
    ],
    'charts' => [
        'total_distance_traveled' => [
            'title' => 'Total Distance Traveled',
            'description' => 'Total Distance Traveled',
        ],
        'drivers_status' => [
            'title' => 'Drivers Status',
            'description' => 'Total number of drivers by status.',
        ],
        'assistant_demographics' => [
            'title' => 'Assistant Demographics',
            'description' => 'Gender, and other demographics...',
        ],
        'driver_demographics' => [
            'title' => 'Driver Demographics',
            'description' => 'Gender, and other demographics...',
        ],
        'vehicle_status' => [
            'title' => 'Vehicle Status',
            'description' => 'Total number of active drivers.',
        ],
        'vehicle_type' => [
            'title' => 'Vehicle Type',
            'description' => 'Total number of active drivers.',
        ],
        'average_driver_speed' => [
            'title' => 'Average Driver Speed',
            'description' => 'Average speed of drivers.',
        ],
        'average_vehicle_usage' => [
            'title' => 'Average Vehicle Usage',
            'description' => 'Average usage of vehicles.',
        ],
        'ride_counts_by_vehicle' => [
            'title' => 'Ride Counts by Vehicle',
            'description' => 'Number of rides per vehicle over time.',
        ],
        'ride_counts_by_driver' => [
            'title' => 'Ride Counts by Driver',
            'description' => 'Number of rides per driver over time.',
        ],
        'total_number_of_rides' => [
            'title' => 'Total Number of Rides',
            'description' => 'Count of all rides over a period',
        ],
        'ride_duration' => [
            'title' => 'Ride Duration',
            'description' => 'Average, min, max ride duration',
        ],
        'ride_distance' => [
            'title' => 'Ride Distance',
            'description' => 'Average, min, max distance covered',
        ],
        'ride_status' => [
            'title' => 'Ride Status',
            'description' => 'Distribution of ride statuses',
        ],
        'ride_start_and_end_times' => [
            'title' => 'Ride Start and End Times',
            'description' => 'Patterns and peak times for ride start and end times',
        ],
        'total_number_of_routes' => [
            'title' => 'Total Number of Routes',
            'description' => 'Count of all active routes',
        ],
        'ridership_per_route' => [
            'title' => 'Ridership per Route',
            'description' => 'Number of passengers per route',
        ],
        'on_time_performance' => [
            'title' => 'On-time Performance',
            'description' => 'Percentage of on-time arrivals/departures',
        ],
        'total_number_of_stations' => [
            'title' => 'Total Number of Stations',
            'description' => 'Count of all stations',
        ],
        'stations_locations' => [
            'title' => 'Stations Locations',
            'description' => 'Geographical distribution of stations',
        ],
        'total_number_of_passengers' => [
            'title' => 'Total Number of Passengers',
            'description' => 'Count of all passengers over a period',
        ],
        'passenger_demographics' => [
            'title' => 'Passenger Demographics',
            'description' => 'Gender, and other demographic info',
        ],
        'responsible_demographics' => [
            'title' => 'Responsible Demographics',
            'description' => 'Gender, and other demographic info',
        ],
        'ride_status_count' => [
            'title' => 'Ride Count by Status',
            'description' => 'Distribution of Rides based on Status.',
        ],
        'total_driver_count' => [
            'title' => 'Total Number of Drivers',
        ],
        'total_assistant_count' => [
            'title' => 'Total Number of Assistants',
        ],
        'total_vehicle_count' => [
            'title' => 'Total Number of Vehicles',
        ],
    ],
    'menu' => [
        'transport_analytics' => 'Transport Analytics',
        'fleet_analytics' => 'Fleet Analytics',
    ],
];
