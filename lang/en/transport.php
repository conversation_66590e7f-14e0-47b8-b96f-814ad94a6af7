<?php

return [
    'name' => 'transport',
    'passenger' => [
        'details' => 'passenger details',
        'name' => 'passenger',
        'names' => 'passengers',
        'new' => 'new passenger',
        'add' => 'add passengers',
        'map' => 'Passengers Map',
        'selected' => '[0,1] :count passenger selected|[2,*] :count passengers selected',
        'no_passengers_found' => 'No Passengers Found',
        'no_passengers_match_filters' => 'No passengers match your current filters',
    ],
    'attendee' => [
        'details' => 'attendee details',
        'name' => 'attendee',
        'names' => 'attendees',
    ],
    'group' => [
        'name' => 'group',
        'names' => 'groups',
        'new' => 'new group',
    ],
    'region' => [
        'new' => 'new region',
        'name' => 'region',
        'names' => 'regions',
    ],
    'responsible' => [
        'details' => 'responsible details',
        'name' => 'responsible',
        'names' => 'responsibles',
        'new' => 'new responsible',
    ],
    'ride' => [
        'details' => 'ride details',
        'name' => 'ride',
        'names' => 'rides',
        'modes' => [
            'pickup' => 'pickup',
            'drop_off' => 'drop off',
        ],
        'start_of_ride' => 'Start of the ride',
        'end_of_ride' => 'End of the ride',
        'ongoing' => 'Ongoing',
        'next_5_days' => 'Next 5 days',
    ],
    'rides' => [
        'labels' => [
            'ride_id' => 'ride ID',
            'distance' => 'Distance',
            'duration' => 'Duration',
            'minutes' => 'minutes',
            'start_time' => 'Start time',
            'average_speed' => 'Average speed',
            'kmh' => 'km/h',
        ],
    ],
    'route' => [
        'destination' => [
            'name' => 'destination',
        ],
        'details' => 'route details',
        'labels' => [
            'static' => 'disable sequence optimization',
            'starting_location' => 'starting location',
            'destination' => 'destination location',
            'duration' => 'duration',
            'distance' => 'distance',
            'km' => 'km',
        ],
        'select_location' => 'select location',
        'select_vehicles' => 'select vehicles',
        'select_drivers' => 'select drivers',
        'start_of_route' => 'Start of the route',
        'end_of_route' => 'End of the route',
        'name' => 'route',
        'names' => 'routes',
        'new' => 'new route',
        'no_routes_found' => 'No Routes Found',
        'no_routes_match_filters' => 'No routes match your current filters',
        'all_routes_hidden' => 'All Routes Hidden',
        'click_eye_to_show' => 'Click the eye icon on a route card to make it visible on the map',
        'show_all_routes' => 'Show All Routes',
        'optimize' => 'optimize',
        'origin' => [
            'name' => 'origin',
        ],
    ],
    'seat' => [
        'name' => 'seat',
        'names' => 'seats',
    ],
    'settings' => [
        'pages' => [
            'regions' => 'regions',
            'vehicles' => 'vehicles',
        ],
    ],
    'station' => [
        'add' => 'add station',
        'details' => 'station details',
        'name' => 'station',
        'names' => 'stations',
    ],
    'time_info' => [
        'arrived_and_departed' => 'Arrived at :arrived_at, departed at :departed_at',
        'arrived_waiting_departure' => 'Arrived at :arrived_at, awaiting departure',
        'started_waiting_arrival' => 'Started at :started_at, awaiting arrival',
        'started_and_arrived' => 'Started at :started_at, arrived at :arrived_at',
        'expected_arrival' => 'Expected to arrive at :scheduled_at',
        'waiting_schedule' => 'Waiting for arrival schedule',
    ],
];
