<?php

return [
    'attendance' => [
        'name' => 'attendance',
        'names' => 'attendances',
        'details' => 'attendance details',
    ],
    'absence' => [
        'name' => 'absence',
        'names' => 'absences',
        'details' => 'absence details',
        'new' => 'new absence',
        'for' => 'applies to',
        'reason' => 'reason',
    ],
    'create' => 'create plan',
    'edit' => 'edit plan',
    'messages' => [
        'no_order_found' => 'no order found',
    ],
    'name' => 'plan',
    'names' => 'plans',
    'route_plan' => [
        'name' => 'Route Planning',
        'total_capacity' => 'Total Capacity',
        'all_items_page_selected' => 'All items on this page are selected',
        'all_passengers_selected' => 'All passengers in your organization are selected',
        'unselect_all_passengers' => 'Unselect all passengers',
        'select_up_to' => '[0,1] You can select up to :count passenger|[2,*] You can select up to :count passengers',
        'visible_records' => 'showing :from to :to of :total records',
        'configure_schedules' => 'Configure schedules',
        'steps' => [
            'step_1' => 'Select Drivers and Vehicles',
            'step_2' => 'Passengers',
            'step_3' => 'Confirm Routes',
            'step_4' => 'Configure Schedules',
        ],
    ],
    'schedule' => [
        'create' => 'create schedule',
        'frequency' => [
            'name' => 'schedule frequency',
            'daily' => 'daily',
            'monthly' => 'monthly',
            'name' => 'frequency',
            'onetime' => 'one time',
            'select_days' => 'select days',
            'weekly' => 'weekly',
        ],
        'name' => 'schedule',
        'names' => 'schedules',
        'days' => [
            'abbreviation' => [
                'monday' => 'MO',
                'tuesday' => 'TU',
                'wednesday' => 'WE',
                'thursday' => 'TH',
                'friday' => 'FR',
                'saturday' => 'SA',
                'sunday' => 'SU',
            ],
            'full' => [
                'monday' => 'Monday',
                'tuesday' => 'Tuesday',
                'wednesday' => 'Wednesday',
                'thursday' => 'Thursday',
                'friday' => 'Friday',
                'saturday' => 'Saturday',
                'sunday' => 'Sunday',
            ],
        ],
    ],
];
