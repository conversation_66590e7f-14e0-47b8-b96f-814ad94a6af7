<?php

// ENGLISH
return [
    'transport' => [
        'ride' => [
            'started' => [
                'responsible' => [
                    'title' => 'Ride in Progress',
                    'body' => ':ride_mode for :names has begun. Monitor journey status.',
                ],
                'passenger' => [
                    'title' => 'Your Ride Has Started',
                    'body' => 'Your :ride_mode is now in progress. Please be ready at your pickup location.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => 'Ride Cancelled',
                    'body' => ':ride_mode for :names has been cancelled. Please arrange alternative transport.',
                ],
                'passenger' => [
                    'title' => 'Ride Cancelled',
                    'body' => 'Your :ride_mode has been cancelled. Please contact support for assistance.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Journey Complete',
                    'body' => ':ride_mode for :names has reached its destination successfully.',
                ],
                'passenger' => [
                    'title' => 'Destination Reached',
                    'body' => 'You have safely arrived at your destination. Thank you for traveling with us.',
                ],
            ],
            'upcoming' => [
                'responsible' => [
                    'title' => 'Scheduled Ride Alert',
                    'body' => ':ride_mode for :names is scheduled at :time. Ensure vehicle readiness.',
                ],
                'passenger' => [
                    'title' => 'Upcoming Trip Reminder',
                    'body' => 'Your :ride_mode is scheduled at :time. Please be ready 10 minutes early.',
                ],
            ],
        ],
        'stop' => [
            'scheduled' => [
                'responsible' => [
                    'title' => ':ride_mode Departure Scheduled',
                    'body' => ':ride_mode departs at :time for :names. Track live location via app.',
                ],
                'passenger' => [
                    'title' => 'Bus Departure Time',
                    'body' => 'Your :ride_mode departs at :time. Track the bus location in real-time.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Bus at Stop',
                    'body' => 'Bus has arrived at the designated stop for :ride_mode (:names).',
                ],
                'passenger' => [
                    'title' => 'Bus Has Arrived',
                    'body' => 'Your bus is now at the stop. Please board promptly.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => ':ride_mode Service Cancelled',
                    'body' => ':ride_mode service has been cancelled for :names. Notify passengers immediately.',
                ],
                'passenger' => [
                    'title' => 'Service Cancelled',
                    'body' => 'Your :ride_mode has been cancelled. Alternative arrangements will be communicated shortly.',
                ],
            ],
            'departed' => [
                'responsible' => [
                    'title' => 'Bus Departed',
                    'body' => 'Bus has left the stop for :ride_mode (:names). Next stop en route.',
                ],
                'passenger' => [
                    'title' => 'Bus Has Departed',
                    'body' => 'The bus has left your stop and is proceeding to the next destination.',
                ],
            ],
            'delayed' => [
                'responsible' => [
                    'title' => ':ride_mode Running Late',
                    'body' => 'Bus delayed for :ride_mode (:names). New estimated arrival: :time.',
                ],
                'passenger' => [
                    'title' => 'Service Delay Notice',
                    'body' => 'Your bus is running late. Updated arrival time: approximately :time.',
                ],
            ],
            'skipped' => [
                'responsible' => [
                    'title' => 'Stop Bypassed',
                    'body' => ':ride_mode stop was skipped for :names. Check routing protocols.',
                ],
                'passenger' => [
                    'title' => 'Stop Missed',
                    'body' => 'Your stop was bypassed. Please check updated itinerary or contact support.',
                ],
            ],
            'ignored' => [
                'responsible' => [
                    'title' => 'Stop Not Serviced',
                    'body' => ':ride_mode stop was not serviced for :names. Investigation required.',
                ],
                'passenger' => [
                    'title' => 'Service Unavailable',
                    'body' => 'Service at your stop was unavailable. Please review updated schedule.',
                ],
            ],
            'no_show' => [
                'responsible' => [
                    'title' => 'Passenger No-Show',
                    'body' => ':names failed to board :ride_mode at scheduled stop. Follow up required.',
                ],
                'passenger' => [
                    'title' => 'Missed Boarding',
                    'body' => 'You missed your :ride_mode departure. Contact support for rebooking assistance.',
                ],
            ],
        ],
    ],
    'incident' => [
        'mechanical_issue' => [
            'title' => 'Vehicle Malfunction Alert',
            'body' => 'Mechanical issue reported at :time. Immediate technical assessment required.',
        ],
        'accident' => [
            'title' => 'Accident Report',
            'body' => 'Traffic accident occurred at :time. Emergency protocol activated - assess and respond.',
        ],
        'health_emergency' => [
            'title' => 'Medical Emergency',
            'body' => 'Health emergency reported at :time. Emergency services notified - provide immediate assistance.',
        ],
        'behavioral_issue' => [
            'title' => 'Passenger Conduct Issue',
            'body' => 'Behavioral incident reported at :time. Ensure passenger safety and de-escalate situation.',
        ],
        'traffic_delay' => [
            'title' => 'Traffic Congestion Alert',
            'body' => 'Significant traffic delays affecting services at :time. Consider route alternatives.',
        ],
        'weather_condition' => [
            'title' => 'Weather Advisory',
            'body' => 'Adverse weather conditions impacting operations at :time. Implement safety protocols.',
        ],
        'road_blockage' => [
            'title' => 'Route Obstruction',
            'body' => 'Road blockage reported at :time. Immediate route diversion required.',
        ],
        'missed_station' => [
            'title' => 'Station Bypass Alert',
            'body' => 'Scheduled station missed at :time. Review route adherence and notify affected passengers.',
        ],
        'other' => [
            'title' => 'Incident Report',
            'body' => 'Operational incident occurred at :time. Investigation and response required.',
        ],
    ],
    'auth' => [
        'otp' => [
            'title' => 'Authentication Code',
            'body' => 'Your verification code is :otp. Valid for 10 minutes.',
        ],
    ],
];
