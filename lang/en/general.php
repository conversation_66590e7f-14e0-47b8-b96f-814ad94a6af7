<?php

return [
    'accept' => 'accept',
    'add' => 'Add',
    'add_assignees' => 'Add Assignees',
    'add_filter' => 'Add Filter',
    'add_members' => 'Add Members',
    'all' => 'All',
    'allow' => 'Allow',
    'allowed_file_types' => 'Allowed file types: :types.',
    'and' => 'And',
    'apply' => 'apply',
    'approve' => 'Approve',
    'arrival_time' => 'end time',
    'arrive_at' => 'arrive at',
    'arrived_at' => 'arrived at',
    'assignees' => 'Assignees',
    'back' => 'Back',
    'birthdate' => 'Birthdate',
    'cancel' => 'Cancel',
    'change_made_by' => 'Changes made by',
    'changes' => 'changes',
    'choose_date' => 'Choose a date',
    'choose_members' => 'Choose members',
    'clear_filters' => 'Clear filters',
    'comment' => 'Comment',
    'comment_optional' => 'Comment (optional)',
    'confirm' => 'Confirm',
    'confirmed' => 'Confirmed',
    'create' => 'Create',
    'create_request' => 'Create Request',
    'reference' => 'reference',
    'capacity' => 'capacity',
    'country' => 'Country',
    'created_at' => 'created at',
    'date_added' => 'date added',
    'date_range' => 'date range',
    'default_vehicle' => 'default vehicle',
    'description' => 'Description',
    'email' => 'e-mail',
    'end_date' => 'end date',
    'end_location' => 'end location',
    'first_name' => 'first name',
    'gender' => 'gendre',
    'id' => 'id',
    'image' => 'image',
    'include_in' => 'Include In',
    'last_name' => 'last name',
    'loading' => 'loading',
    'make' => 'make',
    'message' => 'Message',
    'mode' => 'mode',
    'name' => 'Name',
    'next' => 'next',
    'note' => 'note',
    'phone' => 'phone',
    'plate' => 'plate',
    'preset' => 'Preset',
    'rating' => 'rating',
    'reporter' => 'reporter',
    'reviewer' => 'reviewer',
    'see_all' => 'See all',
    'select_all' => 'Select all',
    'select_all_records' => 'Select all records',
    'selected' => 'selected',
    'starts' => 'starts',
    'start_at_time' => 'start at time',
    'start_date' => 'start date',
    'start_date_end_date' => 'start and end dates',
    'start_location' => 'start location',
    'started_at' => 'started at',
    'status' => 'status',
    'subject' => 'Subject',
    'submit' => 'submit',
    'timezone' => 'timezone',
    'title' => 'Title',
    'type' => 'Type',
    'industry' => 'industry',
    'emergency_contact' => 'emergency contact',
    'start_time' => 'start time',
    'date' => 'Date',
    'day' => [
        'name' => 'Days',
        'names' => 'Day',
    ],
    'decline' => 'Decline',
    'delete' => 'Delete',
    'details' => 'details',
    'discard' => 'Discard',
    'done' => 'Done',
    'edit' => 'Edit',
    'ends' => 'ends',
    'enums' => [
        'absent' => 'absent',
        'active' => 'active',
        'admin' => 'admin',
        'arrived' => 'arrived',
        'both' => 'Transport / Institute',
        'canceled' => 'canceled',
        'completed' => 'completed',
        'departed' => 'departed',
        'drop_off' => 'drop-off',
        'emergency' => 'Emergency',
        'expected' => 'expected',
        'female' => 'female',
        'inactive' => 'inactive',
        'institute' => 'Institute',
        'male' => 'male',
        'maintenance' => 'maintenance',
        'no_show' => 'no show',
        'pickup' => 'pick-up',
        'present' => 'present',
        'scheduled' => 'scheduled',
        'sickness' => 'Sickness',
        'skipped' => 'skipped',
        'stalled' => 'stalled',
        'started' => 'started',
        'static' => 'disable sequence optimization',
        'car' => 'car',
        'van' => 'van',
        'school_bus' => 'school bus',
        'bus' => 'bus',
        'ongoing' => 'ongoing',
        'waiting' => 'waiting',
        'passengers' => 'passengers',
        'personal' => 'Personal',
        'responsibles' => 'responsibles',
        'education' => 'Education',
        'corporate' => 'Corporate',
        'tourism' => 'Tourism',
        'transport' => 'Transport',
        'transport,institute' => 'Both',
        'travel' => 'Travel',
        'public_transportation' => 'Public Transportation',
        'healthcare' => 'Healthcare',
        'event_management' => 'Event Management',
        'manufacturing' => 'Manufacturing',
        'logistics_and_delivery' => 'Logistics and Delivery',
        'non_profit' => 'Non-Profit',
        'government' => 'Government',
        'retirement_communities' => 'Retirement Communities',
        'retail' => 'Retail',
        'mechanical_issue' => 'Mechanical Issue',
        'accident' => 'Accident',
        'health_emergency' => 'Health Emergency',
        'behavioral_issue' => 'Behavioral Issue',
        'traffic_delay' => 'Traffic Delay',
        'weather_condition' => 'Weather Condition',
        'road_blockage' => 'Road Blockage',
        'missed_station' => 'Missed Station',
        'other' => 'Other',
    ],
    'filter' => 'filter',
    'filter_options' => 'filter options',
    'filters' => 'Filter',
    'for' => 'for',
    'from' => 'From',
    'ignore' => 'Ignore',
    'in_review' => 'In Review',
    'info' => 'Info',
    'insert' => 'Insert',
    'interval_options' => [
        'half_yearly' => 'Half-Yearly',
        'monthly' => 'Monthly',
        'quarterly' => 'Quarterly',
        'yearly' => 'Yearly',
    ],
    'language' => 'Language',
    'map' => 'map',
    'members' => 'Members',
    'messages' => [
        'assignee_removed_successfully' => 'Assignee has been removed successfully',
        'confirm_deletion' => 'Please confirm deletion.',
        'confirm_leaving' => 'You have unsaved changes. Do you really want to leave?',
        'construction' => [
            'action' => 'Return to home',
            'description' => 'We\'re still working on this section of our app but we\'ll be back soon with something awesome!',
            'title' => 'Under Construction',
        ],
        'employee_not_set' => 'Employees Not Set',
        'employees_assigned_successfully' => 'Employees assigned successfully',
        'help' => [
            'action' => 'Contact Yosr Team',
            'description' => 'Do you have any problem while using Yosr portal?',
            'title' => 'Need Help?',
        ],
        'no_assigned_assignees' => 'No assigned assignees',
        'no_assigned_members' => 'No assigned members',
        'no_birthdays_today' => 'No birthdays today',
        'no_passengers_found' => 'No passengers found',
        'no_results_found' => 'No result found',
        'no_upcoming_rides' => 'No Up-coming Rides',
        'no_ongoing_rides' => 'No Ongoing Rides',
        'not_enough_report_data' => 'Not enough data to display chart',
        'not_set' => 'not set',
        'send_us_email' => 'Send Us Email',
        'type_to_search' => 'Type to search',
        'no_locations_yet' => 'No locations yet',
        'please_select_vehicles' => 'Please select vehicles',
        'please_select_drivers' => 'Please select drivers',
        'please_select_passengers' => 'Please select passengers',
        'no_groups' => 'No groups',
        'no_regions' => 'No regions',
        'no_responsibles' => 'No responsibles',
        'select_a_ride' => 'Select a Ride',
        'no_rides' => 'No Rides',
    ],
    'models' => [
        'image_upload' => [
            'set_thumbnail_message' => 'Set the :placeholder thumbnail image. Only *.png, *.jpg, and *.jpeg image files are accepted.',
        ],
        'import' => 'Import',
        'user' => 'User',
        'assistant' => 'Assistant',
        'country' => 'Country',
        'currency' => 'Currency',
        'company' => 'Company',
        'location' => 'Location',
        'media' => 'Media',
        'route' => 'Route',
        'station' => 'Station',
        'region' => 'Region',
        'vehicle' => 'Vehicle',
        'passenger' => 'Passenger',
        'driver' => 'Driver',
        'ride' => 'Ride',
        'plan' => 'Plan',
        'schedule' => 'Schedule',
        'responsible' => 'Responsible',
        'group' => 'Group',
    ],
    'month' => 'Month',
    'my_home' => 'My Home',
    'new' => 'New',
    'no' => 'No',
    'none' => 'None',
    'not_set' => 'Not Set',
    'now' => 'Now',
    'options' => [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
    ],
    'org_chart' => 'Org Chart',
    'overview' => 'overview',
    'page_titles' => [
        'profile' => ':name - Profile',
        'settings' => ':domain - Settings',
    ],
    'password' => 'Password',
    'placeholders' => [
        'choose_assignees' => 'Choose assignees',
        'choose_date' => 'Choose a date',
        'choose_members' => 'Choose members',
        'choose_time' => 'choose time',
        'list_is_empty' => 'List is empty.',
        'no_elements_found' => 'No elements found. Consider changing the search query.',
        'optional' => 'Optional',
        'search_assignees' => 'Search assignees',
        'search_members' => 'Search members',
        'select_option' => 'Select an option',
    ],
    'preset_dates' => [
        'today' => 'Today',
        'yesterday' => 'Yesterday',
        'last_7_days' => 'Last 7 days',
        'last_30_days' => 'Last 30 days',
        'this_month' => 'This month',
        'last_month' => 'Last month',
        'this_year' => 'This year',
    ],
    'profile_not_found' => 'Profile not found.',
    'projects' => 'Projects',
    'request' => [
        'name' => 'Request',
        'names' => 'Requests',
    ],
    'request_changes' => 'Request Changes',
    'reset' => 'reset',
    'responder_comment' => 'Responder Comment',
    'save' => 'Save',
    'saving' => 'Saving',
    'save_changes' => 'Save Changes',
    'saved' => 'Saved',
    'search' => 'Search',
    'send' => 'Send',
    'settings' => [
        'labels' => [
            'company' => 'company',
            'transport' => 'transport',
            'preferences' => 'preferences',
        ],
        'pages' => [
            'company' => 'company',
            'drivers' => 'drivers',
            'regions' => 'regions',
            'users' => 'users',
            'vehicles' => 'vehicles',
            'passengers' => 'passengers',
        ],
    ],
    'since' => 'Since',
    'soon' => 'Soon',
    'submit_request' => 'Submit Request',
    'terms_and_conditions' => 'Terms and conditions',
    'timeline' => [
        'name' => 'time line',
    ],
    'to' => 'To',
    'tomorrow' => 'Tomorrow',
    'until' => 'Until',
    'units' => [
        'kilometers' => 'km',
        'meters' => 'm',
        'hours' => 'hours',
        'minutes' => 'minutes',
        'seconds' => 'seconds',
    ],
    'unsaved_changes' => 'Unsaved changes detected. Click \'Save Changes\' to update.',
    'view' => 'view',
    'view_all' => 'View All',
    'view_details' => 'View Details',
    'week_days' => [
        'friday' => 'Friday',
        'monday' => 'Monday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday',
        'thursday' => 'Thursday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
    ],
    'weekends' => 'Weekends',
    'where' => 'Where',
    'yes' => 'Yes',
    'upload' => 'upload',
];
