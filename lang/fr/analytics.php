<?php

declare(strict_types=1);

return [
    'statistics' => 'Statistiques',
    'start_on_time' => 'Commencer à l\'heure',
    'end_on_time' => 'Terminer à l\'heure',
    'rides' => [
        'title' => 'Les Tours',
        'description' => 'Nombre total des tours sur une période',
    ],
    'drivers' => [
        'title' => 'Conducteurs',
        'description' => 'Nombre total des conducteurs sur une période',
    ],
    'vehicles' => [
        'title' => 'Véhicules',
        'description' => 'Nombre total des véhicules sur une période',
    ],
    'passengers' => [
        'title' => 'Passagers',
        'description' => 'Nombre total de passagers sur une période',
    ],
    'charts' => [
        'total_distance_traveled' => [
            'title' => 'Distance Totale Parcourue',
            'description' => 'Distance totale parcourue',
        ],
        'drivers_status' => [
            'title' => 'Statut des Conducteurs',
            'description' => 'Nombre total de conducteurs par statut.',
        ],
        'assistant_demographics' => [
            'title' => 'Démographie des Assistants',
            'description' => 'Sexe et autres données démographiques...',
        ],
        'driver_demographics' => [
            'title' => 'Démographie des Conducteurs',
            'description' => 'Sexe et autres données démographiques...',
        ],
        'vehicle_status' => [
            'title' => 'Statut des Véhicules',
            'description' => 'Nombre total de conducteurs actifs.',
        ],
        'vehicle_type' => [
            'title' => 'Type de Véhicule',
            'description' => 'Nombre total de conducteurs actifs.',
        ],
        'average_driver_speed' => [
            'title' => 'Vitesse Moyenne des Conducteurs',
            'description' => 'Vitesse moyenne des conducteurs.',
        ],
        'average_vehicle_usage' => [
            'title' => 'Utilisation Moyenne des Véhicules',
            'description' => 'Utilisation moyenne des véhicules.',
        ],
        'ride_counts_by_vehicle' => [
            'title' => 'Nombre des tours par Véhicule',
            'description' => 'Nombre des tours par véhicule sur une période.',
        ],
        'ride_counts_by_driver' => [
            'title' => 'Nombre des tours par Conducteur',
            'description' => 'Nombre des tours par conducteur sur une période.',
        ],
        'total_number_of_rides' => [
            'title' => 'Nombre Total des Tours',
            'description' => 'Nombre total des Tours sur une période',
        ],
        'ride_duration' => [
            'title' => 'Durée des Tours',
            'description' => 'Durée moyenne, minimale, maximale des tours',
        ],
        'ride_distance' => [
            'title' => 'Distance des Tours',
            'description' => 'Distance moyenne, minimale, maximale parcourue',
        ],
        'ride_status' => [
            'title' => 'Statut des Tours',
            'description' => 'Répartition des statuts des tours',
        ],
        'ride_start_and_end_times' => [
            'title' => 'Heures de Départ et d\'Arrivée des Tours',
            'description' => 'Modèles et heures de pointe des départs et des arrivées des tours',
        ],
        'total_number_of_routes' => [
            'title' => 'Nombre Total de Routes',
            'description' => 'Nombre total de routes actives',
        ],
        'ridership_per_route' => [
            'title' => 'Passagers par Route',
            'description' => 'Nombre de passagers par route',
        ],
        'on_time_performance' => [
            'title' => 'Performance de Ponctualité',
            'description' => 'Pourcentage d\'arrivées/départs à l\'heure',
        ],
        'total_number_of_stations' => [
            'title' => 'Nombre Total de Stations',
            'description' => 'Nombre total de stations',
        ],
        'stations_locations' => [
            'title' => 'Localisation des Stations',
            'description' => 'Répartition géographique des stations',
        ],
        'total_number_of_passengers' => [
            'title' => 'Nombre Total de Passagers',
            'description' => 'Nombre total de passagers sur une période',
        ],
        'passenger_demographics' => [
            'title' => 'Démographie des Passagers',
            'description' => 'Sexe et autres informations démographiques',
        ],
        'responsible_demographics' => [
            'title' => 'Démographie des Responsables',
            'description' => 'Sexe et autres informations démographiques',
        ],
        'ride_status_count' => [
            'title' => 'Nombre de Courses par Statut',
            'description' => 'Répartition des Courses basée sur le Statut.',
        ],
        'total_driver_count' => [
            'title' => 'Nombre Total de Chauffeurs',
        ],
        'total_assistant_count' => [
            'title' => 'Nombre Total d\'Assistants',
        ],
        'total_vehicle_count' => [
            'title' => 'Nombre Total de Véhicules',
        ],
    ],
    'menu' => [
        'transport_analytics' => 'Analytique des Transports',
        'fleet_analytics' => 'Analytique de la Flotte',
    ],
];
