<?php

return [
    'transport' => [
        'ride' => [
            'started' => [
                'responsible' => [
                    'title' => 'Course en Cours',
                    'body' => ':ride_mode pour :names a commencé. Surveillez le statut du trajet.',
                ],
                'passenger' => [
                    'title' => 'Votre Course a Commencé',
                    'body' => 'Votre :ride_mode est maintenant en cours. Veuillez être prêt à votre point de prise en charge.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => 'Course Annulée',
                    'body' => ':ride_mode pour :names a été annulé. Veuillez organiser un transport alternatif.',
                ],
                'passenger' => [
                    'title' => 'Course Annulée',
                    'body' => 'Votre :ride_mode a été annulé. Veuillez contacter le support pour assistance.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Trajet Terminé',
                    'body' => ':ride_mode pour :names a atteint sa destination avec succès.',
                ],
                'passenger' => [
                    'title' => 'Destination Atteinte',
                    'body' => 'Vous êtes arrivé en sécurité à votre destination. Merci de voyager avec nous.',
                ],
            ],
            'upcoming' => [
                'responsible' => [
                    'title' => 'Alerte Course Programmée',
                    'body' => ':ride_mode pour :names est programmé à :time. Assurez la disponibilité du véhicule.',
                ],
                'passenger' => [
                    'title' => 'Rappel Voyage à Venir',
                    'body' => 'Votre :ride_mode est programmé à :time. Veuillez être prêt 10 minutes à l\'avance.',
                ],
            ],
        ],
        'stop' => [
            'scheduled' => [
                'responsible' => [
                    'title' => 'Départ :ride_mode Programmé',
                    'body' => ':ride_mode part à :time pour :names. Suivez la localisation en temps réel via l\'app.',
                ],
                'passenger' => [
                    'title' => 'Heure de Départ du Bus',
                    'body' => 'Votre :ride_mode part à :time. Suivez la localisation du bus en temps réel.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Bus à l\'Arrêt',
                    'body' => 'Le bus est arrivé à l\'arrêt désigné pour :ride_mode (:names).',
                ],
                'passenger' => [
                    'title' => 'Bus Arrivé',
                    'body' => 'Votre bus est maintenant à l\'arrêt. Veuillez monter rapidement.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => 'Service :ride_mode Annulé',
                    'body' => 'Le service :ride_mode a été annulé pour :names. Informez immédiatement les passagers.',
                ],
                'passenger' => [
                    'title' => 'Service Annulé',
                    'body' => 'Votre :ride_mode a été annulé. Des arrangements alternatifs seront communiqués sous peu.',
                ],
            ],
            'departed' => [
                'responsible' => [
                    'title' => 'Bus Parti',
                    'body' => 'Le bus a quitté l\'arrêt pour :ride_mode (:names). Prochain arrêt en route.',
                ],
                'passenger' => [
                    'title' => 'Bus Parti',
                    'body' => 'Le bus a quitté votre arrêt et se dirige vers la prochaine destination.',
                ],
            ],
            'delayed' => [
                'responsible' => [
                    'title' => ':ride_mode en Retard',
                    'body' => 'Bus retardé pour :ride_mode (:names). Nouvelle heure d\'arrivée estimée: :time.',
                ],
                'passenger' => [
                    'title' => 'Avis de Retard',
                    'body' => 'Votre bus est en retard. Nouvelle heure d\'arrivée: environ :time.',
                ],
            ],
            'skipped' => [
                'responsible' => [
                    'title' => 'Arrêt Contourné',
                    'body' => 'L\'arrêt :ride_mode a été contourné pour :names. Vérifiez les protocoles de routage.',
                ],
                'passenger' => [
                    'title' => 'Arrêt Manqué',
                    'body' => 'Votre arrêt a été contourné. Veuillez vérifier l\'itinéraire mis à jour ou contacter le support.',
                ],
            ],
            'ignored' => [
                'responsible' => [
                    'title' => 'Arrêt Non Desservi',
                    'body' => 'L\'arrêt :ride_mode n\'a pas été desservi pour :names. Investigation requise.',
                ],
                'passenger' => [
                    'title' => 'Service Indisponible',
                    'body' => 'Le service à votre arrêt était indisponible. Veuillez consulter l\'horaire mis à jour.',
                ],
            ],
            'no_show' => [
                'responsible' => [
                    'title' => 'Passager Absent',
                    'body' => ':names n\'a pas embarqué dans :ride_mode à l\'arrêt prévu. Suivi requis.',
                ],
                'passenger' => [
                    'title' => 'Embarquement Manqué',
                    'body' => 'Vous avez manqué le départ de votre :ride_mode. Contactez le support pour une nouvelle réservation.',
                ],
            ],
        ],
    ],
    'incident' => [
        'mechanical_issue' => [
            'title' => 'Alerte Panne Véhicule',
            'body' => 'Problème mécanique signalé à :time. Évaluation technique immédiate requise.',
        ],
        'accident' => [
            'title' => 'Rapport d\'Accident',
            'body' => 'Accident de circulation survenu à :time. Protocole d\'urgence activé - évaluez et répondez.',
        ],
        'health_emergency' => [
            'title' => 'Urgence Médicale',
            'body' => 'Urgence sanitaire signalée à :time. Services d\'urgence notifiés - fournissez une assistance immédiate.',
        ],
        'behavioral_issue' => [
            'title' => 'Problème de Conduite Passager',
            'body' => 'Incident comportemental signalé à :time. Assurez la sécurité des passagers et désamorcez la situation.',
        ],
        'traffic_delay' => [
            'title' => 'Alerte Embouteillage',
            'body' => 'Retards de circulation importants affectant les services à :time. Considérez des itinéraires alternatifs.',
        ],
        'weather_condition' => [
            'title' => 'Avis Météorologique',
            'body' => 'Conditions météorologiques défavorables impactant les opérations à :time. Implémentez les protocoles de sécurité.',
        ],
        'road_blockage' => [
            'title' => 'Obstruction de Route',
            'body' => 'Blocage de route signalé à :time. Déviation d\'itinéraire immédiate requise.',
        ],
        'missed_station' => [
            'title' => 'Alerte Station Contournée',
            'body' => 'Station programmée manquée à :time. Révisez l\'adhérence à l\'itinéraire et notifiez les passagers affectés.',
        ],
        'other' => [
            'title' => 'Rapport d\'Incident',
            'body' => 'Incident opérationnel survenu à :time. Investigation et réponse requises.',
        ],
    ],
    'auth' => [
        'otp' => [
            'title' => 'Code d\'Authentification',
            'body' => 'Votre code de vérification est :otp. Valide pendant 10 minutes.',
        ],
    ],
];
