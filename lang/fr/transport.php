<?php

return [
    'name' => 'transport',
    'passenger' => [
        'details' => 'détails du passager',
        'name' => 'passager',
        'names' => 'passagers',
        'new' => 'nouveau passager',
        'add' => 'ajouter des passagers',
        'map' => 'Carte des Passagers',
        'selected' => '[0,1] :count passager sélectionné|[2,*] :count passagers sélectionnés',
        'no_passengers_found' => 'Aucun passager trouvé',
        'no_passengers_match_filters' => 'Aucun passager ne correspond à vos filtres actuels',
    ],
    'attendee' => [
        'details' => 'détails de l\'attendant',
        'name' => 'attendant',
        'names' => 'attendants',
    ],
    'group' => [
        'name' => 'groupe',
        'names' => 'groupes',
        'new' => 'nouveau groupe',
    ],
    'region' => [
        'new' => 'nouvelle région',
        'name' => 'région',
        'names' => 'régions',
    ],
    'responsible' => [
        'details' => 'détails du responsable',
        'name' => 'responsable',
        'names' => 'responsables',
        'new' => 'nouveau responsable',
    ],
    'ride' => [
        'details' => 'détails de la tour',
        'name' => 'tour',
        'names' => 'tours',
        'modes' => [
            'pickup' => 'ramassage',
            'drop_off' => 'dépose',
        ],
        'start_of_ride' => 'Début de la tour',
        'end_of_ride' => 'Fin de la tour',
        'ongoing' => 'En cours',
        'next_5_days' => 'Prochains 5 jours',
    ],
    'rides' => [
        'labels' => [
            'ride_id' => 'ID de la tour',
            'start_time' => 'heure de début',
            'distance' => 'Distance',
            'duration' => 'Durée',
            'minutes' => 'minutes',
            'start_time' => 'Heure de début',
            'average_speed' => 'Vitesse moyenne',
            'kmh' => 'km/h',
        ],
    ],
    'route' => [
        'destination' => [
            'name' => 'destination',
        ],
        'details' => 'détails de la route',
        'labels' => [
            'static' => "désactiver l'optimisation de la séquence",
            'starting_location' => 'lieu de départ',
            'destination' => 'lieu de destination',
            'duration' => 'durée',
            'distance' => 'distance',
            'km' => 'km',
        ],
        'select_location' => 'sélectionner un lieu',
        'select_vehicles' => 'sélectionner des véhicules',
        'select_drivers' => 'sélectionner des conducteurs',
        'start_of_route' => 'Début de la route',
        'end_of_route' => 'Fin de la route',
        'name' => 'route',
        'names' => 'routes',
        'new' => 'nouvel route',
        'no_routes_found' => 'Aucune route trouvée',
        'no_routes_match_filters' => 'Aucune route ne correspond à vos filtres actuels',
        'all_routes_hidden' => 'Toutes les routes sont masquées',
        'click_eye_to_show' => 'Cliquez sur l\'icône de l\'oeil sur une carte de route pour la rendre visible sur la carte',
        'show_all_routes' => 'Afficher toutes les routes',
        'optimize' => 'optimiser',
        'origin' => [
            'name' => 'origine',
        ],
    ],
    'seat' => [
        'name' => 'siège',
        'names' => 'sièges',
    ],
    'settings' => [
        'pages' => [
            'regions' => 'régions',
            'vehicles' => 'véhicules',
        ],
    ],
    'station' => [
        'add' => 'ajouter une station',
        'details' => 'détails de la station',
        'name' => 'station',
        'names' => 'stations',
    ],
    'time_info' => [
        'arrived_and_departed' => 'Arrivé à :arrived_at, parti à :departed_at',
        'arrived_waiting_departure' => 'Arrivé à :arrived_at, en attente de départ',
        'started_waiting_arrival' => 'Débuté à :started_at, en attente d\'arrivée',
        'started_and_arrived' => 'Débuté à :started_at, arrivé à :arrived_at',
        'expected_arrival' => 'Arrivée prévue à :scheduled_at',
        'waiting_schedule' => 'En attente du programme d\'arrivée',
    ],
];
