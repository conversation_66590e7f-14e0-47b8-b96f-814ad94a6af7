<?php

namespace Tests\Feature\API\Transport;

use App\Enums\General\Models;
use App\Enums\Incident\Reason;
use App\Enums\Transport\RideStatus;
use App\Models\Fleet\Driver;
use App\Models\Plan\Plan;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class RideApiTest extends TestCase
{
    use RefreshDatabase;

    protected Driver $driver;

    protected Plan $plan;

    protected Schedule $schedule;

    protected Ride $ride;

    protected Passenger $passenger;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
        Event::fake();

        $this->driver = Driver::factory()->create();
        $this->plan = Plan::factory()->create([
            'driver_id' => $this->driver->id,
        ]);
        $this->schedule = Schedule::factory()->create([
            'plan_id' => $this->plan->id,
        ]);
        $this->ride = Ride::factory()->create([
            'schedule_id' => $this->schedule->id,
            'status' => RideStatus::Ongoing,
            'started_at' => now(),
        ]);
        $this->passenger = Passenger::factory()->create();
    }

    public function test_list_rides()
    {
        $response = $this->actingAs($this->driver)
            ->getJson(route('api.transport.rides.list', [
                'user_id' => $this->driver->id,
                'user_type' => Models::Driver,
                'day' => today()->format('Y-m-d'),
            ]));

        $response->assertOk();
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'status',
                        'distance',
                        'mode',
                        'duration',
                        'started_at',
                        'arrived_at',
                        'company_id',
                        'route_name',
                        'driver' => [
                            'name',
                            'image',
                        ],
                    ],
                ],
            ]);
    }

    public function test_get_active_ride()
    {
        $response = $this->actingAs($this->passenger)
            ->getJson(route('api.transport.rides.ongoing.get', [
                'user_id' => $this->passenger->id,
                'user_type' => Models::Passenger,
            ]));

        $response->assertNoContent();
    }

    public function test_resume_ride()
    {
        $response = $this->actingAs($this->driver)
            ->postJson(route('api.transport.rides.start'), [
                'schedule_id' => $this->schedule->id,
                'start_lat' => fake()->latitude(25.194453, 25.206731),
                'start_lng' => fake()->longitude(55.244370, 55.259432),
                'scheduled_at' => now()->addMinutes(30),
                'distance' => 334,
                'duration' => 40 * 60, // 40 min
                'stations' => $this->ride->route->stations()->limit(5)->pluck('id')->toArray(),
            ]);

        $response->assertSessionHasNoErrors()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'route',
                ],
            ])
            ->assertJsonPath('data.status', RideStatus::Ongoing)
            ->assertJsonPath('data.id', $this->ride->id);
    }

    public function test_start_ride()
    {
        $this->ride->status = RideStatus::Arrived;
        $this->ride->save();

        $response = $this->actingAs($this->driver)
            ->postJson(route('api.transport.rides.start'), [
                'schedule_id' => $this->schedule->id,
                'start_lat' => fake()->latitude(25.194453, 25.206731),
                'start_lng' => fake()->longitude(55.244370, 55.259432),
                'scheduled_at' => now()->addMinutes(30),
                'distance' => 334,
                'duration' => 40 * 60, // 40 min
                'stations' => $this->ride->route->stations()->limit(5)->pluck('id')->toArray(),
            ]);

        $response->assertSessionHasNoErrors()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'route',
                ],
            ])
            ->assertJsonPath('data.status', RideStatus::Ongoing);

        $responseData = $response->json('data');
        $this->assertNotEquals($this->ride->id, $responseData['id']);
    }

    public function test_cancel_ride()
    {
        $this->ride->status = RideStatus::Ongoing;
        $this->ride->save();

        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.cancel', $this->ride->id), [
                'end_lat' => fake()->latitude(25.194453, 25.206731),
                'end_lng' => fake()->longitude(55.244370, 55.259432),
                'distance' => rand(1000, 100000),
                'duration' => rand(1000, 100000),
                'reason' => Reason::getRandomValue(),
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                ],
            ])
            ->assertJsonPath('data.status', RideStatus::Canceled);
    }

    public function test_arrive_ride()
    {
        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.arrive', $this->ride->id), [
                'end_lat' => fake()->latitude(25.194453, 25.206731),
                'end_lng' => fake()->longitude(55.244370, 55.259432),
                'distance' => rand(10, 50),
                'duration' => rand(1000, 10000),
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                ],
            ])
            ->assertJsonPath('data.status', RideStatus::Arrived);
    }
}
