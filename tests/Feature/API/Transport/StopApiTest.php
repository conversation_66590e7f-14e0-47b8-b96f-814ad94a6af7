<?php

namespace Tests\Feature\API\Transport;

use App\Enums\Incident\Reason;
use App\Enums\Transport\RideStatus;
use App\Enums\Transport\StopStatus;
use App\Models\Fleet\Driver;
use App\Models\Plan\Plan;
use App\Models\Plan\Schedule;
use App\Models\Transport\Ride;
use App\Models\Transport\Station;
use App\Models\Transport\Stop;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class StopApiTest extends TestCase
{
    use RefreshDatabase;

    protected Driver $driver;

    protected Plan $plan;

    protected Schedule $schedule;

    protected Ride $ride;

    protected Station $station;

    protected Stop $stop;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
        Event::fake();

        $this->driver = Driver::factory()->create();
        $this->plan = Plan::factory()->create([
            'driver_id' => $this->driver->id,
        ]);
        $this->schedule = Schedule::factory()->create([
            'plan_id' => $this->plan->id,
        ]);
        $this->ride = Ride::factory()->create([
            'schedule_id' => $this->schedule->id,
            'status' => RideStatus::Ongoing,
        ]);
        $this->station = $this->ride->route->stations()->inRandomOrder()->first();
        $this->stop = Stop::factory()->create([
            'station_id' => $this->station->id,
            'ride_id' => $this->ride->id,
        ]);
    }

    public function test_schedule_stop()
    {
        $scheduledAt = Carbon::make($this->ride->started_at)->addMinutes(10);
        $response = $this->actingAs($this->driver)
            ->postJson(route('api.transport.rides.stops.schedule'), [
                'station_id' => $this->station->id,
                'ride_id' => $this->ride->id,
                'scheduled_at' => $scheduledAt,
            ]);

        $response->assertSessionHasNoErrors()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'station_id',
                    'scheduled_at',
                ],
            ])
            ->assertJsonPath('data.status', StopStatus::Scheduled)
            ->assertJsonPath('data.station_id', $this->station->id)
            ->assertJsonPath('data.scheduled_at', $scheduledAt->toISOString());
    }

    public function test_skip_station()
    {
        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.stops.skip'), [
                'reason' => Reason::getRandomValue(),
                'station_id' => $this->station->id,
                'ride_id' => $this->ride->id,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'station_id',
                    'ride_id',
                    'scheduled_at',
                ],
            ])
            ->assertJsonPath('data.status', StopStatus::Skipped)
            ->assertJsonPath('data.station_id', $this->station->id);
    }

    public function test_arrive_stop()
    {
        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.stops.arrive', $this->stop->id), [
                'reason' => Reason::getRandomValue(),
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'station_id',
                    'ride_id',
                    'scheduled_at',
                ],
            ])
            ->assertJsonPath('data.status', StopStatus::Arrived)
            ->assertJsonPath('data.station_id', $this->station->id);
    }

    public function test_depart_stop()
    {
        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.stops.depart', $this->stop->id), [
                'attendants' => [
                    1, 2,
                ],
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'station_id',
                    'ride_id',
                    'scheduled_at',
                    'departed_at',
                ],
            ])
            ->assertJsonPath('data.status', StopStatus::Departed)
            ->assertJsonPath('data.station_id', $this->station->id);
    }

    public function test_delay_stop()
    {
        $newScheduledTime = Carbon::make($this->stop->scheduled_at)->addMinutes(10);

        $response = $this->actingAs($this->driver)
            ->putJson(route('api.transport.rides.stops.delay', $this->stop->id), [
                'new_scheduled_time' => $newScheduledTime,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'station_id',
                    'ride_id',
                    'scheduled_at',
                    'departed_at',
                ],
            ])
            ->assertJsonPath('data.station_id', $this->station->id);
    }
}
