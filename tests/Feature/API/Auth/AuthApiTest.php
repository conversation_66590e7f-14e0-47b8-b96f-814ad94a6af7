<?php

namespace Tests\Feature\API\Auth;

use App\Enums\General\MobileAppPackage;
use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Notifications\Auth\VerifyOtpNotification;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class AuthApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public static function userTypeProvider(): array
    {
        return [
            'Passenger' => [Passenger::class],
            'Assistant' => [Assistant::class],
            'Driver' => [Driver::class],
            'Responsible' => [Responsible::class],
            'Admin' => [User::class],
        ];
    }

    protected function getPackageName(Authenticatable $user): string
    {
        return $user->isDriver() ? MobileAppPackage::YosrDriver : MobileAppPackage::Yosr;
    }

    #[DataProvider('userTypeProvider')]
    public function test_send_otp(string $userClass)
    {
        $user = $userClass::factory()->create();

        $response = $this->postJson(route('api.auth.otp.send'), [
            'phone' => $user->phone,
            'package' => $this->getPackageName($user),
        ]);

        $response->assertOk();
        Notification::assertSentTo($user, VerifyOtpNotification::class);
    }

    #[DataProvider('userTypeProvider')]
    public function test_verify_otp(string $userClass)
    {
        $user = $userClass::factory()->create();

        $response = $this->postJson(route('api.auth.otp.verify'), [
            'phone' => $user->phone,
            'otp' => '0000',
            'package' => $this->getPackageName($user),
        ]);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'token',
                'user',
            ],
        ]);
    }

    #[DataProvider('userTypeProvider')]
    public function test_get_auth_user(string $userClass)
    {
        $user = $userClass::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('api.auth.user'));

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'image',
                'company' => [
                    'location' => [
                        'lng',
                        'lat',
                    ],
                ],
            ],
        ]);

        $response->assertJsonPath('data.id', $user->id);
        $response->assertJsonPath('data.name', $user->name);
        $response->assertJsonPath('data.company.location.lng', $user->company->location->lng);
        $response->assertJsonPath('data.company.location.lat', $user->company->location->lat);
    }
}
