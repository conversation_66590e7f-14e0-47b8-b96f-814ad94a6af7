<?php

namespace Tests\Feature\API\Plan;

use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Plan\Plan;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Models\Transport\Ride;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class ScheduleApiTest extends TestCase
{
    use RefreshDatabase;

    protected Driver $driver;

    protected Plan $plan;

    protected Assistant $assistant;

    protected Responsible $responsible;

    protected Passenger $passenger;

    protected Schedule $schedule;

    protected Ride $ride;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
        Event::fake();

        $this->assistant = Assistant::factory()->create();
        $this->user = User::factory()->create();
        $this->driver = Driver::factory()->create();
        $this->plan = Plan::factory()->create([
            'driver_id' => $this->driver->id,
        ]);
        $this->schedule = Schedule::factory()->create([
            'plan_id' => $this->plan->id,
        ]);
        $this->passenger = Passenger::factory()->create();
        $this->responsible = Responsible::factory()->create();
        $this->responsible->passengers()->attach($this->passenger, [
            'created_at' => now(),
            'company_id' => $this->passenger->company_id,
        ]);

        $this->schedule->route->stations->first()->passengers()->attach($this->passenger, [
            'route_id' => $this->schedule->route_id,
            'company_id' => $this->passenger->company_id,
            'created_at' => now(),
        ]);
    }

    public static function userTypeProvider(): array
    {
        return [
            'passenger' => ['passenger', 'passenger'],
            'responsible' => ['responsible', 'passenger'],
            'admin' => ['user', 'driver'],
            'assistant' => ['assistant', 'assistant'],
            'driver' => ['driver', 'driver'],
        ];
    }

    #[DataProvider('userTypeProvider')]
    public function test_list_schedules(string $actorType, string $subjectUserType)
    {
        $actor = $this->{$actorType};
        $subjectUser = $this->{$subjectUserType};

        $response = $this->actingAs($actor)
            ->getJson(route('api.plan.schedules.list', [
                'user_id' => $subjectUser->id,
                'user_type' => $subjectUser->type(),
                'day' => today()->toString(),
            ]));

        if ($response->status() !== 200) {
            dd($response->getContent());
        }
        $response->assertOk();
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'plan_id',
                        'route_id',
                        'date',
                        'arrival_time',
                        'route',
                    ],
                ],
            ]);
    }

    #[DataProvider('userTypeProvider')]
    public function test_get_schedule(string $actorType, string $subjectUserType)
    {
        $actor = $this->{$actorType};
        $subjectUser = $this->{$subjectUserType};

        $response = $this->actingAs($actor)
            ->getJson(route('api.plan.schedules.get', [
                'schedule' => $this->schedule->id,
                'user_id' => $subjectUser->id,
                'user_type' => $subjectUser->type(),
                'day' => today()->toString(),
            ]));

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'plan_id',
                'route_id',
                'date',
                'arrival_time',
                'route',
            ],
        ]);
    }
}
