<?php

use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

// Schedule::call(new ScheduleReminder(now()))->everyMinute();

// Schedule::command('backup:run --only-db')->everySixHours()->environments('production');

// Schedule::command('backup:clean')->daily()->environments('production');

Schedule::command('ride:mark-stall')->hourly()->environments('production');
