<?php

use App\Http\Controllers\Transport\PassengerController;
use App\Http\Controllers\Transport\ResponsibleController;
use App\Http\Controllers\Transport\RideController;
use App\Http\Controllers\Transport\RouteController;
use App\Http\Controllers\Transport\StationController;
use Illuminate\Support\Facades\Route;

Route::prefix('routes')->name('routes.')->group(function () {
    Route::get('/', [RouteController::class, 'list'])->name('list');
    Route::get('/{route}', [RouteController::class, 'view'])->name('view');
    Route::post('/', [RouteController::class, 'save'])->name('save');
    Route::put('/{route}', [RouteController::class, 'update'])->name('update');
    Route::put('/{route}/optimize', [RouteController::class, 'optimize'])->name('optimize');
    Route::delete('/{route}', [RouteController::class, 'delete'])->name('delete');
    Route::get('/{route}/stations', [RouteController::class, 'viewStations'])->name('stations.view');
    Route::get('/{route}/map', [RouteController::class, 'viewMap'])->name('map.view');
    Route::prefix('{route}/stations')->name('stations.')->group(function () {
        Route::post('/', [StationController::class, 'save'])->name('save');
        Route::put('/{station}', [StationController::class, 'update'])->name('update');
        Route::put('/', [StationController::class, 'updateOrder'])->name('order.update');
        Route::delete('/{station}', [StationController::class, 'delete'])->name('delete');
    });
});

Route::prefix('rides')->name('rides.')->group(function () {
    Route::get('/', [RideController::class, 'list'])->name('list');
    Route::get('/calendar', [RideController::class, 'calendar'])->name('calendar');
    Route::get('/{ride}', [RideController::class, 'view'])->name('view');
    Route::get('/{ride}/passengers', [RideController::class, 'passengers'])->name('passengers.view');
    Route::get('/{ride}/attendees', [RideController::class, 'attendees'])->name('attendees.view');
    Route::get('/{ride}/map', [RideController::class, 'map'])->name('map.view');
    Route::get('/{ride}/timeline', [RideController::class, 'timeline'])->name('timeline.view');
    Route::get('/{ride}/reviews', [RideController::class, 'reviews'])->name('reviews.view');
    Route::get('/{ride}/incidents', [RideController::class, 'incidents'])->name('incidents.view');
});

Route::prefix('passengers')->name('passengers.')->group(function () {
    Route::get('/', [PassengerController::class, 'list'])->name('list');
    Route::get('/{passenger}', [PassengerController::class, 'view'])->name('view');
    Route::get('/{passenger}/locations', [PassengerController::class, 'viewLocations'])->name('locations.view');
    Route::get('/{passenger}/responsibles', [PassengerController::class, 'viewResponsibles'])->name('responsibles.view');
    Route::post('/', [PassengerController::class, 'save'])->name('save');
    Route::post('/{passenger}', [PassengerController::class, 'update'])->name('update');
    Route::delete('/{passenger}', [PassengerController::class, 'delete'])->name('delete');
});

Route::prefix('responsibles')->name('responsibles.')->group(function () {
    Route::get('/', [ResponsibleController::class, 'list'])->name('list');
    Route::get('/{responsible}', [ResponsibleController::class, 'view'])->name('view');
    Route::get('/{responsible}/passengers', [ResponsibleController::class, 'viewPassengers'])->name('passengers.view');
    Route::post('/{responsible}/passengers', [ResponsibleController::class, 'addPassengers'])->name('passengers.add');
    Route::delete('/{responsible}/passengers/{passenger}', [ResponsibleController::class, 'removePassenger'])->name('passenger.remove');
    Route::post('/', [ResponsibleController::class, 'save'])->name('save');
    Route::post('/{responsible}', [ResponsibleController::class, 'update'])->name('update');
    Route::delete('/{responsible}', [ResponsibleController::class, 'delete'])->name('delete');
});
