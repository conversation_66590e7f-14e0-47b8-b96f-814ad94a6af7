<?php

namespace Database\Factories\Transport;

use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\Plan\Schedule;
use App\Models\Transport\Ride;
use App\Models\Transport\Route;
use App\Models\Transport\Stop;
use Illuminate\Database\Eloquent\Factories\Factory;

class RideFactory extends Factory
{
    protected $model = Ride::class;

    public function definition(): array
    {
        $startedAt = fake()->dateTimeBetween('-1 month');
        $arrivedAt = (clone $startedAt)->modify('+'.mt_rand(15, 90).' minutes');
        $scheduledAt = (clone $arrivedAt)->modify('+'.mt_rand(0, 10).' minutes');

        return [
            'status' => fake()->randomElement([
                RideStatus::Arrived,
                RideStatus::Canceled,
            ]),
            'note' => fake()->word,
            'mode' => fake()->randomElement(RideMode::asArray()),
            'start_lat' => fake()->latitude(25.194453, 25.206731),
            'start_lng' => fake()->longitude(55.244370, 55.259432),
            'end_lat' => fake()->latitude(25.194453, 25.206731),
            'end_lng' => fake()->longitude(55.244370, 55.259432),
            'distance' => fake()->numberBetween(5000, 100000),
            'started_at' => $startedAt,
            'arrived_at' => $arrivedAt,
            'scheduled_at' => $scheduledAt,
            'vehicle_id' => Vehicle::inRandomOrder()->first()?->id ?? Vehicle::factory(),
            'route_id' => Route::where('id', '!=', 1)
                ->inRandomOrder()
                ->first()?->id ?? Route::factory(),
            'driver_id' => Driver::inRandomOrder()->first()?->id ?? Driver::factory(),
            'assistant_id' => Assistant::inRandomOrder()->first()?->id,
            'schedule_id' => Schedule::factory(),
            'company_id' => 1,
        ];
    }

    public function configure(): RideFactory
    {
        return $this->afterCreating(function (Ride $ride) {
            foreach ($ride->route->stations as $station) {
                Stop::factory()->create([
                    'ride_id' => $ride->id,
                    'station_id' => $station->id,
                ]);
            }
        });
    }
}
