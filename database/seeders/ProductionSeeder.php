<?php

namespace Database\Seeders;

use App\Enums\Auth\UserStatus;
use App\Enums\General\Locale;
use App\Enums\General\Models;
use App\Models\Auth\User;
use App\Models\Company\Company;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\General\Region;
use App\Models\Geo\Location;
use App\Models\Transport\Group;
use App\Models\Transport\Passenger;
use App\Models\Transport\PassengerResponsible;
use App\Models\Transport\Responsible;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Models\Transport\StationPassenger;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ProductionSeeder extends Seeder
{
    private int $phoneCounter = *********; // Start with a phone number base for 9 digits

    private int $emailCounter = 100;

    public function run(): void
    {
        $company = Company::factory()->create([
            'name' => 'Yosr',
            'locale' => Locale::English,
        ]);

        // Create Super Admin
        $this->createSuperAdmin($company->id);

        // Create demo data for the company
        $this->createCompanyData($company->id);
    }

    private function createSuperAdmin(int $companyId): void
    {
        User::factory()->create([
            'first_name' => 'Super',
            'last_name' => 'Admin 1',
            'email' => '<EMAIL>',
            'status' => UserStatus::Active,
            'password' => Hash::make('G7^mP@3dLz*9vT!k'),
            'phone' => '212612345678',
            'company_id' => $companyId,
        ]);
    }

    private function createCompanyData(int $companyId): void
    {
        // Create Casablanca region
        $casablancaRegion = Region::factory()->create([
            'name' => 'Casablanca-Settat',
            'company_id' => $companyId,
        ]);

        // Create school levels (groups)
        $groups = $this->createSchoolLevels($companyId);

        // Create exactly 150 responsibles
        $responsibles = $this->createResponsibles($companyId, 150);

        // Create 200 passengers
        $passengers = $this->createPassengers($companyId, 200, $casablancaRegion->id, $groups);

        // Assign responsibles to passengers
        $this->assignPassengersToResponsibles($responsibles, $passengers);

        // Create vehicles and drivers after passengers to ensure capacity
        $this->createVehiclesAndDrivers($companyId, $passengers);

        // Create routes and stations
        $this->createRoutesAndStations($companyId, $passengers);
    }

    private function createVehiclesAndDrivers(int $companyId, array $passengers): void
    {
        $totalPassengers = count($passengers);
        $desiredCapacityPerVehicle = 30; // Adjust as needed
        $numberOfVehicles = ceil($totalPassengers / $desiredCapacityPerVehicle);

        for ($i = 1; $i <= $numberOfVehicles; $i++) {
            // Create vehicle
            $vehicle = Vehicle::factory()->create([
                'name' => 'Véhicule '.$i,
                'plate' => 'A-'.rand(10000, 99999),
                'capacity' => $desiredCapacityPerVehicle, // Assuming 'capacity' is a column in your Vehicle model
                'company_id' => $companyId,
            ]);

            // Create driver for the vehicle
            $this->createDriver($companyId, $i);
        }
    }

    private function createSchoolLevels(int $companyId): array
    {
        $levels = ['Primaire', 'Collège', 'Lycée'];
        $groups = [];

        foreach ($levels as $level) {
            $groups[] = Group::factory()->create([
                'name' => $level,
                'company_id' => $companyId,
            ]);
        }

        return $groups;
    }

    private function createDriver(int $companyId, int $index): Driver
    {
        $moroccanNames = $this->getMoroccanNames();
        $name = $moroccanNames[array_rand($moroccanNames)];

        return Driver::factory()->create([
            'first_name' => $name['first_name'],
            'last_name' => $name['last_name'],
            'email' => $this->generateUniqueEmail('chauffeur'),
            'phone' => $this->generateUniquePhone(),
            'company_id' => $companyId,
        ]);
    }

    private function createResponsibles(int $companyId, int $count): array
    {
        $responsibles = [];
        for ($i = 1; $i <= $count; $i++) {
            $moroccanNames = $this->getMoroccanNames();
            $name = $moroccanNames[array_rand($moroccanNames)];

            $responsibles[] = Responsible::factory()->create([
                'first_name' => $name['first_name'],
                'last_name' => $name['last_name'],
                'email' => $this->generateUniqueEmail('parent'),
                'phone' => $this->generateUniquePhone(),
                'company_id' => $companyId,
            ]);
        }

        return $responsibles;
    }

    private function createPassengers(int $companyId, int $count, int $regionId, array $groups): array
    {
        $passengers = [];
        for ($i = 1; $i <= $count; $i++) {
            $moroccanNames = $this->getMoroccanNames();
            $name = $moroccanNames[array_rand($moroccanNames)];

            $passenger = Passenger::create([
                'first_name' => $name['first_name'],
                'last_name' => $name['last_name'],
                'email' => $this->generateUniqueEmail('eleve'),
                'phone' => $this->generateUniquePhone(),
                'region_id' => $regionId,
                'group_id' => $this->assignGroup($i, $groups),
                'company_id' => $companyId,
            ]);

            $this->createPassengerLocation($passenger, $companyId);
            $passengers[] = $passenger;
        }

        return $passengers;
    }

    private function createPassengerLocation(Passenger $passenger, int $companyId): void
    {
        $streetAddress = $this->getRandomCasablancaAddress();

        Location::factory()->create([
            'name' => 'Adresse de '.$passenger->first_name.' '.$passenger->last_name,
            'street_line_1' => $streetAddress,
            'city' => 'Casablanca',
            'state' => 'Casablanca-Settat',
            'postal_code' => '20000',
            'lat' => fake()->latitude(25.194453, 25.206731),
            'lng' => fake()->longitude(55.244370, 55.259432),
            'primary' => true,
            'model_type' => Models::Passenger,
            'model_id' => $passenger->id,
            'creator_type' => Models::User,
            'creator_id' => 1,
            'company_id' => $companyId,
        ]);
    }

    private function assignPassengersToResponsibles(array $responsibles, array $passengers): void
    {
        foreach ($passengers as $index => $passenger) {
            $responsibleIndex = $index % count($responsibles);
            PassengerResponsible::factory()->create([
                'passenger_id' => $passenger->id,
                'responsible_id' => $responsibles[$responsibleIndex]->id,
            ]);
        }
    }

    private function createRoutesAndStations(int $companyId, array $passengers): void
    {
        // Load all vehicles and their capacities
        $vehicles = Vehicle::where('company_id', $companyId)->get();

        // Distribute passengers based on vehicle capacities
        $passengerGroups = [];
        $currentPassengerIndex = 0;

        foreach ($vehicles as $vehicle) {
            $vehicleCapacity = $vehicle->capacity;
            $passengerGroups[] = array_slice($passengers, $currentPassengerIndex, $vehicleCapacity);
            $currentPassengerIndex += $vehicleCapacity;

            if ($currentPassengerIndex >= count($passengers)) {
                break; // Stop if we've assigned all passengers
            }
        }

        // Create routes for each group of passengers
        foreach ($passengerGroups as $index => $passengerGroup) {
            if (empty($passengerGroup)) {
                continue; // Skip empty groups
            }

            // Create route with unique origin and destination
            $route = Route::factory()->create([
                'name' => 'Circuit '.($index + 1),
                'reference' => Str::random(6),
                'company_id' => $companyId,
                'origin_id' => $this->createDefaultLocation($companyId, 'Origin '.($index + 1)),
                'destination_id' => $this->createDefaultLocation($companyId, 'Destination '.($index + 1)),
            ]);

            // Limit the number of stations to the number of passengers in the group
            $passengerCount = count($passengerGroup);
            $stationsToCreate = min($passengerCount, 1); // Create at least one station per group

            // Load locations for passengers in the group
            $passengerIds = array_map(fn ($passenger) => $passenger->id, $passengerGroup);
            $passengersWithLocations = Passenger::with('location')->whereIn('id', $passengerIds)->get();

            foreach ($passengersWithLocations->take($stationsToCreate) as $order => $passenger) {
                if ($passenger->location) { // Check if location exists
                    $station = Station::factory()->create([
                        'route_id' => $route->id,
                        'location_id' => $passenger->location->id,
                        'order' => $order + 1,
                        'company_id' => $companyId,
                    ]);

                    $existingRecord = StationPassenger::where('station_id', $station->id)
                        ->where('passenger_id', $passenger->id)
                        ->first();

                    if (! $existingRecord) {
                        StationPassenger::factory()->create([
                            'station_id' => $station->id,
                            'passenger_id' => $passenger->id,
                            'route_id' => $route->id,
                            'company_id' => $companyId,
                        ]);
                    }
                }
            }
        }
    }

    private function createDefaultLocation(int $companyId, string $name): int
    {
        return Location::factory()->create([
            'name' => $name,
            'street_line_1' => $this->getRandomCasablancaAddress(),
            'city' => 'Casablanca',
            'state' => 'Casablanca-Settat',
            'postal_code' => '20000',
            'lat' => fake()->latitude(25.194453, 25.206731),
            'lng' => fake()->longitude(55.244370, 55.259432),
            'primary' => true,
            'model_type' => Models::Route,
            'model_id' => null, // This will be updated when the route is created
            'creator_type' => Models::User,
            'creator_id' => 1,
            'company_id' => $companyId,
        ])->id;
    }

    private function assignGroup(int $index, array $groups): int
    {
        if ($index <= 50) {
            return $groups[0]->id; // Primaire
        } elseif ($index <= 150) {
            return $groups[1]->id; // Collège
        } else {
            return $groups[2]->id; // Lycée
        }
    }

    private function generateUniquePhone(): string
    {
        return '212'.$this->phoneCounter++;
    }

    private function generateUniqueEmail(string $prefix): string
    {
        return $prefix.$this->emailCounter++.'@yosr.ma';
    }

    private function getRandomCasablancaAddress(): string
    {
        $addresses = [
            'Boulevard Mohammed V, Casablanca 20250',
            'Rue d\'Agadir, Quartier Gauthier, Casablanca 20060',
            'Avenue Hassan II, Casablanca 20100',
            'Rue Jean Jaurès, Quartier Gauthier, Casablanca 20060',
            'Boulevard Anfa, Casablanca 20050',
            'Rue Abou Al Mahassin Rouyani, Maarif, Casablanca 20330',
            'Boulevard de la Résistance, Casablanca 20300',
            'Rue Ibn Tachfine, Derb Omar, Casablanca 20250',
            'Boulevard Ghandi, Maarif, Casablanca 20100',
            'Rue Ahmed El Mokri, Quartier Bourgogne, Casablanca 20053',
        ];

        return $addresses[array_rand($addresses)];
    }

    private function getMoroccanNames(): array
    {
        return [
            ['first_name' => 'Mohammed', 'last_name' => 'El Alaoui'],
            ['first_name' => 'Fatima', 'last_name' => 'Bennis'],
            ['first_name' => 'Ahmed', 'last_name' => 'Tazi'],
            ['first_name' => 'Amina', 'last_name' => 'Bennani'],
            ['first_name' => 'Hassan', 'last_name' => 'El Fassi'],
            ['first_name' => 'Laila', 'last_name' => 'Chraibi'],
            ['first_name' => 'Youssef', 'last_name' => 'Kadiri'],
            ['first_name' => 'Nadia', 'last_name' => 'Ziani'],
            ['first_name' => 'Karim', 'last_name' => 'El Amrani'],
            ['first_name' => 'Samira', 'last_name' => 'Berrada'],
        ];
    }
}
