<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_invitations', function (Blueprint $table) {
            $table->id();
            $table->string('status');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->unique()->index();
            $table->string('region_id')->nullable();
            $table->foreignId('company_id');
            $table->string('token', 24)->unique();
            $table->foreignId('inviter_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_invitations');
    }
};
