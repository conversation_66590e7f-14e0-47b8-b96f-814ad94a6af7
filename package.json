{"private": true, "scripts": {"dev": "vite", "build": "node --max_old_space_size=2048 ./node_modules/vite/bin/vite.js build"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "array-sort": "^1.0.0", "autoprefixer": "^10.4.12", "axios": "^1.1.2", "install": "^0.13.0", "laravel-vapor": "^0.7.1", "laravel-vite-plugin": "^0.7.2", "npm": "^10.7.0", "postcss": "^8.4.18", "sass": "1.77.6", "sass-loader": "^12.4.0", "vite": "^4.0.0", "vue": "^3.2.41"}, "dependencies": {"@ably/laravel-echo": "^1.0.5", "@amplitude/analytics-browser": "^2.11.12", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/rrule": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@fullcalendar/vue3": "^6.1.11", "@inertiajs/vue3": "^2.0.0", "@sentry/vite-plugin": "^2.23.0", "@sentry/vue": "^8.48.0", "@vuepic/vue-datepicker": "^8.7.0", "ably": "^1.2.50", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.10.3", "chart.js": "^4.4.0", "dropzone": "^6.0.0-beta.2", "laravel-vue-i18n": "^2.7.7", "mapbox-gl": "^3.9.2", "mixpanel-browser": "^2.64.0", "supercluster": "^8.0.1", "sweetalert2": "^11.4.8", "vue-chartjs": "^5.2.0", "vue-echarts": "^6.7.2", "vue-final-modal": "^3.4.11", "vue-i18n": "^9.2.2", "vue-multiselect": "^3.2.0", "vue-select": "^4.0.0-beta.6", "vue-tel-input": "^9.1.4", "vue-tippy": "^6.0.0", "vue-toastification": "^2.0.0-rc.5", "vue3-click-away": "^1.2.4", "vuedraggable": "^4.1.0"}}